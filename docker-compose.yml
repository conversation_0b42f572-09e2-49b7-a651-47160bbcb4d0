services:
  api:
    image: api-image
    command: 'pnpm run start:dev:fast api'
    ports:
      - '3000:3000'
      - '9010:9229'
    environment:
      # - NODE_ENV=local
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://grafana-alloy:4318
      - OTEL_SERVICE_NAME=api-service
      - OTEL_LOG_LEVEL=debug
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=http://grafana-alloy:4317
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc

  analyser:
    image: analyser-image
    command: 'pnpm run start:dev:fast analyser'
    ports:
      - '3002:3000'
      - '9020:9229'
    environment:
      # - NODE_ENV=local
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://grafana-alloy:4318
      - OTEL_SERVICE_NAME=analyser-service
      - OTEL_LOG_LEVEL=debug
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=http://grafana-alloy:4317
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc

  oms:
    image: oms-image
    command: 'pnpm run start:dev:fast oms'
    ports:
      - '3003:3000'
      - '9030:9229'
    environment:
      # - NODE_ENV=local
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://grafana-alloy:4318
      - OTEL_SERVICE_NAME=oms-service
      - OTEL_LOG_LEVEL=debug
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=http://grafana-alloy:4317
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc

  simulator:
    image: simulator-image
    command: 'pnpm run start:dev:fast simulator'
    ports:
      - '3004:3000'
      - '9040:9229'
    environment:
      # - NODE_ENV=local
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://grafana-alloy:4318
      - OTEL_SERVICE_NAME=simulator-service
      - OTEL_LOG_LEVEL=debug
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=http://grafana-alloy:4317
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc

  ticker:
    image: ticker-image
    command: 'pnpm run start:dev:fast ticker'
    ports:
      - '3005:3000'
      - '9050:9229'
    environment:
      # - NODE_ENV=local
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://grafana-alloy:4318
      - OTEL_SERVICE_NAME=ticker-service
      - OTEL_LOG_LEVEL=debug
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=http://grafana-alloy:4317
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc
