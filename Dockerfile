# base stage to have pnpm installed
FROM node:22-bullseye-slim AS base
# FROM --platform=linux/amd64 node:22-bullseye-slim AS base
# Install a few system dependencies
RUN apt-get update
RUN apt-get install -y build-essential python3
RUN npm -g i pnpm

# development stage
FROM base AS development 
ARG NODE_ENV=development 
ENV NODE_ENV=${NODE_ENV}

# Instruct pnpm to use store directory at /pnpm
# And copy dependencies from store instead of symlinking them
# This is required as cache mount will only be available
# during build time
# it's also possible to run commands such as
#   pnpm config set store-dir /pnpm_store
#   pnpm config set package-import-method copy 
# or use .npmrc
ENV NPM_CONFIG_STORE_DIR=/pnpm
ENV NPM_CONFIG_PACKAGE_IMPORT_METHOD=copy

WORKDIR /app 
COPY package.json pnpm-lock.yaml ./ 

# Mount pnpm cache at /pnpm (matching NPM_CONFIG_STORE_DIR)
# And run pnpm install
RUN --mount=type=cache,id=pnmcache,target=/pnpm \
  pnpm i --prefer-offline --frozen-lockfile
COPY . /app
# TODO: should it be cmd instead of run?
# CMD ["pnpm", "run", "start:dev", "${APP}"]

# build stage
FROM base AS build
ARG NODE_ENV=development 
ENV NODE_ENV=${NODE_ENV}
WORKDIR /app
COPY --from=development /app/node_modules ./node_modules
COPY . /app
ARG APP
RUN pnpm build ${APP}

# production stage
FROM base AS production 

ARG NODE_ENV=production 
ENV NODE_ENV=${NODE_ENV} 
WORKDIR /app 
COPY package.json pnpm-lock.yaml ./ 
RUN --mount=type=cache,id=pnmcache,target=/pnpm \
  pnpm i --prod --prefer-offline --frozen-lockfile
COPY --from=build /app/dist ./dist 
 
# Add an env to save ARG
ARG APP 
ENV APP_MAIN_FILE=dist/apps/${APP}/main 
CMD node ${APP_MAIN_FILE}
