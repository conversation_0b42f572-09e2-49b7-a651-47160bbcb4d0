services:
  postgres:
    image: postgres:16-alpine
    environment:
      POSTGRES_DB: patterntrade
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  questdb:
    image: questdb/questdb:8.3.3 # Or a specific version, e.g., questdb/questdb:7.3.11
    ports:
      - '9000:9000' # Web Console and REST API
      - '8812:8812' # PostgreSQL protocol
      - '9009:9009' # InfluxDB Line Protocol
    volumes:
      - questdb_data:/var/lib/questdb # Mount a named volume to the QuestDB data directory

  redis:
    image: 'docker.dragonflydb.io/dragonflydb/dragonfly'
    environment:
      REDIS_ARGS: '--requirepass admin'
    ulimits:
      memlock: -1
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  questdb_data:
    driver: local
