const nodeExternals = require('webpack-node-externals');
const { RunScriptWebpackPlugin } = require('run-script-webpack-plugin');
const path = require('path');

module.exports = function (options, webpack) {
  return {
    ...options,
    entry: ['webpack/hot/poll?100', options.entry],
    devtool: 'eval-cheap-module-source-map',
    externals: [
      nodeExternals({
        allowlist: ['webpack/hot/poll?100'],
      }),
    ],
    module: {
      rules: [
        {
          test: /\.ts$/,
          exclude: /node_modules/,
          use: [
            {
              loader: 'swc-loader',
              options: {
                sync: false,
                jsc: {
                  parser: {
                    syntax: 'typescript',
                    tsx: false,
                    decorators: true,
                    dynamicImport: true,
                  },
                  transform: {
                    legacyDecorator: true,
                    decoratorMetadata: true,
                  },
                  target: 'es2020',
                  keepClassNames: true,
                  baseUrl: path.resolve(__dirname),
                  paths: {
                    '@app/*': [path.resolve(__dirname, 'libs/*/src')],
                  },
                },
                module: {
                  type: 'commonjs',
                  strict: true,
                  noInterop: false,
                },
              },
            },
          ],
        },
      ],
    },
    optimization: {
      removeAvailableModules: false,
      removeEmptyChunks: false,
      splitChunks: false,
    },
    cache: {
      type: 'filesystem',
      cacheDirectory: path.resolve(__dirname, '.temp/.webpack-cache'),
      name: 'pt-backend-cache',
      buildDependencies: {
        config: [__filename],
      },
      compression: 'gzip',
    },
    plugins: [
      ...options.plugins,
      new webpack.HotModuleReplacementPlugin(),
      new webpack.WatchIgnorePlugin({
        paths: [/\.js$/, /\.d\.ts$/],
      }),
      new RunScriptWebpackPlugin({
        name: options.output.filename,
        autoRestart: false,
        nodeArgs: ['--inspect=0.0.0.0:9229'],
      }),
    ],
  };
};
