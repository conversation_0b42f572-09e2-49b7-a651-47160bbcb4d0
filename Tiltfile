# Load deployment_create extension
# load('ext://deployment', 'deployment_create')
load('ext://restart_process', 'docker_build_with_restart')

update_settings(max_parallel_updates=1)

docker_compose(["docker-compose.yml", "docker-compose.infra.yml"])

# Define the docker build context
# this is for docker compose
docker_build(
    'api-image',
    '.',
    dockerfile='Dockerfile',
    target='development',
    build_args={'APP': 'api'},
    # ignore=['apps/analyser/**', 'apps/oms/**', 'apps/simulator/**', 'apps/ticker/**'],
    live_update=[
        sync('.', '/app'),
        run('cd /app && pnpm install', trigger=['package.json', 'pnpm-lock.yaml']),
        restart_container()
    ]
)

# docker_build(
#     'analyser-image',
#     '.',
#     dockerfile='Dockerfile',
#     target='development',
#     build_args={'APP': 'analyser'},
#     ignore=['apps/api/**', 'apps/oms/**', 'apps/simulator/**', 'apps/ticker/**'],
#     live_update=[
#         sync('.', '/app'),
#         run('cd /app && pnpm install', trigger=['package.json', 'pnpm-lock.yaml']),
#         restart_container()
#     ]
# )

# docker_build(
#     'oms-image',
#     '.',
#     dockerfile='Dockerfile',
#     target='development',
#     build_args={'APP': 'oms'},
#     ignore=['apps/api/**', 'apps/analyser/**', 'apps/simulator/**', 'apps/ticker/**'],
#     live_update=[
#         sync('.', '/app'),
#         run('cd /app && pnpm install', trigger=['package.json', 'pnpm-lock.yaml']),
#         restart_container()
#     ]
# )

# docker_build(
#     'simulator-image',
#     '.',
#     dockerfile='Dockerfile',
#     target='development',
#     build_args={'APP': 'simulator'},
#     ignore=['apps/api/**', 'apps/analyser/**', 'apps/oms/**', 'apps/ticker/**'],
#     live_update=[
#         sync('.', '/app'),
#         run('cd /app && pnpm install', trigger=['package.json', 'pnpm-lock.yaml']),
#         restart_container()
#     ]
# )

# docker_build(
#     'ticker-image',
#     '.',
#     dockerfile='Dockerfile',
#     target='development',
#     build_args={'APP': 'ticker'},
#     ignore=['apps/api/**', 'apps/analyser/**', 'apps/oms/**', 'apps/simulator/**'],
#     live_update=[
#         sync('.', '/app'),
#         run('cd /app && pnpm install', trigger=['package.json', 'pnpm-lock.yaml']),
#         restart_container()
#     ]
# )

dc_resource(
    'api',
    # port_forwards=['3000:3000', '9010:9229'],
    labels=['api'],
    resource_deps=['redis']
)

# dc_resource(
#     'analyser',
#     # port_forwards=['3000:3000', '9010:9229'],
#     labels=['analyser'],
#     resource_deps=['api']
# )

# dc_resource(
#     'oms',
#     # port_forwards=['3000:3000', '9010:9229'],
#     labels=['oms'],
#     resource_deps=['analyser']
# )

# dc_resource(
#     'simulator',
#     # port_forwards=['3000:3000', '9010:9229'],
#     labels=['simulator'],
#     resource_deps=['oms']
# )

# dc_resource(
#     'ticker',
#     # port_forwards=['3000:3000', '9010:9229'],
#     labels=['ticker'],
#     resource_deps=['simulator']
# )

dc_resource(
    'redis',
    labels=['redis']
)

dc_resource(
    'postgres',
    labels=['postgres']
)

dc_resource(
    'questdb',
    labels=['questdb']
)