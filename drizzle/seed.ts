import 'dotenv/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { UserTable } from '@app/user/user.model';
import { eq } from 'drizzle-orm';
import * as bcrypt from 'bcrypt';
import { DateTimeUtilsService } from '@app/utils';

// Database connection
const connectionString = `postgresql://${process.env.DB_USER || 'admin'}:${process.env.DB_PASSWORD || 'admin'}@${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || '5432'}/${process.env.DB_NAME || 'patterntrade'}`;

const client = postgres(connectionString);
const db = drizzle(client);

/**
 * Seed data for initial admin user creation
 * This script is idempotent and can be run multiple times safely
 */
async function seedAdminUsers() {
  console.log('🌱 Starting seed process...');

  try {
    const dateTimeUtils = new DateTimeUtilsService();
    // Default admin user data
    const adminEmail = '<EMAIL>';
    const defaultPassword = 'AdminPass123!'; // This should be changed on first login
    const currentTimestamp = dateTimeUtils.getUtcNow();

    // Check if admin user already exists
    const existingAdmin = await db
      .select()
      .from(UserTable)
      .where(eq(UserTable.email, adminEmail))
      .limit(1);

    if (existingAdmin.length > 0) {
      console.log('✅ Admin user already exists, skipping creation');
      return;
    }

    // Hash the default password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(defaultPassword, saltRounds);

    // Create admin user with explicit ID
    const [adminUser] = await db
      .insert(UserTable)
      .values({
        id: 1, // Explicit ID for admin user
        email: adminEmail,
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin',
        isActive: true,
        passwordHash,
        lastLoginAt: null,
        createdAt: currentTimestamp,
        createdBy: 'system',
        updatedAt: currentTimestamp,
        updatedBy: 'system',
        deletedAt: null,
        deletedBy: null,
      })
      .returning();

    console.log('✅ Admin user created successfully:', {
      id: adminUser.id,
      email: adminUser.email,
      role: adminUser.role,
    });

    console.log('⚠️  IMPORTANT: Default password is "AdminPass123!" - Please change it on first login');

  } catch (error) {
    console.error('❌ Error during seed process:', error);
    throw error;
  }
}

/**
 * Main seed function
 * Handles all seeding operations with proper error handling
 */
async function main() {
  try {
    await seedAdminUsers();
    console.log('🎉 Seed process completed successfully');
  } catch (error) {
    console.error('💥 Seed process failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
  }
}

// Run the seed script
if (require.main === module) {
  void main();
}

export { seedAdminUsers };