{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"**/*.{ts,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{ts,json,md,yml,yaml}\"", "lint": "eslint \"{src,apps,libs}/**/*.ts\" --ignore-pattern \"**/*.spec.ts\" --ignore-pattern \"**/*.test.ts\" --ignore-pattern \"**/test/**/*\" --fix", "lint:fix": "pnpm run format && pnpm run lint", "lint:fix:parallel": "concurrently \"pnpm run format\" \"pnpm run lint\"", "start": "NODE_ENV=local nest start", "start:dev": "NEST_DEBUG=true NODE_ENV=local nest build --webpack --webpackPath webpack-hmr.config.js --watch", "start:debug": "NODE_ENV=local nest start --debug --watch", "start:prod": "node dist/apps/api/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/api/test/jest-e2e.json", "start:dev:profile": "NODE_ENV=local PROFILE=true nest build --webpack --webpackPath webpack-hmr.config.js --watch --debug", "analyze": "webpack-bundle-analyzer dist/stats.json", "start:dev:fast": "NODE_ENV=local nest build --webpack --webpackPath webpack-hmr.config.js --watch --preserveWatchOutput", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx drizzle/seed.ts", "db:reset": "drizzle-kit push --force && pnpm run db:seed", "db:test-config": "node -e \"import('./drizzle.config.ts').then(config => console.log('✅ Drizzle config loaded successfully', config.default))\"", "prepare": "husky"}, "dependencies": {"@bull-board/api": "^6.12.0", "@bull-board/express": "^6.12.0", "@bull-board/ui": "^6.12.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/microservices": "^11.1.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/schedule": "^6.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.1.5", "@questdb/nodejs-client": "^3.0.0", "@types/bcrypt": "^6.0.0", "bcrypt": "^6.0.0", "bullmq": "^5.56.9", "cache-manager": "^7.0.1", "cache-manager-redis-store": "^3.0.1", "connect-redis": "^9.0.0", "dayjs": "^1.11.13", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "express": "^5.1.0", "express-session": "^1.18.2", "helmet": "^8.1.0", "ioredis": "^5.6.1", "kiteconnect": "^5.0.1", "nestjs-cls": "^6.0.1", "nestjs-pino": "^4.4.0", "nestjs-zod": "5.0.0-beta.20250519T201559", "pg": "^8.16.3", "pino-pretty": "^13.0.0", "postgres": "^3.4.7", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "task-master-ai": "^0.20.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.5", "@swc/cli": "^0.7.8", "@swc/core": "^1.13.0", "@types/express": "^5.0.3", "@types/express-session": "^1.18.2", "@types/jest": "^30.0.0", "@types/multer": "^2.0.0", "@types/node": "^24.0.15", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/socket.io": "^3.0.2", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "cache-loader": "^4.1.0", "concurrently": "^9.2.0", "drizzle-kit": "^0.31.4", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.4", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "run-script-webpack-plugin": "^0.2.3", "source-map-support": "^0.5.21", "supertest": "^7.1.3", "swc-loader": "^0.2.6", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "webpack": "^5.100.2", "webpack-bundle-analyzer": "^4.10.2", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/libs/", "<rootDir>/apps/"], "moduleNameMapper": {"^@app/admin(|/.*)$": "<rootDir>/libs/admin/src/$1", "^@app/broker(|/.*)$": "<rootDir>/libs/broker/src/$1", "^@app/common(|/.*)$": "<rootDir>/libs/common/src/$1", "^@app/core(|/.*)$": "<rootDir>/libs/core/src/$1", "^@app/event(|/.*)$": "<rootDir>/libs/event/src/$1", "^@app/holding(|/.*)$": "<rootDir>/libs/holding/src/$1", "^@app/journal(|/.*)$": "<rootDir>/libs/journal/src/$1", "^@app/margin(|/.*)$": "<rootDir>/libs/margin/src/$1", "^@app/notification(|/.*)$": "<rootDir>/libs/notification/src/$1", "^@app/order(|/.*)$": "<rootDir>/libs/order/src/$1", "^@app/portfolio(|/.*)$": "<rootDir>/libs/portfolio/src/$1", "^@app/position(|/.*)$": "<rootDir>/libs/position/src/$1", "^@app/scanner(|/.*)$": "<rootDir>/libs/scanner/src/$1", "^@app/schedular(|/.*)$": "<rootDir>/libs/schedular/src/$1", "^@app/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^@app/signal(|/.*)$": "<rootDir>/libs/signal/src/$1", "^@app/stats(|/.*)$": "<rootDir>/libs/stats/src/$1", "^@app/strategy(|/.*)$": "<rootDir>/libs/strategy/src/$1", "^@app/symbol(|/.*)$": "<rootDir>/libs/symbol/src/$1", "^@app/ticker-data(|/.*)$": "<rootDir>/libs/ticker-data/src/$1", "^@app/trade(|/.*)$": "<rootDir>/libs/trade/src/$1", "^@app/user(|/.*)$": "<rootDir>/libs/user/src/$1", "^@app/utils(|/.*)$": "<rootDir>/libs/utils/src/$1", "^@app/watchlist(|/.*)$": "<rootDir>/libs/watchlist/src/$1"}}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml}": ["prettier --write"]}}