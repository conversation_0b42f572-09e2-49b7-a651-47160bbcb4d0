#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 22 using NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm globally with sudo
sudo npm install -g pnpm

# Install build essentials and Python (required by some native dependencies)
sudo apt-get install -y build-essential python3 python3-pip

# Navigate to workspace directory
cd /mnt/persist/workspace

# Verify pnpm is available
pnpm --version

# Install project dependencies using pnpm
pnpm install --frozen-lockfile

# Set up environment variables for testing
export NODE_ENV=test
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=test_db
export DB_USER=test_user
export DB_PASSWORD=test_password
export REDIS_HOST=localhost
export REDIS_PORT=6379

# Add environment variables to profile for persistence
echo 'export NODE_ENV=test' >> $HOME/.profile
echo 'export DB_HOST=localhost' >> $HOME/.profile
echo 'export DB_PORT=5432' >> $HOME/.profile
echo 'export DB_NAME=test_db' >> $HOME/.profile
echo 'export DB_USER=test_user' >> $HOME/.profile
echo 'export DB_PASSWORD=test_password' >> $HOME/.profile
echo 'export REDIS_HOST=localhost' >> $HOME/.profile
echo 'export REDIS_PORT=6379' >> $HOME/.profile

# Create a simple test file in the libs directory to verify Jest is working
mkdir -p libs/test-demo/src
cat > libs/test-demo/src/demo.spec.ts << 'EOF'
describe('Environment Setup Test', () => {
  it('should pass a basic arithmetic test', () => {
    expect(2 + 2).toBe(4);
  });

  it('should handle string operations correctly', () => {
    const testString = 'PatternTrade API';
    expect(testString.toLowerCase()).toBe('patterntrade api');
    expect(testString.includes('API')).toBe(true);
  });

  it('should work with arrays and objects', () => {
    const testArray = [1, 2, 3, 4, 5];
    const testObject = { name: 'test', value: 42 };
    
    expect(testArray.length).toBe(5);
    expect(testArray).toContain(3);
    expect(testObject.name).toBe('test');
    expect(testObject.value).toBe(42);
  });

  it('should handle environment variables', () => {
    // Test that NODE_ENV is set
    expect(process.env.NODE_ENV).toBe('test');
  });

  it('should demonstrate async/await functionality', async () => {
    const asyncFunction = async () => {
      return new Promise((resolve) => {
        setTimeout(() => resolve('async result'), 10);
      });
    };

    const result = await asyncFunction();
    expect(result).toBe('async result');
  });
});
EOF