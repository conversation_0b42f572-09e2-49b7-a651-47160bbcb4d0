# PatternTrade API Controller Template Patterns

This document establishes the standardized patterns and conventions for implementing NestJS controllers in the PatternTrade API. All controller implementations must follow these established patterns to ensure architectural consistency, maintainability, and adherence to project standards.

## Controller Class Structure Template

### Basic Controller Template

```typescript
import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  NotFoundException,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@app/auth/guards/auth.guard';
import { RolesGuard } from '@app/auth/guards/roles.guard';
import { Roles } from '@app/auth/decorators/roles.decorator';
import { Public } from '@app/auth/decorators/public.decorator';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { createZodDto } from 'nestjs-zod';

// Import domain-specific schemas and services
import { 
  EntityService, 
  Entity,
  CreateEntitySchema,
  UpdateEntitySchema,
  EntitySearchSchema 
} from '@app/domain';

// Create DTO classes using nestjs-zod createZodDto
class CreateEntityDto extends createZodDto(CreateEntitySchema) {}
class UpdateEntityDto extends createZodDto(UpdateEntitySchema) {}
class EntitySearchDto extends createZodDto(EntitySearchSchema) {}

/**
 * Entity controller providing CRUD operations for entity management
 * Requirements: [List specific requirements from specs]
 */
@Controller('entities')
@UseGuards(AuthGuard, RolesGuard) // Apply to all endpoints unless @Public() is used
export class EntityController {
  private readonly logger = new Logger(EntityController.name);

  constructor(
    private readonly entityService: EntityService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // Controller methods follow below...
}
```

## Import Organization Standards

### Required Import Order

```typescript
// 1. Node.js built-ins (if needed)
import { readFile } from 'fs/promises';

// 2. NestJS framework imports
import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  NotFoundException,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';

// 3. Third-party libraries
import { createZodDto } from 'nestjs-zod';

// 4. Internal library imports (using path aliases)
import { AuthGuard } from '@app/auth/guards/auth.guard';
import { RolesGuard } from '@app/auth/guards/roles.guard';
import { Roles } from '@app/auth/decorators/roles.decorator';
import { Public } from '@app/auth/decorators/public.decorator';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

// 5. Domain-specific imports
import { EntityService, Entity } from '@app/domain';
import { CreateEntitySchema, UpdateEntitySchema, EntitySearchSchema } from '@app/domain';

// 6. Type-only imports (last)
import type { CustomType } from './types';
```

## Dependency Injection Patterns

### Standard Constructor Pattern

```typescript
export class EntityController {
  private readonly logger = new Logger(EntityController.name);

  constructor(
    private readonly entityService: EntityService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}
}
```

### Required Dependencies

- **Service Layer**: Always inject the primary domain service
- **Logger**: Use NestJS Logger with class name for context
- **DateTimeUtils**: For consistent timestamp handling
- **ErrorUtils**: For consistent error message extraction

## DTO Creation Patterns

### Zod Schema to DTO Conversion

```typescript
// Import schemas from domain library
import { CreateEntitySchema, UpdateEntitySchema, EntitySearchSchema } from '@app/domain';
import { createZodDto } from 'nestjs-zod';

// Create DTO classes using nestjs-zod createZodDto
class CreateEntityDto extends createZodDto(CreateEntitySchema) {}
class UpdateEntityDto extends createZodDto(UpdateEntitySchema) {}
class EntitySearchDto extends createZodDto(EntitySearchSchema) {}
```

### DTO Naming Convention

- **Create**: `Create{Entity}Dto`
- **Update**: `Update{Entity}Dto`
- **Search/Filter**: `{Entity}SearchDto`
- **Query Parameters**: `{Entity}QueryDto`

## Route Definition Patterns

### HTTP Method Decorators

```typescript
// CREATE - POST /entities
@Post()
@Roles('admin', 'user') // Specify required roles
async createEntity(@Body() createEntityDto: CreateEntityDto): Promise<Entity> {
  // Implementation
}

// READ ALL - GET /entities (with pagination)
@Get()
@Roles('admin')
async findAllEntities(
  @Query() searchFilters: EntitySearchDto,
  @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
  @Query('sortBy') sortBy?: string,
  @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
): Promise<PaginatedResult<Entity>> {
  // Implementation
}

// READ ONE - GET /entities/:id
@Get(':id')
@Roles('admin', 'user')
async findEntityById(@Param('id', ParseIntPipe) id: number): Promise<Entity> {
  // Implementation
}

// UPDATE - PATCH /entities/:id
@Patch(':id')
@Roles('admin')
async updateEntity(
  @Param('id', ParseIntPipe) id: number, 
  @Body() updateEntityDto: UpdateEntityDto
): Promise<Entity> {
  // Implementation
}

// DELETE - DELETE /entities/:id
@Delete(':id')
@Roles('admin')
async deleteEntity(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
  // Implementation
}
```

### Parameter Validation

- **Path Parameters**: Use `ParseIntPipe` for numeric IDs
- **Query Parameters**: Use appropriate pipes with `{ optional: true }` for optional params
- **Body Parameters**: Use Zod DTOs for automatic validation

## Authentication & Authorization Patterns

### Guard Application

```typescript
// Apply to entire controller (all endpoints protected by default)
@Controller('entities')
@UseGuards(AuthGuard, RolesGuard)
export class EntityController {
  
  // Public endpoint (bypasses authentication)
  @Get('public-info')
  @Public()
  async getPublicInfo(): Promise<PublicInfo> {
    // No authentication required
  }

  // Role-based access control
  @Post()
  @Roles('admin') // Only admin users
  async createEntity(): Promise<Entity> {
    // Admin only
  }

  @Get()
  @Roles('admin', 'user') // Both admin and user roles
  async findEntities(): Promise<Entity[]> {
    // Admin or user access
  }
}
```

### Role-Based Access Patterns

- **Admin Only**: `@Roles('admin')` - For management operations
- **User Access**: `@Roles('admin', 'user')` - For general user operations
- **Public Access**: `@Public()` - For endpoints that don't require authentication

## Request/Response Handling Patterns

### Pagination Implementation

```typescript
@Get()
@Roles('admin')
async findAllEntities(
  @Query() searchFilters: EntitySearchDto,
  @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
  @Query('sortBy') sortBy?: string,
  @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
): Promise<PaginatedResult<Entity>> {
  this.logger.log('Retrieving entities with filters and pagination');

  const paginationOptions: PaginationOptions = {
    limit: limit || 10,
    offset: offset || 0,
    sortBy: sortBy || 'createdAt',
    sortOrder: sortOrder || 'DESC',
    filters: searchFilters as Record<string, unknown>,
  };

  const result = await this.entityService.findEntitiesPaginated(paginationOptions);
  this.logger.log(`Retrieved ${result.data.length} entities out of ${result.total} total`);
  return result;
}
```

### Response Formatting

```typescript
// Simple entity response
async findEntityById(@Param('id', ParseIntPipe) id: number): Promise<Entity> {
  this.logger.log(`Retrieving entity with ID: ${id}`);
  
  const entity = await this.entityService.findEntityById(id);
  
  if (!entity) {
    this.logger.warn(`Entity with ID ${id} not found`);
    throw new NotFoundException(`Entity with ID ${id} not found`);
  }
  
  this.logger.log(`Successfully retrieved entity: ${entity.id}`);
  return entity;
}

// Success message response
async deleteEntity(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
  this.logger.log(`Deleting entity with ID: ${id}`);

  await this.entityService.deleteEntity(id);
  this.logger.log(`Successfully deleted entity with ID: ${id}`);
  return { message: `Entity with ID ${id} has been deleted` };
}
```

## Error Handling Patterns

### Standard Error Handling

```typescript
// Let NestJS exception filters handle domain errors (recommended)
async findEntityById(@Param('id', ParseIntPipe) id: number): Promise<Entity> {
  this.logger.log(`Retrieving entity with ID: ${id}`);

  const entity = await this.entityService.findEntityById(id);

  if (!entity) {
    this.logger.warn(`Entity with ID ${id} not found`);
    throw new NotFoundException(`Entity with ID ${id} not found`);
  }

  this.logger.log(`Successfully retrieved entity: ${entity.id}`);
  return entity;
}

// Manual error handling (when needed for custom responses)
async getEntityWithCustomError(@Param('id', ParseIntPipe) id: number): Promise<EntityResponseDto> {
  try {
    this.logger.log(`Getting entity with ID: ${id}`);

    const entity = await this.entityService.findEntityById(id);

    return {
      success: true,
      data: entity,
      timestamp: this.dateTimeUtils.getUtcNow(),
    };
  } catch (error) {
    this.logger.error('Failed to get entity', {
      id,
      error: this.errorUtils.getErrorMessage(error),
    });

    throw new HttpException(
      {
        success: false,
        error: this.errorUtils.getErrorMessage(error),
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}
```

### Error Logging Patterns

```typescript
// Standard error logging with context
this.logger.error('Operation failed', {
  operationContext: { id, data },
  error: this.errorUtils.getErrorMessage(error),
  stack: this.errorUtils.getErrorStack(error),
});

// Warning for expected errors
this.logger.warn(`Entity with ID ${id} not found`);

// Info for successful operations
this.logger.log(`Successfully created entity with ID: ${entity.id}`);
```

## Logging Integration Patterns

### Structured Logging

```typescript
export class EntityController {
  private readonly logger = new Logger(EntityController.name);

  async createEntity(@Body() createEntityDto: CreateEntityDto): Promise<Entity> {
    // Log operation start with context
    this.logger.log(`Creating new entity with data`, {
      operation: 'createEntity',
      data: { ...createEntityDto, password: '[REDACTED]' }, // Redact sensitive data
    });

    const entity = await this.entityService.createEntity(createEntityDto);

    // Log successful completion
    this.logger.log(`Successfully created entity`, {
      operation: 'createEntity',
      entityId: entity.id,
      email: entity.email,
    });

    return entity;
  }
}
```

### Log Level Guidelines

- **`logger.log()`**: Successful operations, normal flow
- **`logger.warn()`**: Expected errors (not found, validation failures)
- **`logger.error()`**: Unexpected errors, system failures
- **`logger.debug()`**: Detailed debugging information (development only)

## Validation Patterns

### Zod Integration with NestJS

```typescript
// Automatic validation through DTO classes
@Post()
async createEntity(@Body() createEntityDto: CreateEntityDto): Promise<Entity> {
  // createEntityDto is automatically validated by nestjs-zod
  // Validation errors are automatically handled by exception filters
  return this.entityService.createEntity(createEntityDto);
}

// Manual validation (when needed)
@Post('custom-validation')
async createEntityWithCustomValidation(@Body() rawData: unknown): Promise<Entity> {
  // Manual validation with custom error handling
  const parseResult = CreateEntitySchema.safeParse(rawData);

  if (!parseResult.success) {
    this.logger.warn('Validation failed', {
      errors: parseResult.error.errors,
      data: rawData,
    });

    throw new HttpException(
      {
        success: false,
        error: 'Validation failed',
        details: parseResult.error.errors,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  return this.entityService.createEntity(parseResult.data);
}
```

## Documentation Patterns

### JSDoc Standards

```typescript
/**
 * Create a new entity
 * Requirements: [Specific requirement references from specs]
 *
 * @param createEntityDto - Entity creation data
 * @returns Promise<Entity> - Created entity with generated ID
 * @throws NotFoundException - When referenced data is not found
 * @throws ValidationException - When input data is invalid
 */
@Post()
@Roles('admin')
async createEntity(@Body() createEntityDto: CreateEntityDto): Promise<Entity> {
  // Implementation
}

/**
 * Get entities with optional filtering and pagination
 * Requirements: [Specific requirement references from specs]
 *
 * @param searchFilters - Search and filter criteria
 * @param limit - Maximum number of results (default: 10)
 * @param offset - Number of results to skip (default: 0)
 * @param sortBy - Field to sort by (default: 'createdAt')
 * @param sortOrder - Sort direction (default: 'DESC')
 * @returns Promise<PaginatedResult<Entity>> - Paginated entity results
 */
@Get()
@Roles('admin')
async findAllEntities(
  @Query() searchFilters: EntitySearchDto,
  @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
  @Query('sortBy') sortBy?: string,
  @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
): Promise<PaginatedResult<Entity>> {
  // Implementation
}
```

## Complete Controller Example

### Full Implementation Template

```typescript
import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { UserService, User } from '@app/user';
import { AuthGuard } from '@app/auth/guards/auth.guard';
import { RolesGuard } from '@app/auth/guards/roles.guard';
import { Roles } from '@app/auth/decorators/roles.decorator';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { CreateUserSchema, UpdateUserSchema, UserSearchSchema } from '@app/user';
import { createZodDto } from 'nestjs-zod';

// Create DTO classes using nestjs-zod createZodDto
class CreateUserDto extends createZodDto(CreateUserSchema) {}
class UpdateUserDto extends createZodDto(UpdateUserSchema) {}
class UserSearchDto extends createZodDto(UserSearchSchema) {}

/**
 * User controller providing CRUD operations for user management
 * Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 7.4 - Admin-only user management with validation
 */
@Controller('users')
@UseGuards(AuthGuard, RolesGuard)
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly userService: UserService) {}

  /**
   * Create a new user
   * Requirements: 1.1 - Admin can create new user accounts
   */
  @Post()
  @Roles('admin')
  async createUser(@Body() createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating new user with email: ${createUserDto.email}`);

    const user = await this.userService.createUser(createUserDto);
    this.logger.log(`Successfully created user with ID: ${user.id}`);
    return user;
  }

  /**
   * Get all users with optional filtering and pagination
   * Requirements: 1.2 - Admin can retrieve list of all users
   */
  @Get()
  @Roles('admin')
  async findAllUsers(
    @Query() searchFilters: UserSearchDto,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResult<User>> {
    this.logger.log('Retrieving users with filters and pagination');

    const paginationOptions: PaginationOptions = {
      limit: limit || 10,
      offset: offset || 0,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'DESC',
      filters: searchFilters as Record<string, unknown>,
    };

    const result = await this.userService.findUsersPaginated(paginationOptions);
    this.logger.log(`Retrieved ${result.data.length} users out of ${result.total} total`);
    return result;
  }

  /**
   * Get user by ID
   * Requirements: 1.3 - Admin can retrieve detailed user information
   */
  @Get(':id')
  @Roles('admin')
  async findUserById(@Param('id', ParseIntPipe) id: number): Promise<User> {
    this.logger.log(`Retrieving user with ID: ${id}`);

    const user = await this.userService.findUserById(id);

    if (!user) {
      this.logger.warn(`User with ID ${id} not found`);
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    this.logger.log(`Successfully retrieved user: ${user.email}`);
    return user;
  }

  /**
   * Update user information
   * Requirements: 1.4 - Admin can update user information
   */
  @Patch(':id')
  @Roles('admin')
  async updateUser(@Param('id', ParseIntPipe) id: number, @Body() updateUserDto: UpdateUserDto): Promise<User> {
    this.logger.log(`Updating user with ID: ${id}`);

    const updatedUser = await this.userService.updateUser(id, updateUserDto);
    this.logger.log(`Successfully updated user: ${updatedUser.email}`);
    return updatedUser;
  }

  /**
   * Delete user (soft delete by deactivating)
   * Requirements: 1.5 - Admin can delete/deactivate user accounts
   */
  @Delete(':id')
  @Roles('admin')
  async deleteUser(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    this.logger.log(`Deleting user with ID: ${id}`);

    await this.userService.deleteUser(id);
    this.logger.log(`Successfully deleted user with ID: ${id}`);
    return { message: `User with ID ${id} has been deactivated` };
  }
}
```

## Key Principles Summary

1. **Consistency**: All controllers follow the same structural patterns
2. **Security**: Authentication and authorization applied consistently
3. **Validation**: Zod schemas integrated with NestJS validation pipes
4. **Logging**: Structured logging with proper context and levels
5. **Error Handling**: Consistent error responses through exception filters
6. **Documentation**: Comprehensive JSDoc with requirement references
7. **Type Safety**: Full TypeScript typing with no `any` types
8. **Maintainability**: Clear separation of concerns and dependency injection

## Implementation Checklist

When creating a new controller, ensure:

- [ ] Proper import organization following the established order
- [ ] DTO classes created using `createZodDto` from domain schemas
- [ ] Authentication guards applied at controller level
- [ ] Role-based authorization on all endpoints
- [ ] Structured logging with operation context
- [ ] Pagination support for list endpoints
- [ ] Proper error handling and HTTP status codes
- [ ] JSDoc documentation with requirement references
- [ ] Type safety with no `any` types
- [ ] Consistent response formats

This template ensures all PatternTrade API controllers maintain architectural consistency while following established best practices and project standards.
