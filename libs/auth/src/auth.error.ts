import { z } from 'zod/v4';
import { BaseError } from '@app/common/errors';
import type { ErrorDomainType } from '@app/common/errors';

/**
 * Authentication-specific error types enum
 * Requirements: 7.1, 7.2, 7.3 - Comprehensive error handling for authentication
 */
export const AuthErrorEnum = z.enum([
  // Authentication errors
  'INVALID_CREDENTIALS',
  'USER_NOT_AUTHENTICATED',
  'AUTHENTICATION_FAILED',
  // Session errors
  'SESSION_EXPIRED',
  'SESSION_INVALID',
  'SESSION_CREATION_FAILED',
  'SESSION_DESTRUCTION_FAILED',
  'SESSION_NOT_FOUND',
  // Password reset errors
  'PASSWORD_RESET_TOKEN_INVALID',
  'PASSWORD_RESET_TOKEN_EXPIRED',
  'PASSWORD_RESET_TOKEN_USED',
  'PASSWORD_RESET_FAILED',
  'PASSWORD_RESET_REQUEST_FAILED',
  // Password change errors
  'PASSWORD_TOO_WEAK',
  'CURRENT_PASSWORD_INCORRECT',
  'PASSWORD_CHANGE_FAILED',
  'PASSWORD_SAME_AS_CURRENT',
  // Account security errors
  'ACCOUNT_LOCKED',
  'TOO_MANY_LOGIN_ATTEMPTS',
  'UNAUTHORIZED_ACCESS',
  'INSUFFICIENT_PERMISSIONS',
]);

/**
 * Error messages mapping for consistent error responses
 * Requirements: 7.3 - Consistent error messages without revealing sensitive information
 */
export const AuthErrorMessages: Record<AuthErrorEnumType, string> = {
  // Authentication errors
  INVALID_CREDENTIALS: 'Invalid email or password',
  USER_NOT_AUTHENTICATED: 'Authentication required',
  AUTHENTICATION_FAILED: 'Authentication failed',
  // Session errors
  SESSION_EXPIRED: 'Your session has expired. Please log in again',
  SESSION_INVALID: 'Invalid session. Please log in again',
  SESSION_CREATION_FAILED: 'Failed to create session',
  SESSION_DESTRUCTION_FAILED: 'Failed to end session',
  SESSION_NOT_FOUND: 'Session not found',
  // Password reset errors
  PASSWORD_RESET_TOKEN_INVALID: 'Invalid or expired password reset token',
  PASSWORD_RESET_TOKEN_EXPIRED: 'Password reset token has expired',
  PASSWORD_RESET_TOKEN_USED: 'Password reset token has already been used',
  PASSWORD_RESET_FAILED: 'Failed to reset password',
  PASSWORD_RESET_REQUEST_FAILED: 'Failed to process password reset request',
  // Password change errors
  PASSWORD_TOO_WEAK: 'Password does not meet security requirements',
  CURRENT_PASSWORD_INCORRECT: 'Current password is incorrect',
  PASSWORD_CHANGE_FAILED: 'Failed to change password',
  PASSWORD_SAME_AS_CURRENT: 'New password must be different from current password',
  // Account security errors
  ACCOUNT_LOCKED: 'Account is temporarily locked for security reasons',
  TOO_MANY_LOGIN_ATTEMPTS: 'Too many failed login attempts. Please try again later',
  UNAUTHORIZED_ACCESS: 'Access denied',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions to perform this action',
};

export type AuthErrorEnumType = z.output<typeof AuthErrorEnum>;

/**
 * Authentication error class extending BaseError with proper error codes
 * Requirements: 7.1, 7.2 - Proper error handling with HTTP status codes
 */
export class AuthError extends BaseError<AuthErrorEnumType> {
  constructor(name: AuthErrorEnumType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : AuthErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

/**
 * Helper function to create authentication errors with AUTH domain
 * Requirements: 7.1 - Consistent error creation
 */
export const createAuthError = (
  name: AuthErrorEnumType,
  details?: { message?: string; cause?: unknown },
): AuthError => {
  return new AuthError(name, 'AUTH', details);
};

/**
 * Helper function to create session errors with SESSION domain
 * Requirements: 7.1 - Consistent error creation for session operations
 */
export const createSessionError = (
  name: AuthErrorEnumType,
  details?: { message?: string; cause?: unknown },
): AuthError => {
  return new AuthError(name, 'SESSION', details);
};
