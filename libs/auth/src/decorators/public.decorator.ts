import { SetMetadata } from '@nestjs/common';

/**
 * Metadata key for public endpoints
 */
export const IS_PUBLIC_KEY = 'isPublic';

/**
 * Decorator to mark endpoints as public (bypass authentication)
 * Requirements: 5.1, 5.2 - Public endpoint support
 *
 * @example
 * ```typescript
 * @Post('login')
 * @Public()
 * async login(@Body() loginDto: LoginRequest) {
 *   // This endpoint bypasses authentication
 * }
 * ```
 */
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
