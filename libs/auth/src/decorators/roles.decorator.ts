import { SetMetadata } from '@nestjs/common';
import type { Role } from '../constants/role.enum';

/**
 * Metadata key for role-based access control
 */
export const ROLES_KEY = 'roles';

/**
 * Decorator to specify required roles for endpoints
 * Requirements: 5.1, 5.2 - Role-based access control
 *
 * @param roles - Array of roles that can access the endpoint
 *
 * @example
 * ```typescript
 * @Get('users')
 * @Roles('admin')
 * async getAllUsers() {
 *   // Only admin users can access this endpoint
 * }
 *
 * @Post('profile')
 * @Roles('admin', 'user')
 * async updateProfile() {
 *   // Both admin and user roles can access this endpoint
 * }
 * ```
 */
export const Roles = (...roles: Role[]) => SetMetadata(ROLES_KEY, roles);
