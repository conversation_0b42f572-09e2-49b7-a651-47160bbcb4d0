import { Modu<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { PasswordResetTokenRepository } from './password-reset-token.repository';
import { AuthGuard } from './guards/auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { UserModule } from '@app/user';
import { CoreModule } from '@app/core';
import { UtilsModule } from '@app/utils';

@Module({
  imports: [UserModule, CoreModule, UtilsModule],
  providers: [AuthService, PasswordResetTokenRepository, AuthGuard, RolesGuard],
  exports: [AuthService, PasswordResetTokenRepository, AuthGuard, RolesGuard],
})
export class AuthModule {}
