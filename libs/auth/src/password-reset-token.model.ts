import { pgTable, varchar, boolean, integer, index } from 'drizzle-orm/pg-core';
import { baseModel } from '@app/common/models';
import { UserTable } from '@app/user/user.model';

// ==================== DATABASE SCHEMA ====================

/**
 * Password Reset Token table schema for PatternTrade platform
 * Manages secure password reset tokens with expiration and usage tracking
 */
export const PasswordResetTokenTable = pgTable(
  'password_reset_tokens',
  {
    ...baseModel,
    userId: integer('user_id')
      .notNull()
      .references(() => UserTable.id, { onDelete: 'cascade' }),
    token: varchar('token', { length: 255 }).notNull().unique(),
    expiresAt: varchar('expires_at', { length: 32 }).notNull(),
    isUsed: boolean('is_used').notNull().default(false),
  },
  (table) => [
    // Index for user_id lookups (finding tokens for a user)
    index('password_reset_tokens_user_id_idx').on(table.userId),
    // Index for token lookups (validation)
    index('password_reset_tokens_token_idx').on(table.token),
    // Index for expiration cleanup queries
    index('password_reset_tokens_expires_at_idx').on(table.expiresAt),
    // Composite index for active tokens by user
    index('password_reset_tokens_user_active_idx').on(table.userId, table.isUsed, table.expiresAt),
  ],
);
