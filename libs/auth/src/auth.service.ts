import { Injectable, Logger } from '@nestjs/common';
import { UserService } from '@app/user';
import { SessionService } from '@app/core/session';
import { DateTimeUtilsService } from '@app/utils';
import { PasswordResetTokenRepository } from './password-reset-token.repository';
import {
  LoginRequest,
  PasswordReset,
  ChangePasswordRequest,
  LoginResponse,
  LogoutResponse,
  PasswordResetRequestResponse,
  PasswordResetResponse,
  ChangePasswordResponse,
  SessionContext,
  SessionValidationResponse,
} from './auth.schema';
import { AuthError, createAuthError, createSessionError } from './auth.error';

/**
 * Auth service interface defining all required authentication methods
 * Requirements: 3.1, 3.2, 3.3, 4.1, 4.2, 4.3, 4.4, 4.5, 6.1, 6.2, 6.3
 */
export interface IAuthService {
  login(credentials: LoginRequest, sessionContext: SessionContext): Promise<LoginResponse>;
  logout(sessionId: string): Promise<LogoutResponse>;
  validateSession(sessionId: string): Promise<SessionValidationResponse>;
  requestPasswordReset(email: string): Promise<PasswordResetRequestResponse>;
  resetPassword(resetData: PasswordReset): Promise<PasswordResetResponse>;
  changePassword(userId: number, changeData: ChangePasswordRequest): Promise<ChangePasswordResponse>;
  refreshSession(sessionId: string): Promise<SessionValidationResponse>;
}

/**
 * Auth service providing authentication and session management functionality
 * Integrates with UserService for credential validation and SessionService for session management
 */
@Injectable()
export class AuthService implements IAuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly userService: UserService,
    private readonly sessionService: SessionService,
    private readonly passwordResetTokenRepository: PasswordResetTokenRepository,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Authenticate user with credentials and create session
   * Requirements: 3.1 - User authentication with credentials and session creation
   */
  async login(credentials: LoginRequest, sessionContext: SessionContext): Promise<LoginResponse> {
    try {
      this.logger.log(`Login attempt for email: ${credentials.email}`);

      // Find user by email
      const user = await this.userService.findUserByEmail(credentials.email);
      if (!user) {
        throw createAuthError('INVALID_CREDENTIALS');
      }

      // Check if user is active
      if (!user.isActive) {
        throw createAuthError('ACCOUNT_LOCKED', {
          message: 'User account is inactive',
        });
      }

      // Validate password
      const isPasswordValid = await this.userService.validatePassword(user, credentials.password);
      if (!isPasswordValid) {
        throw createAuthError('INVALID_CREDENTIALS');
      }

      // Create session data
      const sessionData = this.sessionService.createSessionData({
        isAuthenticated: true,
        user: {
          id: user.id.toString(),
          email: user.email,
          orgId: 'default', // PatternTrade doesn't use orgId, using default
          role: user.role,
        },
        ipAddress: sessionContext.ipAddress,
        userAgent: sessionContext.userAgent,
      });

      // Update user's last login timestamp
      await this.userService.updateLastLogin(user.id);

      this.logger.log(`Successful login for user ID: ${user.id}`);

      return {
        success: true,
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        },
        sessionId: sessionData.id,
        expiresAt: sessionData.expiresAt,
      };
    } catch (error) {
      if (error instanceof AuthError) {
        this.logger.warn(`Login failed for email: ${credentials.email} - ${error.message}`);
        throw error;
      }

      this.logger.error(`Login error for email: ${credentials.email}:`, error);
      throw createAuthError('AUTHENTICATION_FAILED', {
        message: 'Login failed due to system error',
        cause: error,
      });
    }
  }

  /**
   * Logout user and destroy session
   * Requirements: 3.3 - Logout with proper session cleanup
   */
  logout(sessionId: string): Promise<LogoutResponse> {
    try {
      this.logger.log(`Logout attempt for session: ${sessionId}`);

      // Note: The actual session destruction would be handled by the session middleware
      // when the session cookie is cleared. This method provides confirmation.

      this.logger.log(`Logout successful for session: ${sessionId}`);

      return Promise.resolve({
        success: true,
        message: 'Logout successful',
      });
    } catch (error) {
      this.logger.error(`Logout error for session: ${sessionId}:`, error);
      throw createSessionError('SESSION_DESTRUCTION_FAILED', {
        message: 'Failed to logout',
        cause: error,
      });
    }
  }

  /**
   * Validate session and return session information
   * Requirements: 5.1, 6.1 - Session validation for protected endpoints
   */
  validateSession(sessionId: string): Promise<SessionValidationResponse> {
    try {
      this.logger.debug(`Validating session: ${sessionId}`);

      // Note: In a real implementation, this would validate the session with Redis
      // For now, we'll return a basic validation structure
      // The actual session validation is handled by the SessionService and middleware

      return Promise.resolve({
        isValid: true,
        session: {
          id: sessionId,
          user: {
            id: 1, // This would come from the actual session data
            email: '<EMAIL>',
            role: 'user' as const,
          },
          expiresAt: this.dateTimeUtils.addHours(this.dateTimeUtils.getUtcNow(), 1),
          lastAccessedAt: this.dateTimeUtils.getUtcNow(),
        },
      });
    } catch (error) {
      this.logger.error(`Session validation error for session: ${sessionId}:`, error);
      return Promise.resolve({
        isValid: false,
      });
    }
  }

  /**
   * Refresh session and extend expiration
   * Requirements: 6.1, 6.2 - Session management with refresh capability
   */
  async refreshSession(sessionId: string): Promise<SessionValidationResponse> {
    try {
      this.logger.debug(`Refreshing session: ${sessionId}`);

      // Validate current session first
      const validation = await this.validateSession(sessionId);
      if (!validation.isValid || !validation.session) {
        throw createSessionError('SESSION_INVALID');
      }

      // Update session access time (would be handled by SessionService)
      const refreshedSession = {
        ...validation.session,
        lastAccessedAt: this.dateTimeUtils.getUtcNow(),
        expiresAt: this.dateTimeUtils.addHours(this.dateTimeUtils.getUtcNow(), 1),
      };

      this.logger.debug(`Session refreshed: ${sessionId}`);

      return {
        isValid: true,
        session: refreshedSession,
      };
    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }

      this.logger.error(`Session refresh error for session: ${sessionId}:`, error);
      throw createSessionError('SESSION_INVALID', {
        message: 'Failed to refresh session',
        cause: error,
      });
    }
  }

  /**
   * Request password reset by generating and storing reset token
   * Requirements: 4.1 - Password reset initiation with token generation
   */
  async requestPasswordReset(email: string): Promise<PasswordResetRequestResponse> {
    try {
      this.logger.log(`Password reset requested for email: ${email}`);

      // Find user by email
      const user = await this.userService.findUserByEmail(email);
      if (!user) {
        // Don't reveal if user exists or not for security
        this.logger.warn(`Password reset requested for non-existent email: ${email}`);
        return {
          success: true,
          message: 'If the email exists in our system, a password reset link has been sent',
        };
      }

      // Check if user is active
      if (!user.isActive) {
        this.logger.warn(`Password reset requested for inactive user: ${email}`);
        return {
          success: true,
          message: 'If the email exists in our system, a password reset link has been sent',
        };
      }

      // Generate password reset token
      const resetToken = await this.passwordResetTokenRepository.generateTokenForUser(user.id);

      // In a real implementation, you would send an email with the reset token here
      // For now, we'll just log it (remove this in production)
      this.logger.debug(`Password reset token generated for user ${user.id}: ${resetToken.token}`);

      this.logger.log(`Password reset token generated for user ID: ${user.id}`);

      return {
        success: true,
        message: 'If the email exists in our system, a password reset link has been sent',
      };
    } catch (error) {
      this.logger.error(`Password reset request error for email: ${email}:`, error);
      throw createAuthError('PASSWORD_RESET_REQUEST_FAILED', {
        message: 'Failed to process password reset request',
        cause: error,
      });
    }
  }

  /**
   * Reset password using valid reset token
   * Requirements: 4.2, 4.3 - Password reset with token validation and password update
   */
  async resetPassword(resetData: PasswordReset): Promise<PasswordResetResponse> {
    try {
      this.logger.log(`Password reset attempt with token`);

      // Find and validate reset token
      const resetToken = await this.passwordResetTokenRepository.findValidToken(resetData.token);
      if (!resetToken) {
        throw createAuthError('PASSWORD_RESET_TOKEN_INVALID');
      }

      // Verify user exists and is active
      const user = await this.userService.findUserById(resetToken.userId);
      if (!user || !user.isActive) {
        throw createAuthError('PASSWORD_RESET_TOKEN_INVALID', {
          message: 'Invalid token or inactive user',
        });
      }

      // Update user password
      await this.userService.updatePassword(resetToken.userId, resetData.newPassword);

      // Mark token as used
      await this.passwordResetTokenRepository.markTokenAsUsed(resetToken.id);

      this.logger.log(`Password reset successful for user ID: ${resetToken.userId}`);

      return {
        success: true,
        message: 'Password has been reset successfully',
      };
    } catch (error) {
      if (error instanceof AuthError) {
        this.logger.warn(`Password reset failed: ${error.message}`);
        throw error;
      }

      this.logger.error(`Password reset error:`, error);
      throw createAuthError('PASSWORD_RESET_FAILED', {
        message: 'Failed to reset password',
        cause: error,
      });
    }
  }

  /**
   * Change password for authenticated user
   * Requirements: 4.4 - Password change for authenticated users
   */
  async changePassword(userId: number, changeData: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    try {
      this.logger.log(`Password change attempt for user ID: ${userId}`);

      // Find user
      const user = await this.userService.findUserById(userId);
      if (!user) {
        throw createAuthError('USER_NOT_AUTHENTICATED', {
          message: 'User not found',
        });
      }

      // Check if user is active
      if (!user.isActive) {
        throw createAuthError('ACCOUNT_LOCKED', {
          message: 'User account is inactive',
        });
      }

      // Validate current password
      const isCurrentPasswordValid = await this.userService.validatePassword(user, changeData.currentPassword);
      if (!isCurrentPasswordValid) {
        throw createAuthError('CURRENT_PASSWORD_INCORRECT');
      }

      // Ensure new password is different from current
      const isSamePassword = await this.userService.validatePassword(user, changeData.newPassword);
      if (isSamePassword) {
        throw createAuthError('PASSWORD_SAME_AS_CURRENT');
      }

      // Update password
      await this.userService.updatePassword(userId, changeData.newPassword);

      // Invalidate any existing password reset tokens for this user
      await this.passwordResetTokenRepository.invalidateUserTokens(userId);

      this.logger.log(`Password changed successfully for user ID: ${userId}`);

      return {
        success: true,
        message: 'Password has been changed successfully',
      };
    } catch (error) {
      if (error instanceof AuthError) {
        this.logger.warn(`Password change failed for user ID: ${userId} - ${error.message}`);
        throw error;
      }

      this.logger.error(`Password change error for user ID: ${userId}:`, error);
      throw createAuthError('PASSWORD_CHANGE_FAILED', {
        message: 'Failed to change password',
        cause: error,
      });
    }
  }
}
