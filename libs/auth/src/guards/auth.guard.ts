import { CanActivate, ExecutionContext, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { AuthService } from '../auth.service';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { AuthError } from '../auth.error';

// Define extended request interface
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: 'admin' | 'user';
  };
}

/**
 * Authentication guard that validates sessions for protected endpoints
 * Requirements: 5.1, 5.2, 5.3, 5.4 - Session-based authentication with public endpoint support
 */
@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private reflector: Reflector,
    private authService: AuthService,
  ) {}

  /**
   * Determines if the current request can proceed based on authentication status
   *
   * @param context - Execution context containing request information
   * @returns Promise<boolean> - True if request can proceed, false otherwise
   * @throws UnauthorizedException - When authentication fails
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      // Check if endpoint is marked as public
      const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug('Public endpoint accessed, bypassing authentication');
        return true;
      }

      const request = context.switchToHttp().getRequest<AuthenticatedRequest>();

      // Extract session ID from request
      const sessionId = this.extractSessionId(request);

      if (!sessionId) {
        this.logger.warn('No session ID found in request');
        throw new UnauthorizedException('No active session found');
      }

      // Validate session with auth service
      const validation = await this.authService.validateSession(sessionId);

      if (!validation.isValid || !validation.session) {
        this.logger.warn(`Invalid session: ${sessionId}`);
        throw new UnauthorizedException('Invalid or expired session');
      }

      // Attach user information to request for use in controllers
      request.user = validation.session.user;

      this.logger.debug(`Authentication successful for user: ${validation.session.user.email}`);
      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      if (error instanceof AuthError) {
        this.logger.warn(`Authentication failed: ${error.message}`);
        throw new UnauthorizedException(error.message);
      }

      this.logger.error('Authentication error:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Extract session ID from request
   * Supports both session cookie and custom session header
   *
   * @param request - Express request object
   * @returns Session ID string or null if not found
   */
  private extractSessionId(request: AuthenticatedRequest): string | null {
    // Try to get session ID from express-session
    if (request.session?.id) {
      return request.session.id;
    }

    // Try to get session ID from custom header (for API clients)
    const sessionHeader = request.headers['x-session-id'];
    if (sessionHeader && typeof sessionHeader === 'string') {
      return sessionHeader;
    }

    // Try to get session ID from Authorization header (Bearer token format)
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return null;
  }
}
