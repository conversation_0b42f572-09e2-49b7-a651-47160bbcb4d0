import { Injectable } from '@nestjs/common';
import { eq, and, lt, gt, type InferInsertModel } from 'drizzle-orm';
import * as crypto from 'crypto';
import { BaseRepository } from '@app/common/repository';
import { DrizzleService } from '@app/core/drizzle';
import { UtilsService, DbUtilsService, DateTimeUtilsService } from '@app/utils';
import { PasswordResetTokenTable } from './password-reset-token.model';
import { PasswordResetToken, CreatePasswordResetTokenRequest, UpdatePasswordResetTokenRequest } from './auth.schema';
import { AuthError, AuthErrorEnum } from './auth.error';
import { EntityNotFoundError } from '@app/common/repository';

// Type for PasswordResetTokenTable insert operations
type PasswordResetTokenInsert = InferInsertModel<typeof PasswordResetTokenTable>;

/**
 * Password Reset Token repository providing data access operations for token management
 * Extends BaseRepository with token-specific functionality including generation and validation
 */
@Injectable()
export class PasswordResetTokenRepository extends BaseRepository<
  PasswordResetToken,
  CreatePasswordResetTokenRequest,
  UpdatePasswordResetTokenRequest
> {
  private static readonly TOKEN_LENGTH = 64;
  private static readonly TOKEN_EXPIRY_HOURS = 1;

  constructor(
    drizzleService: DrizzleService,
    utilsService: UtilsService,
    dbUtils: DbUtilsService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    super(drizzleService, utilsService, dbUtils, 'PasswordResetTokenRepository');
  }

  // ==================== ABSTRACT METHOD IMPLEMENTATIONS ====================

  protected getTable() {
    return PasswordResetTokenTable;
  }

  protected getTableName(): string {
    return 'password_reset_tokens';
  }

  // ==================== TOKEN-SPECIFIC METHODS ====================

  /**
   * Generate and create a new password reset token for a user
   * @param userId - User ID to create token for
   * @returns Promise<PasswordResetToken> - Created token entity
   * @throws AuthError if token generation fails
   */
  async generateTokenForUser(userId: number): Promise<PasswordResetToken> {
    return this.executeWithErrorHandling('generateTokenForUser', async () => {
      this.logOperation('generateTokenForUser', { userId });

      // Invalidate any existing active tokens for this user
      await this.invalidateUserTokens(userId);

      // Generate secure token
      const token = this.generateSecureToken();
      const expiresAt = this.dateTimeUtils.addHours(
        this.dateTimeUtils.getUtcNow(),
        PasswordResetTokenRepository.TOKEN_EXPIRY_HOURS,
      );

      // Create token data
      const tokenData: CreatePasswordResetTokenRequest = {
        userId,
        token,
        expiresAt,
      };

      // Insert token
      const auditedData = this.addCreateAuditFields(tokenData);
      const [created] = await this.getDb()
        .insert(PasswordResetTokenTable)
        .values(auditedData as PasswordResetTokenInsert)
        .returning();

      this.logger.log(`Generated password reset token for user ID: ${userId}`);
      return created as PasswordResetToken;
    });
  }

  /**
   * Find and validate a password reset token
   * @param token - Token string to validate
   * @returns Promise<PasswordResetToken | null> - Valid token or null
   */
  async findValidToken(token: string): Promise<PasswordResetToken | null> {
    return this.executeWithErrorHandling('findValidToken', async () => {
      this.logOperation('findValidToken', { tokenLength: token.length });

      const now = this.dateTimeUtils.getUtcNow();

      const results = await this.getDb()
        .select()
        .from(PasswordResetTokenTable)
        .where(
          and(
            eq(PasswordResetTokenTable.token, token),
            eq(PasswordResetTokenTable.isUsed, false),
            gt(PasswordResetTokenTable.expiresAt, now), // Token not expired
          ),
        )
        .limit(1);

      const foundToken = results[0] as PasswordResetToken | undefined;

      if (foundToken) {
        this.logger.debug(`Found valid token for user ID: ${foundToken.userId}`);
      } else {
        this.logger.debug('No valid token found');
      }

      return foundToken || null;
    });
  }

  /**
   * Mark a token as used
   * @param tokenId - Token ID to mark as used
   * @returns Promise<void>
   * @throws EntityNotFoundError if token doesn't exist
   */
  async markTokenAsUsed(tokenId: number): Promise<void> {
    return this.executeWithErrorHandling('markTokenAsUsed', async () => {
      this.logOperation('markTokenAsUsed', { tokenId });

      // Validate token exists
      await this.validateEntityExists(tokenId);

      // Mark as used
      const auditedUpdates = this.addUpdateAuditFields({ isUsed: true });

      await this.getDb()
        .update(PasswordResetTokenTable)
        .set(auditedUpdates)
        .where(eq(PasswordResetTokenTable.id, tokenId));

      this.logger.log(`Marked token as used: ${tokenId}`);
    });
  }

  /**
   * Find all active tokens for a user
   * @param userId - User ID
   * @returns Promise<PasswordResetToken[]> - Array of active tokens
   */
  async findActiveTokensForUser(userId: number): Promise<PasswordResetToken[]> {
    return this.executeWithErrorHandling('findActiveTokensForUser', async () => {
      this.logOperation('findActiveTokensForUser', { userId });

      const now = this.dateTimeUtils.getUtcNow();

      const results = await this.getDb()
        .select()
        .from(PasswordResetTokenTable)
        .where(
          and(
            eq(PasswordResetTokenTable.userId, userId),
            eq(PasswordResetTokenTable.isUsed, false),
            gt(PasswordResetTokenTable.expiresAt, now), // Token not expired
          ),
        )
        .orderBy(PasswordResetTokenTable.createdAt);

      return results as PasswordResetToken[];
    });
  }

  /**
   * Invalidate all active tokens for a user
   * @param userId - User ID
   * @returns Promise<void>
   */
  async invalidateUserTokens(userId: number): Promise<void> {
    return this.executeWithErrorHandling('invalidateUserTokens', async () => {
      this.logOperation('invalidateUserTokens', { userId });

      const auditedUpdates = this.addUpdateAuditFields({ isUsed: true });

      await this.getDb()
        .update(PasswordResetTokenTable)
        .set(auditedUpdates)
        .where(and(eq(PasswordResetTokenTable.userId, userId), eq(PasswordResetTokenTable.isUsed, false)));

      this.logger.debug(`Invalidated all active tokens for user ID: ${userId}`);
    });
  }

  /**
   * Clean up expired tokens (for maintenance/cleanup jobs)
   * @returns Promise<number> - Number of tokens cleaned up
   */
  async cleanupExpiredTokens(): Promise<number> {
    return this.executeWithErrorHandling('cleanupExpiredTokens', async () => {
      this.logOperation('cleanupExpiredTokens');

      const now = this.dateTimeUtils.getUtcNow();

      const result = await this.getDb()
        .delete(PasswordResetTokenTable)
        .where(lt(PasswordResetTokenTable.expiresAt, now))
        .returning({ id: PasswordResetTokenTable.id });

      const cleanedCount = result.length;
      this.logger.log(`Cleaned up ${cleanedCount} expired password reset tokens`);

      return cleanedCount;
    });
  }

  /**
   * Find token by token string (without validation)
   * @param token - Token string
   * @returns Promise<PasswordResetToken | null> - Found token or null
   */
  async findByToken(token: string): Promise<PasswordResetToken | null> {
    return this.executeWithErrorHandling('findByToken', async () => {
      this.logOperation('findByToken', { tokenLength: token.length });

      const results = await this.getDb()
        .select()
        .from(PasswordResetTokenTable)
        .where(eq(PasswordResetTokenTable.token, token))
        .limit(1);

      return (results[0] as PasswordResetToken) || null;
    });
  }

  // ==================== PRIVATE UTILITIES ====================

  /**
   * Generate a cryptographically secure random token
   * @returns string - Secure random token
   * @private
   */
  private generateSecureToken(): string {
    try {
      return crypto.randomBytes(PasswordResetTokenRepository.TOKEN_LENGTH).toString('hex');
    } catch (error) {
      this.logger.error('Token generation failed:', error);
      throw new AuthError(AuthErrorEnum.enum.PASSWORD_RESET_TOKEN_INVALID, 'AUTH', {
        message: 'Failed to generate secure token',
        cause: error,
      });
    }
  }

  // ==================== ERROR HANDLING OVERRIDES ====================

  /**
   * Handle repository errors with auth-specific error mapping
   * @param error - Original error
   * @param operation - Operation that failed
   * @param context - Additional context
   * @throws AuthError or base repository errors
   */
  protected handleError(error: unknown, operation: string, context?: Record<string, unknown>): never {
    this.logger.error(`${operation} failed for password reset tokens:`, error);

    // Handle known auth-specific errors
    if (error instanceof EntityNotFoundError || error instanceof AuthError) {
      throw error;
    }

    // Map database errors to auth errors
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase();

      if (errorMessage.includes('foreign key') && errorMessage.includes('user_id')) {
        throw new AuthError(AuthErrorEnum.enum.PASSWORD_RESET_TOKEN_INVALID, 'AUTH', {
          message: 'Invalid user ID for password reset token',
          cause: error,
        });
      }

      if (errorMessage.includes('duplicate key') && errorMessage.includes('token')) {
        // This should be very rare due to crypto.randomBytes, but handle it
        throw new AuthError(AuthErrorEnum.enum.PASSWORD_RESET_TOKEN_INVALID, 'AUTH', {
          message: 'Token generation collision',
          cause: error,
        });
      }

      if (errorMessage.includes('not null')) {
        throw new AuthError(AuthErrorEnum.enum.PASSWORD_RESET_TOKEN_INVALID, 'AUTH', {
          message: 'Required token field is missing',
          cause: error,
        });
      }
    }

    // Fall back to base error handling
    super.handleError(error, operation, context);
  }
}
