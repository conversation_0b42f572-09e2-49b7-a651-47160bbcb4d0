import { z } from 'zod/v4';
import { emailSchema, baseUpdatableEntitySchema, utcDateTimeSchema } from '../../common/src/schema';
import { passwordSchema } from '../../user/src/user.schema';

// ==================== PASSWORD RESET TOKEN SCHEMA ====================

/**
 * Password Reset Token entity schema
 * Requirements: 4.1, 4.2, 4.3, 8.2 - Password reset token management
 */
export const PasswordResetTokenSchema = z.object({
  ...baseUpdatableEntitySchema.shape,
  userId: z.number().int().positive().describe('User ID associated with the token'),
  token: z.string().min(1).describe('Unique reset token'),
  expiresAt: utcDateTimeSchema.describe('Token expiration timestamp'),
  isUsed: z.boolean().default(false).describe('Whether token has been used'),
});

/**
 * Schema for creating a password reset token
 */
export const CreatePasswordResetTokenSchema = z
  .object({
    userId: z.number().int().positive().describe('User ID for the token'),
    token: z.string().min(1).describe('Unique reset token'),
    expiresAt: utcDateTimeSchema.describe('Token expiration timestamp'),
  })
  .strict();

/**
 * Schema for updating a password reset token (mainly for marking as used)
 */
export const UpdatePasswordResetTokenSchema = z
  .object({
    isUsed: z.boolean().describe('Mark token as used'),
  })
  .strict();

// ==================== AUTH REQUEST/RESPONSE SCHEMAS ====================

/**
 * Login request schema with email and password validation
 * Requirements: 3.1 - User authentication with credentials
 */
export const LoginRequestSchema = z
  .object({
    email: emailSchema.describe('User email address'),
    password: z.string().min(1, 'Password is required').describe('User password'),
    rememberMe: z.boolean().default(false).describe('Remember user session for extended period'),
  })
  .strict();

/**
 * Password reset request schema
 * Requirements: 4.1 - Password reset initiation
 */
export const PasswordResetRequestSchema = z
  .object({
    email: emailSchema.describe('Email address for password reset'),
  })
  .strict();

/**
 * Password reset confirmation schema with token and new password
 * Requirements: 4.2, 4.3 - Password reset with token validation
 */
export const PasswordResetSchema = z
  .object({
    token: z.string().min(1, 'Reset token is required').describe('Password reset token'),
    newPassword: passwordSchema.describe('New password'),
  })
  .strict();

/**
 * Change password schema for authenticated users
 * Requirements: 4.4 - Password change for authenticated users
 */
export const ChangePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required').describe('Current password'),
    newPassword: passwordSchema.describe('New password'),
  })
  .strict()
  .refine((data) => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  });

/**
 * Login response schema
 * Requirements: 3.1 - Authentication success response
 */
export const LoginResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().describe('Success message'),
  user: z
    .object({
      id: z.number().int().positive(),
      email: emailSchema,
      firstName: z.string(),
      lastName: z.string(),
      role: z.enum(['admin', 'user']),
    })
    .describe('Authenticated user information'),
  sessionId: z.string().describe('Session identifier'),
  expiresAt: z.string().describe('Session expiration timestamp'),
});

/**
 * Logout response schema
 * Requirements: 3.3 - Logout confirmation
 */
export const LogoutResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().describe('Logout confirmation message'),
});

/**
 * Password reset request response schema
 * Requirements: 4.1 - Password reset initiation confirmation
 */
export const PasswordResetRequestResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().describe('Password reset request confirmation'),
});

/**
 * Password reset confirmation response schema
 * Requirements: 4.3 - Password reset completion confirmation
 */
export const PasswordResetResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().describe('Password reset completion confirmation'),
});

/**
 * Change password response schema
 * Requirements: 4.4 - Password change confirmation
 */
export const ChangePasswordResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().describe('Password change confirmation'),
});

// ==================== SESSION CONTEXT SCHEMAS ====================

/**
 * Session context schema for authentication
 * Requirements: 6.1, 6.2 - Session management with security context
 */
export const SessionContextSchema = z.object({
  ipAddress: z.string().optional().describe('Client IP address'),
  userAgent: z.string().optional().describe('Client user agent'),
  rememberMe: z.boolean().default(false).describe('Extended session duration'),
});

/**
 * Session validation response schema
 * Requirements: 5.1, 6.1 - Session validation for protected endpoints
 */
export const SessionValidationResponseSchema = z.object({
  isValid: z.boolean().describe('Whether session is valid'),
  session: z
    .object({
      id: z.string(),
      user: z.object({
        id: z.number().int().positive(),
        email: emailSchema,
        role: z.enum(['admin', 'user']),
      }),
      expiresAt: z.string(),
      lastAccessedAt: z.string(),
    })
    .optional()
    .describe('Session data if valid'),
});

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types using z.output

export type PasswordResetToken = z.output<typeof PasswordResetTokenSchema>;
export type CreatePasswordResetTokenRequest = z.output<typeof CreatePasswordResetTokenSchema>;
export type UpdatePasswordResetTokenRequest = z.output<typeof UpdatePasswordResetTokenSchema>;
export type LoginRequest = z.output<typeof LoginRequestSchema>;
export type PasswordResetRequest = z.output<typeof PasswordResetRequestSchema>;
export type PasswordReset = z.output<typeof PasswordResetSchema>;
export type ChangePasswordRequest = z.output<typeof ChangePasswordSchema>;
export type LoginResponse = z.output<typeof LoginResponseSchema>;
export type LogoutResponse = z.output<typeof LogoutResponseSchema>;
export type PasswordResetRequestResponse = z.output<typeof PasswordResetRequestResponseSchema>;
export type PasswordResetResponse = z.output<typeof PasswordResetResponseSchema>;
export type ChangePasswordResponse = z.output<typeof ChangePasswordResponseSchema>;
export type SessionContext = z.output<typeof SessionContextSchema>;
export type SessionValidationResponse = z.output<typeof SessionValidationResponseSchema>;
