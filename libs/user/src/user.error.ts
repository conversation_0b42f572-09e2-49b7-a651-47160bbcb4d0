import { z } from 'zod/v4';
import { BaseError } from '../../common/src/errors/base.error';
import type { ErrorDomainType } from '../../common/src/errors/domain';

/**
 * User-related error types enum
 */
export const UserErrorEnum = z.enum([
  'USER_NOT_FOUND',
  'USER_ALREADY_EXISTS',
  'USER_INACTIVE',
  'INVALID_USER_DATA',
  'USER_CREATION_FAILED',
  'USER_UPDATE_FAILED',
  'USER_DELETION_FAILED',
  'INVALID_PASSWORD',
  'PASSWORD_UPDATE_FAILED',
  'EMAIL_ALREADY_EXISTS',
  'INVALID_USER_ROLE',
]);

/**
 * Error messages mapping for consistent error responses
 */
export const UserErrorMessages: Record<UserErrorEnumType, string> = {
  USER_NOT_FOUND: 'User not found',
  USER_ALREADY_EXISTS: 'User already exists with this email',
  USER_INACTIVE: 'User account is inactive',
  INVALID_USER_DATA: 'Invalid user data provided',
  USER_CREATION_FAILED: 'Failed to create user',
  USER_UPDATE_FAILED: 'Failed to update user',
  USER_DELETION_FAILED: 'Failed to delete user',
  INVALID_PASSWORD: 'Invalid password provided',
  PASSWORD_UPDATE_FAILED: 'Failed to update password',
  EMAIL_ALREADY_EXISTS: 'Email address already exists',
  INVALID_USER_ROLE: 'Invalid user role specified',
};

export type UserErrorEnumType = z.output<typeof UserErrorEnum>;

/**
 * User error class extending BaseError with proper error codes
 * Provides consistent error handling for user-related operations
 */
export class UserError extends BaseError<UserErrorEnumType> {
  constructor(name: UserErrorEnumType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : UserErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
