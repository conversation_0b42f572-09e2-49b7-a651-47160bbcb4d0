import { pgTable, varchar, boolean, text, index } from 'drizzle-orm/pg-core';
import { baseModel } from '@app/common/models';

// ==================== DATABASE SCHEMA ====================

/**
 * User table schema for PatternTrade platform
 * Includes all required fields for user management and authentication
 */
export const UserTable = pgTable(
  'users',
  {
    ...baseModel,
    email: varchar('email', { length: 255 }).notNull().unique(),
    firstName: varchar('first_name', { length: 100 }).notNull(),
    lastName: varchar('last_name', { length: 100 }).notNull(),
    role: varchar('role', { length: 50 }).notNull().default('user'),
    isActive: boolean('is_active').notNull().default(true),
    passwordHash: text('password_hash').notNull(),
    lastLoginAt: varchar('last_login_at', { length: 32 }),
  },
  (table) => [
    // Index for email lookups (authentication)
    index('users_email_idx').on(table.email),
    // Index for role-based queries
    index('users_role_idx').on(table.role),
    // Index for active user queries
    index('users_is_active_idx').on(table.isActive),
    // Composite index for active users by role
    index('users_active_role_idx').on(table.isActive, table.role),
  ],
);
