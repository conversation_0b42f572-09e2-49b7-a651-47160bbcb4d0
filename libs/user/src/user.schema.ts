import { z } from 'zod/v4';
import { baseUpdatableEntitySchema, emailSchema, nameSchema, optionalUtcDateTimeSchema } from '@app/common/schema';

// ==================== USER VALIDATION SCHEMAS ====================

/**
 * User role enum schema
 */
export const UserRoleEnum = z.enum(['admin', 'user']);

/**
 * Password validation schema
 * Requirements: 8-128 characters
 */
export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password cannot exceed 128 characters')
  .describe('User password');

/**
 * Complete User entity schema with all fields
 * Extends base updatable entity with audit fields
 */
export const UserSchema = z.object({
  ...baseUpdatableEntitySchema.shape,
  email: emailSchema.describe('User email address'),
  firstName: nameSchema.describe('User first name'),
  lastName: nameSchema.describe('User last name'),
  role: UserRoleEnum.default('user').describe('User role'),
  isActive: z.boolean().default(true).describe('User account status'),
  passwordHash: z.string().describe('Hashed password'),
  lastLoginAt: optionalUtcDateTimeSchema.describe('Last login timestamp'),
});

/**
 * Schema for creating a new user
 * Includes password field instead of passwordHash
 */
export const CreateUserSchema = z
  .object({
    email: emailSchema.describe('User email address'),
    firstName: nameSchema.describe('User first name'),
    lastName: nameSchema.describe('User last name'),
    role: UserRoleEnum.default('user').describe('User role'),
    password: passwordSchema.describe('User password'),
  })
  .strict();

/**
 * Schema for updating user information
 * All fields are optional and strict validation is applied
 */
export const UpdateUserSchema = z
  .object({
    firstName: nameSchema.optional().describe('User first name'),
    lastName: nameSchema.optional().describe('User last name'),
    role: UserRoleEnum.optional().describe('User role'),
    isActive: z.boolean().optional().describe('User account status'),
  })
  .strict();

/**
 * Schema for user password updates
 */
export const UpdatePasswordSchema = z
  .object({
    password: passwordSchema.describe('New password'),
  })
  .strict();

/**
 * Schema for user search/filter operations
 */
export const UserSearchSchema = z
  .object({
    email: z.string().optional().describe('Filter by email'),
    role: UserRoleEnum.optional().describe('Filter by role'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    search: z.string().optional().describe('Search in name or email'),
  })
  .strict();

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types using z.output

export type UserRole = z.output<typeof UserRoleEnum>;
export type User = z.output<typeof UserSchema>;
export type CreateUserRequest = z.output<typeof CreateUserSchema>;
export type UpdateUserRequest = z.output<typeof UpdateUserSchema>;
export type UpdatePasswordRequest = z.output<typeof UpdatePasswordSchema>;
export type UserSearchRequest = z.output<typeof UserSearchSchema>;
