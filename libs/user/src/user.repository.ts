import { Injectable } from '@nestjs/common';
import { eq, type InferInsertModel } from 'drizzle-orm';
import * as bcrypt from 'bcrypt';
import { BaseRepository } from '@app/common/repository';
import { DrizzleService } from '@app/core/drizzle';
import { UtilsService, DbUtilsService, DateTimeUtilsService } from '@app/utils';
import { UserTable } from './user.model';
import { User, CreateUserRequest, UpdateUserRequest } from './user.schema';
import { UserError, UserErrorEnum } from './user.error';
import { DuplicateEntityError, EntityNotFoundError } from '@app/common/repository';

// Type for UserTable insert operations
type UserInsert = InferInsertModel<typeof UserTable>;

/**
 * User repository providing data access operations for user management
 * Extends BaseRepository with user-specific functionality including password hashing
 */
@Injectable()
export class UserRepository extends BaseRepository<User, CreateUserRequest, UpdateUserRequest> {
  private static readonly SALT_ROUNDS = 12;

  constructor(
    drizzleService: DrizzleService,
    utilsService: UtilsService,
    dbUtils: DbUtilsService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    super(drizzleService, utilsService, dbUtils, 'UserRepository');
  }

  // ==================== ABSTRACT METHOD IMPLEMENTATIONS ====================

  protected getTable() {
    return UserTable;
  }

  protected getTableName(): string {
    return 'users';
  }

  // ==================== USER-SPECIFIC METHODS ====================

  /**
   * Create a new user with password hashing
   * @param data - User creation data with plain password
   * @returns Promise<User> - Created user entity
   * @throws UserError if email already exists or creation fails
   */
  async create(data: CreateUserRequest): Promise<User> {
    return this.executeWithErrorHandling('create', async () => {
      this.logOperation('create', { email: data.email, role: data.role });

      // Check for duplicate email
      const existingUser = await this.findByEmail(data.email);
      if (existingUser) {
        throw new DuplicateEntityError('User', 'email', data.email);
      }

      // Hash password
      const passwordHash = await this.hashPassword(data.password);

      // Prepare data for insertion
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password: _, ...userData } = data;
      const userDataWithHash = {
        ...userData,
        passwordHash,
      };

      const auditedData = this.addCreateAuditFields(userDataWithHash);

      // Insert user
      const [created] = await this.getDb()
        .insert(UserTable)
        .values(auditedData as UserInsert)
        .returning();

      this.logger.log(`Created user with email: ${data.email}`);
      return created as User;
    });
  }

  /**
   * Find user by email address
   * @param email - User email address
   * @returns Promise<User | null> - Found user or null
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.executeWithErrorHandling('findByEmail', async () => {
      this.logOperation('findByEmail', { email });

      const results = await this.getDb().select().from(UserTable).where(eq(UserTable.email, email)).limit(1);

      return (results[0] as User) || null;
    });
  }

  /**
   * Update user password
   * @param userId - User ID
   * @param newPassword - New plain password
   * @returns Promise<void>
   * @throws EntityNotFoundError if user doesn't exist
   */
  async updatePassword(userId: number, newPassword: string): Promise<void> {
    return this.executeWithErrorHandling('updatePassword', async () => {
      this.logOperation('updatePassword', { userId });

      // Validate user exists
      await this.validateEntityExists(userId);

      // Hash new password
      const passwordHash = await this.hashPassword(newPassword);

      // Update password
      const auditedUpdates = this.addUpdateAuditFields({ passwordHash });

      await this.getDb().update(UserTable).set(auditedUpdates).where(eq(UserTable.id, userId));

      this.logger.log(`Updated password for user ID: ${userId}`);
    });
  }

  /**
   * Update user's last login timestamp
   * @param userId - User ID
   * @returns Promise<void>
   * @throws EntityNotFoundError if user doesn't exist
   */
  async updateLastLogin(userId: number): Promise<void> {
    return this.executeWithErrorHandling('updateLastLogin', async () => {
      this.logOperation('updateLastLogin', { userId });

      // Validate user exists
      await this.validateEntityExists(userId);

      // Update last login timestamp
      const auditedUpdates = this.addUpdateAuditFields({
        lastLoginAt: this.dateTimeUtils.getUtcNow(),
      });

      await this.getDb().update(UserTable).set(auditedUpdates).where(eq(UserTable.id, userId));

      this.logger.debug(`Updated last login for user ID: ${userId}`);
    });
  }

  /**
   * Find all active users
   * @returns Promise<User[]> - Array of active users
   */
  async findActiveUsers(): Promise<User[]> {
    return this.executeWithErrorHandling('findActiveUsers', async () => {
      this.logOperation('findActiveUsers');

      const results = await this.getDb()
        .select()
        .from(UserTable)
        .where(eq(UserTable.isActive, true))
        .orderBy(UserTable.createdAt);

      return results as User[];
    });
  }

  /**
   * Find users by role
   * @param role - User role to filter by
   * @returns Promise<User[]> - Array of users with specified role
   */
  async findByRole(role: 'admin' | 'user'): Promise<User[]> {
    return this.executeWithErrorHandling('findByRole', async () => {
      this.logOperation('findByRole', { role });

      const results = await this.getDb()
        .select()
        .from(UserTable)
        .where(eq(UserTable.role, role))
        .orderBy(UserTable.createdAt);

      return results as User[];
    });
  }

  // ==================== PASSWORD UTILITIES ====================

  /**
   * Hash password using bcrypt with configured salt rounds
   * @param password - Plain text password
   * @returns Promise<string> - Hashed password
   * @private
   */
  private async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, UserRepository.SALT_ROUNDS);
    } catch (error) {
      this.logger.error('Password hashing failed:', error);
      throw new UserError(UserErrorEnum.enum.USER_CREATION_FAILED, 'USER', {
        message: 'Failed to hash password',
        cause: error,
      });
    }
  }

  /**
   * Validate password against stored hash
   * @param password - Plain text password
   * @param hash - Stored password hash
   * @returns Promise<boolean> - True if password matches
   */
  async validatePassword(password: string, hash: string): Promise<boolean> {
    return this.executeWithErrorHandling('validatePassword', async () => {
      try {
        return await bcrypt.compare(password, hash);
      } catch (error) {
        this.logger.error('Password validation failed:', error);
        return false;
      }
    });
  }

  // ==================== ERROR HANDLING OVERRIDES ====================

  /**
   * Handle repository errors with user-specific error mapping
   * @param error - Original error
   * @param operation - Operation that failed
   * @param context - Additional context
   * @throws UserError or base repository errors
   */
  protected handleError(error: unknown, operation: string, context?: Record<string, unknown>): never {
    this.logger.error(`${operation} failed for users:`, error);

    // Handle known user-specific errors
    if (error instanceof DuplicateEntityError || error instanceof EntityNotFoundError || error instanceof UserError) {
      throw error;
    }

    // Map database errors to user errors
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase();

      if (errorMessage.includes('duplicate key') && errorMessage.includes('email')) {
        throw new DuplicateEntityError('User', 'email', (context?.email as string) || 'unknown');
      }

      if (errorMessage.includes('foreign key')) {
        throw new UserError(UserErrorEnum.enum.USER_CREATION_FAILED, 'USER', {
          message: 'Foreign key constraint violation',
          cause: error,
        });
      }

      if (errorMessage.includes('not null')) {
        throw new UserError(UserErrorEnum.enum.INVALID_USER_DATA, 'USER', {
          message: 'Required field is missing',
          cause: error,
        });
      }
    }

    // Fall back to base error handling
    super.handleError(error, operation, context);
  }
}
