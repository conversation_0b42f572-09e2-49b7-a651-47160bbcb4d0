import { Injectable } from '@nestjs/common';
import { UserRepository } from './user.repository';
import { User, CreateUserRequest, UpdateUserRequest, UserSearchRequest } from './user.schema';
import { UserError, UserErrorEnum } from './user.error';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';

/**
 * User service interface defining all required methods
 */
export interface IUserService {
  createUser(data: CreateUserRequest): Promise<User>;
  findUserById(id: number): Promise<User | null>;
  findUserByEmail(email: string): Promise<User | null>;
  updateUser(id: number, updates: UpdateUserRequest): Promise<User>;
  deleteUser(id: number): Promise<void>;
  findAllUsers(filters?: UserSearchRequest): Promise<User[]>;
  findUsersPaginated(options: PaginationOptions): Promise<PaginatedResult<User>>;
  validatePassword(user: User, password: string): Promise<boolean>;
  updatePassword(userId: number, newPassword: string): Promise<void>;
  updateLastLogin(userId: number): Promise<void>;
}

/**
 * User service providing business logic for user management operations
 * Implements comprehensive user CRUD operations with validation and security
 */
@Injectable()
export class UserService implements IUserService {
  constructor(private readonly userRepository: UserRepository) {}

  /**
   * Create a new user with password hashing and validation
   * @param data - User creation data with plain password
   * @returns Promise<User> - Created user entity
   * @throws UserError if validation fails or user already exists
   */
  async createUser(data: CreateUserRequest): Promise<User> {
    try {
      // Validate email uniqueness
      const existingUser = await this.userRepository.findByEmail(data.email);
      if (existingUser) {
        throw new UserError(UserErrorEnum.enum.USER_ALREADY_EXISTS, 'USER', {
          message: `User with email ${data.email} already exists`,
        });
      }

      // Create user through repository (handles password hashing)
      const user = await this.userRepository.create(data);

      return user;
    } catch (error) {
      if (error instanceof UserError) {
        throw error;
      }

      throw new UserError(UserErrorEnum.enum.USER_CREATION_FAILED, 'USER', {
        message: 'Failed to create user',
        cause: error,
      });
    }
  }

  /**
   * Find user by ID
   * @param id - User ID
   * @returns Promise<User | null> - Found user or null
   */
  async findUserById(id: number): Promise<User | null> {
    try {
      return await this.userRepository.findById(id);
    } catch (error) {
      throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
        message: `Failed to find user with ID ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Find user by email address
   * @param email - User email address
   * @returns Promise<User | null> - Found user or null
   */
  async findUserByEmail(email: string): Promise<User | null> {
    try {
      return await this.userRepository.findByEmail(email);
    } catch (error) {
      throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
        message: `Failed to find user with email ${email}`,
        cause: error,
      });
    }
  }

  /**
   * Update user information
   * @param id - User ID
   * @param updates - Partial user data for updates
   * @returns Promise<User> - Updated user entity
   * @throws UserError if user not found or update fails
   */
  async updateUser(id: number, updates: UpdateUserRequest): Promise<User> {
    try {
      // Verify user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
          message: `User with ID ${id} not found`,
        });
      }

      // Update user through repository
      const updatedUser = await this.userRepository.update(id, updates);

      return updatedUser;
    } catch (error) {
      if (error instanceof UserError) {
        throw error;
      }

      throw new UserError(UserErrorEnum.enum.USER_UPDATE_FAILED, 'USER', {
        message: `Failed to update user with ID ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Delete user (soft delete by setting isActive to false)
   * @param id - User ID
   * @returns Promise<void>
   * @throws UserError if user not found or deletion fails
   */
  async deleteUser(id: number): Promise<void> {
    try {
      // Verify user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
          message: `User with ID ${id} not found`,
        });
      }

      // Soft delete by deactivating user
      await this.userRepository.update(id, { isActive: false });
    } catch (error) {
      if (error instanceof UserError) {
        throw error;
      }

      throw new UserError(UserErrorEnum.enum.USER_DELETION_FAILED, 'USER', {
        message: `Failed to delete user with ID ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Find all users with optional filtering
   * @param filters - Optional filter criteria
   * @returns Promise<User[]> - Array of users
   */
  async findAllUsers(filters?: UserSearchRequest): Promise<User[]> {
    try {
      if (!filters) {
        return await this.userRepository.findAll();
      }

      // Apply filters based on search criteria
      let users = await this.userRepository.findAll();

      if (filters.email) {
        users = users.filter((user) => user.email.toLowerCase().includes(filters.email!.toLowerCase()));
      }

      if (filters.role) {
        users = users.filter((user) => user.role === filters.role);
      }

      if (filters.isActive !== undefined) {
        users = users.filter((user) => user.isActive === filters.isActive);
      }

      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        users = users.filter(
          (user) =>
            user.email.toLowerCase().includes(searchTerm) ||
            user.firstName.toLowerCase().includes(searchTerm) ||
            user.lastName.toLowerCase().includes(searchTerm),
        );
      }

      return users;
    } catch (error) {
      throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
        message: 'Failed to retrieve users',
        cause: error,
      });
    }
  }

  /**
   * Find users with pagination support
   * @param options - Pagination options
   * @returns Promise<PaginatedResult<User>> - Paginated user results
   */
  async findUsersPaginated(options: PaginationOptions): Promise<PaginatedResult<User>> {
    try {
      // For now, implement basic pagination using findAll and manual slicing
      // This can be optimized later with database-level pagination
      const allUsers = await this.userRepository.findAll(options.filters);

      const limit = options.limit || 10;
      const offset = options.offset || 0;

      // Apply sorting if specified
      if (options.sortBy) {
        allUsers.sort((a, b) => {
          const aValue = a[options.sortBy as keyof User] ?? '';
          const bValue = b[options.sortBy as keyof User] ?? '';

          if (options.sortOrder === 'DESC') {
            return bValue > aValue ? 1 : -1;
          }
          return aValue > bValue ? 1 : -1;
        });
      }

      const total = allUsers.length;
      const data = allUsers.slice(offset, offset + limit);
      const hasMore = offset + limit < total;

      return {
        data,
        total,
        hasMore,
        limit,
        offset,
      };
    } catch (error) {
      throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
        message: 'Failed to retrieve paginated users',
        cause: error,
      });
    }
  }

  /**
   * Validate password against user's stored hash
   * @param user - User entity with password hash
   * @param password - Plain text password to validate
   * @returns Promise<boolean> - True if password matches
   */
  async validatePassword(user: User, password: string): Promise<boolean> {
    try {
      if (!user.isActive) {
        throw new UserError(UserErrorEnum.enum.USER_INACTIVE, 'USER', {
          message: 'User account is inactive',
        });
      }

      return await this.userRepository.validatePassword(password, user.passwordHash);
    } catch (error) {
      if (error instanceof UserError) {
        throw error;
      }

      throw new UserError(UserErrorEnum.enum.INVALID_PASSWORD, 'USER', {
        message: 'Password validation failed',
        cause: error,
      });
    }
  }

  /**
   * Update user password with hashing
   * @param userId - User ID
   * @param newPassword - New plain text password
   * @returns Promise<void>
   * @throws UserError if user not found or password update fails
   */
  async updatePassword(userId: number, newPassword: string): Promise<void> {
    try {
      // Verify user exists
      const existingUser = await this.userRepository.findById(userId);
      if (!existingUser) {
        throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
          message: `User with ID ${userId} not found`,
        });
      }

      if (!existingUser.isActive) {
        throw new UserError(UserErrorEnum.enum.USER_INACTIVE, 'USER', {
          message: 'Cannot update password for inactive user',
        });
      }

      // Update password through repository (handles hashing)
      await this.userRepository.updatePassword(userId, newPassword);
    } catch (error) {
      if (error instanceof UserError) {
        throw error;
      }

      throw new UserError(UserErrorEnum.enum.PASSWORD_UPDATE_FAILED, 'USER', {
        message: `Failed to update password for user ${userId}`,
        cause: error,
      });
    }
  }

  /**
   * Update user's last login timestamp for session management
   * @param userId - User ID
   * @returns Promise<void>
   * @throws UserError if user not found or update fails
   */
  async updateLastLogin(userId: number): Promise<void> {
    try {
      // Verify user exists and is active
      const existingUser = await this.userRepository.findById(userId);
      if (!existingUser) {
        throw new UserError(UserErrorEnum.enum.USER_NOT_FOUND, 'USER', {
          message: `User with ID ${userId} not found`,
        });
      }

      if (!existingUser.isActive) {
        throw new UserError(UserErrorEnum.enum.USER_INACTIVE, 'USER', {
          message: 'Cannot update login time for inactive user',
        });
      }

      // Update last login timestamp through repository
      await this.userRepository.updateLastLogin(userId);
    } catch (error) {
      if (error instanceof UserError) {
        throw error;
      }

      throw new UserError(UserErrorEnum.enum.USER_UPDATE_FAILED, 'USER', {
        message: `Failed to update last login for user ${userId}`,
        cause: error,
      });
    }
  }
}
