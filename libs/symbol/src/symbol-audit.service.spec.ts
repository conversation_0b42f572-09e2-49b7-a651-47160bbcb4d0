import { Test, TestingModule } from '@nestjs/testing';
import { SymbolAuditService } from './symbol-audit.service';
import { DrizzleService } from '@app/core/drizzle';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

describe('SymbolAuditService', () => {
  let service: SymbolAuditService;
  let mockDrizzleService: jest.Mocked<DrizzleService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockErrorUtils: jest.Mocked<ErrorUtilsService>;

  const mockAuditRecord = {
    id: 1,
    operation: 'download_start',
    exchange: 'NSE',
    segment: 'NSE',
    requestId: 'test-request-123',
    jobId: 'job-456',
    status: 'started',
    symbolsProcessed: null,
    symbolsAdded: null,
    symbolsUpdated: null,
    symbolsSkipped: null,
    duration: null,
    errorMessage: null,
    metadata: { forceRefresh: false },
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-15T10:00:00Z'),
  };

  const mockDownloadResult = {
    exchange: 'NSE',
    segment: 'NSE',
    symbolsProcessed: 100,
    symbolsAdded: 50,
    symbolsUpdated: 50,
    symbolsSkipped: 0,
    duration: 30000,
    startedAt: new Date('2024-01-15T10:00:00Z'),
    completedAt: new Date('2024-01-15T10:00:30Z'),
    errors: [],
  };

  beforeEach(async () => {
    mockDrizzleService = {
      db: {
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockAuditRecord]),
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([mockAuditRecord]),
      },
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(() => new Date('2024-01-15T10:00:00Z')),
      getTime: jest.fn(() => Date.now()),
    } as any;

    mockErrorUtils = {
      getErrorMessage: jest.fn((error) => error instanceof Error ? error.message : String(error)),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SymbolAuditService,
        { provide: DrizzleService, useValue: mockDrizzleService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
      ],
    }).compile();

    service = module.get<SymbolAuditService>(SymbolAuditService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('logDownloadStart', () => {
    it('should log download start successfully', async () => {
      const result = await service.logDownloadStart(
        'NSE',
        'NSE',
        'test-request-123',
        'job-456',
        { forceRefresh: false }
      );

      expect(result).toBe(1);
      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'download_start',
          exchange: 'NSE',
          segment: 'NSE',
          requestId: 'test-request-123',
          jobId: 'job-456',
          status: 'started',
          metadata: { forceRefresh: false },
        })
      );
    });

    it('should handle optional parameters', async () => {
      const result = await service.logDownloadStart('ALL', 'ALL', 'test-request');

      expect(result).toBe(1);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'download_start',
          exchange: 'ALL',
          segment: 'ALL',
          requestId: 'test-request',
          jobId: null,
          metadata: {},
        })
      );
    });

    it('should handle database errors', async () => {
      mockDrizzleService.db.returning.mockRejectedValue(new Error('Database error'));

      await expect(
        service.logDownloadStart('NSE', 'NSE', 'test-request')
      ).rejects.toThrow('Database error');
    });
  });

  describe('logDownloadComplete', () => {
    it('should log download completion successfully', async () => {
      await service.logDownloadComplete(1, mockDownloadResult, 'test-request-123');

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'completed',
          symbolsProcessed: 100,
          symbolsAdded: 50,
          symbolsUpdated: 50,
          symbolsSkipped: 0,
          duration: 30000,
          updatedAt: expect.any(Date),
        })
      );
    });

    it('should handle completion with errors', async () => {
      const resultWithErrors = {
        ...mockDownloadResult,
        errors: ['Error 1', 'Error 2'],
      };

      await service.logDownloadComplete(1, resultWithErrors, 'test-request-123');

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'completed_with_errors',
          errorMessage: 'Error 1; Error 2',
        })
      );
    });

    it('should handle update errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Update error'));

      await expect(
        service.logDownloadComplete(1, mockDownloadResult, 'test-request')
      ).rejects.toThrow('Update error');
    });
  });

  describe('logDownloadFailure', () => {
    it('should log download failure successfully', async () => {
      const error = new Error('Download failed');

      await service.logDownloadFailure(
        1,
        error,
        'test-request-123',
        'NSE',
        'NSE',
        30000
      );

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'failed',
          errorMessage: 'Download failed',
          duration: 30000,
          updatedAt: expect.any(Date),
        })
      );
    });

    it('should handle non-Error objects', async () => {
      await service.logDownloadFailure(
        1,
        'String error',
        'test-request-123',
        'NSE',
        'NSE',
        30000
      );

      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(
        expect.objectContaining({
          errorMessage: 'String error',
        })
      );
    });

    it('should handle failure logging errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Logging error'));

      await expect(
        service.logDownloadFailure(1, new Error('Test'), 'test-request', 'NSE', 'NSE', 1000)
      ).rejects.toThrow('Logging error');
    });
  });

  describe('logSymbolOperation', () => {
    it('should log symbol operation successfully', async () => {
      const result = await service.logSymbolOperation(
        'symbol_upsert',
        'NSE',
        'NSE',
        'manual-operation',
        'job-123',
        { symbolCount: 1 }
      );

      expect(result).toBe(1);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'symbol_upsert',
          exchange: 'NSE',
          segment: 'NSE',
          requestId: 'manual-operation',
          jobId: 'job-123',
          status: 'completed',
          metadata: { symbolCount: 1 },
        })
      );
    });

    it('should handle optional parameters', async () => {
      const result = await service.logSymbolOperation('symbol_search', 'ALL', 'ALL');

      expect(result).toBe(1);
      expect(mockDrizzleService.db.values).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'symbol_search',
          exchange: 'ALL',
          segment: 'ALL',
          requestId: null,
          jobId: null,
          metadata: {},
        })
      );
    });
  });

  describe('getAuditLogsByRequestId', () => {
    it('should retrieve audit logs by request ID', async () => {
      const mockLogs = [mockAuditRecord];
      mockDrizzleService.db.execute.mockResolvedValue(mockLogs);

      const result = await service.getAuditLogsByRequestId('test-request-123');

      expect(result).toEqual(mockLogs);
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.orderBy).toHaveBeenCalled();
    });

    it('should handle empty results', async () => {
      mockDrizzleService.db.execute.mockResolvedValue([]);

      const result = await service.getAuditLogsByRequestId('non-existent');

      expect(result).toEqual([]);
    });

    it('should handle query errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Query error'));

      await expect(
        service.getAuditLogsByRequestId('test-request')
      ).rejects.toThrow('Query error');
    });
  });

  describe('getAuditLogsByJobId', () => {
    it('should retrieve audit logs by job ID', async () => {
      const mockLogs = [mockAuditRecord];
      mockDrizzleService.db.execute.mockResolvedValue(mockLogs);

      const result = await service.getAuditLogsByJobId('job-456');

      expect(result).toEqual(mockLogs);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
    });

    it('should handle job ID query errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Job query error'));

      await expect(
        service.getAuditLogsByJobId('job-456')
      ).rejects.toThrow('Job query error');
    });
  });

  describe('getAuditLogsByDateRange', () => {
    it('should retrieve audit logs by date range', async () => {
      const startDate = new Date('2024-01-15T00:00:00Z');
      const endDate = new Date('2024-01-15T23:59:59Z');
      const mockLogs = [mockAuditRecord];
      mockDrizzleService.db.execute.mockResolvedValue(mockLogs);

      const result = await service.getAuditLogsByDateRange(startDate, endDate, 10, 0);

      expect(result).toEqual(mockLogs);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.limit).toHaveBeenCalledWith(10);
      expect(mockDrizzleService.db.offset).toHaveBeenCalledWith(0);
    });

    it('should use default pagination', async () => {
      const startDate = new Date('2024-01-15T00:00:00Z');
      const endDate = new Date('2024-01-15T23:59:59Z');
      mockDrizzleService.db.execute.mockResolvedValue([]);

      await service.getAuditLogsByDateRange(startDate, endDate);

      expect(mockDrizzleService.db.limit).toHaveBeenCalledWith(100);
      expect(mockDrizzleService.db.offset).toHaveBeenCalledWith(0);
    });
  });

  describe('getAuditStats', () => {
    it('should return audit statistics', async () => {
      const mockStats = [
        { operation: 'download_start', status: 'completed', count: 10 },
        { operation: 'download_start', status: 'failed', count: 2 },
        { operation: 'symbol_upsert', status: 'completed', count: 50 },
      ];
      mockDrizzleService.db.execute.mockResolvedValue(mockStats);

      const result = await service.getAuditStats();

      expect(result).toEqual(mockStats);
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
    });

    it('should handle stats query errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Stats error'));

      await expect(service.getAuditStats()).rejects.toThrow('Stats error');
    });
  });

  describe('cleanupOldAuditLogs', () => {
    it('should cleanup old audit logs', async () => {
      const mockResult = { rowsAffected: 25 };
      mockDrizzleService.db.execute.mockResolvedValue(mockResult);

      const result = await service.cleanupOldAuditLogs(30);

      expect(result).toBe(25);
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
    });

    it('should use default retention days', async () => {
      const mockResult = { rowsAffected: 10 };
      mockDrizzleService.db.execute.mockResolvedValue(mockResult);

      const result = await service.cleanupOldAuditLogs();

      expect(result).toBe(10);
    });

    it('should handle cleanup errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Cleanup error'));

      await expect(service.cleanupOldAuditLogs(30)).rejects.toThrow('Cleanup error');
    });
  });

  describe('getHealthStatus', () => {
    it('should return healthy status', async () => {
      // Mock successful database connection
      mockDrizzleService.db.execute.mockResolvedValue([{ count: 100 }]);

      const result = await service.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: true,
        recordCount: 100,
        lastCheck: expect.any(Date),
        issues: [],
      });
    });

    it('should return unhealthy status on database errors', async () => {
      mockDrizzleService.db.execute.mockRejectedValue(new Error('Database connection failed'));

      const result = await service.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: false,
        recordCount: 0,
        lastCheck: expect.any(Date),
        issues: ['Database connection failed'],
      });
    });

    it('should detect high error rates', async () => {
      // Mock high error rate scenario
      const mockStats = [
        { operation: 'download_start', status: 'completed', count: 10 },
        { operation: 'download_start', status: 'failed', count: 15 }, // High failure rate
      ];
      mockDrizzleService.db.execute
        .mockResolvedValueOnce([{ count: 100 }]) // Record count
        .mockResolvedValueOnce(mockStats); // Stats

      const result = await service.getHealthStatus();

      expect(result.isHealthy).toBe(false);
      expect(result.issues).toContain('High failure rate detected: 60.00%');
    });

    it('should detect stale data', async () => {
      // Mock old last record
      const oldDate = new Date('2024-01-01T00:00:00Z');
      mockDrizzleService.db.execute
        .mockResolvedValueOnce([{ count: 100 }]) // Record count
        .mockResolvedValueOnce([]) // No recent stats
        .mockResolvedValueOnce([{ created_at: oldDate }]); // Old last record

      const result = await service.getHealthStatus();

      expect(result.isHealthy).toBe(false);
      expect(result.issues).toContain('No recent audit activity detected');
    });
  });

  describe('error handling', () => {
    it('should handle database connection failures gracefully', async () => {
      mockDrizzleService.db.insert.mockImplementation(() => {
        throw new Error('Connection lost');
      });

      await expect(
        service.logDownloadStart('NSE', 'NSE', 'test-request')
      ).rejects.toThrow('Connection lost');
    });

    it('should handle malformed data gracefully', async () => {
      const invalidResult = {
        ...mockDownloadResult,
        symbolsProcessed: 'invalid', // Should be number
      };

      // Should not throw, but handle gracefully
      await service.logDownloadComplete(1, invalidResult as any, 'test-request');

      expect(mockDrizzleService.db.set).toHaveBeenCalled();
    });

    it('should preserve error context in logs', async () => {
      const error = new Error('Detailed error message');
      error.stack = 'Error stack trace';

      await service.logDownloadFailure(1, error, 'test-request', 'NSE', 'NSE', 1000);

      expect(mockErrorUtils.getErrorMessage).toHaveBeenCalledWith(error);
      expect(mockDrizzleService.db.set).toHaveBeenCalledWith(
        expect.objectContaining({
          errorMessage: 'Detailed error message',
        })
      );
    });
  });
});
