import { Test, TestingModule } from '@nestjs/testing';
import { SymbolMasterRepository } from './symbol-master.repository';
import { QuestDBService } from '@app/core/questdb';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { SymbolMasterQuestDBTable } from './symbol-master.questdb';

describe('SymbolMasterRepository', () => {
  let repository: SymbolMasterRepository;
  let mockQuestDBService: jest.Mocked<QuestDBService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockErrorUtils: jest.Mocked<ErrorUtilsService>;

  const mockSymbolData = {
    instrumentToken: '738561',
    exchangeToken: '2885',
    tradingSymbol: 'RELIANCE',
    name: 'Reliance Industries Limited',
    lastPrice: 2450.50,
    expiry: undefined,
    strike: undefined,
    tickSize: 0.05,
    lotSize: 1,
    instrumentType: 'EQ' as const,
    segment: 'NSE' as const,
    exchange: 'NSE' as const,
    isActive: true,
  };

  const mockQuestDBRow = {
    instrument_token: '738561',
    exchange_token: '2885',
    trading_symbol: 'RELIANCE',
    name: 'Reliance Industries Limited',
    last_price: 2450.50,
    expiry: null,
    strike: null,
    tick_size: 0.05,
    lot_size: 1,
    instrument_type: 'EQ',
    segment: 'NSE',
    exchange: 'NSE',
    is_active: true,
    downloaded_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  };

  beforeEach(async () => {
    mockQuestDBService = {
      executeQuery: jest.fn(),
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(() => new Date('2024-01-15T10:00:00Z')),
      getTime: jest.fn(() => Date.now()),
    } as any;

    mockErrorUtils = {
      getErrorMessage: jest.fn((error) => error instanceof Error ? error.message : String(error)),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SymbolMasterRepository,
        { provide: QuestDBService, useValue: mockQuestDBService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
      ],
    }).compile();

    repository = module.get<SymbolMasterRepository>(SymbolMasterRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should initialize table successfully', async () => {
      // Mock table initialization
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 0, data: [] }) // CREATE TABLE
        .mockResolvedValueOnce({ rowCount: 1, data: [{ table_name: 'symbol_master' }] }) // Table exists check
        .mockResolvedValueOnce({ rowCount: 16, data: [] }); // Column check

      await repository.onModuleInit();

      expect(mockQuestDBService.executeQuery).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockQuestDBService.executeQuery.mockRejectedValue(new Error('Database error'));

      await expect(repository.onModuleInit()).rejects.toThrow('Database error');
    });
  });

  describe('upsertSymbol', () => {
    it('should upsert a new symbol successfully', async () => {
      // Mock existing symbol check (not found)
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 0, data: [] }) // findByInstrumentToken
        .mockResolvedValueOnce({ rowCount: 1, data: [] }); // upsert

      const result = await repository.upsertSymbol(mockSymbolData);

      expect(result).toEqual({ success: true, isNew: true });
      expect(mockQuestDBService.executeQuery).toHaveBeenCalledTimes(2);
    });

    it('should upsert an existing symbol successfully', async () => {
      // Mock existing symbol check (found)
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 1, data: [mockQuestDBRow] }) // findByInstrumentToken
        .mockResolvedValueOnce({ rowCount: 1, data: [] }); // upsert

      const result = await repository.upsertSymbol(mockSymbolData);

      expect(result).toEqual({ success: true, isNew: false });
    });

    it('should handle validation errors', async () => {
      const invalidData = { ...mockSymbolData, instrumentToken: '' };

      await expect(repository.upsertSymbol(invalidData)).rejects.toThrow();
    });

    it('should handle database errors', async () => {
      mockQuestDBService.executeQuery.mockRejectedValue(new Error('Database error'));

      await expect(repository.upsertSymbol(mockSymbolData)).rejects.toThrow();
    });
  });

  describe('batchUpsertSymbols', () => {
    it('should process batch of symbols successfully', async () => {
      const symbols = [mockSymbolData, { ...mockSymbolData, instrumentToken: '999999' }];

      // Mock successful upserts
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 0, data: [] }) // First symbol check
        .mockResolvedValueOnce({ rowCount: 1, data: [] }) // First symbol upsert
        .mockResolvedValueOnce({ rowCount: 0, data: [] }) // Second symbol check
        .mockResolvedValueOnce({ rowCount: 1, data: [] }); // Second symbol upsert

      const result = await repository.batchUpsertSymbols(symbols, 2);

      expect(result).toMatchObject({
        totalProcessed: 2,
        totalAdded: 2,
        totalUpdated: 0,
        errors: [],
      });
    });

    it('should handle partial failures in batch', async () => {
      const symbols = [mockSymbolData, { ...mockSymbolData, instrumentToken: '999999' }];

      // Mock first success, second failure
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 0, data: [] }) // First symbol check
        .mockResolvedValueOnce({ rowCount: 1, data: [] }) // First symbol upsert
        .mockRejectedValueOnce(new Error('Database error')); // Second symbol fails

      const result = await repository.batchUpsertSymbols(symbols, 2);

      expect(result.totalProcessed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });

    it('should process symbols in batches', async () => {
      const symbols = Array(5).fill(mockSymbolData).map((s, i) => ({
        ...s,
        instrumentToken: `${i}`,
      }));

      // Mock all successful
      mockQuestDBService.executeQuery.mockResolvedValue({ rowCount: 1, data: [] });

      const result = await repository.batchUpsertSymbols(symbols, 2);

      expect(result.totalProcessed).toBe(5);
      // Should be called 10 times (2 calls per symbol: check + upsert)
      expect(mockQuestDBService.executeQuery).toHaveBeenCalledTimes(10);
    });
  });

  describe('findByInstrumentToken', () => {
    it('should find symbol by instrument token', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        rowCount: 1,
        data: [mockQuestDBRow],
      });

      const result = await repository.findByInstrumentToken('738561');

      expect(result).toMatchObject({
        instrumentToken: '738561',
        tradingSymbol: 'RELIANCE',
        exchange: 'NSE',
      });
    });

    it('should return null when symbol not found', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        rowCount: 0,
        data: [],
      });

      const result = await repository.findByInstrumentToken('999999');

      expect(result).toBeNull();
    });

    it('should handle database errors', async () => {
      mockQuestDBService.executeQuery.mockRejectedValue(new Error('Database error'));

      await expect(repository.findByInstrumentToken('738561')).rejects.toThrow();
    });
  });

  describe('findSymbols', () => {
    it('should find symbols with filters', async () => {
      const filters = {
        exchange: 'NSE' as const,
        segment: 'NSE' as const,
        limit: 10,
        offset: 0,
      };

      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 1, data: [mockQuestDBRow] }) // Main query
        .mockResolvedValueOnce({ rowCount: 1, data: [{ total: 100 }] }); // Count query

      const result = await repository.findSymbols(filters);

      expect(result).toMatchObject({
        data: expect.arrayContaining([
          expect.objectContaining({
            instrumentToken: '738561',
            tradingSymbol: 'RELIANCE',
          }),
        ]),
        total: 100,
        hasMore: true,
      });
    });

    it('should handle empty results', async () => {
      const filters = { limit: 10, offset: 0 };

      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({ rowCount: 0, data: [] }) // Main query
        .mockResolvedValueOnce({ rowCount: 1, data: [{ total: 0 }] }); // Count query

      const result = await repository.findSymbols(filters);

      expect(result).toMatchObject({
        data: [],
        total: 0,
        hasMore: false,
      });
    });

    it('should validate filters', async () => {
      const invalidFilters = { limit: -1 };

      await expect(repository.findSymbols(invalidFilters)).rejects.toThrow();
    });
  });

  describe('searchSymbols', () => {
    it('should search symbols by trading symbol', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        rowCount: 1,
        data: [mockQuestDBRow],
      });

      const result = await repository.searchSymbols('RELIANCE', 10, 0);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        instrumentToken: '738561',
        tradingSymbol: 'RELIANCE',
      });
    });

    it('should handle search with no results', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        rowCount: 0,
        data: [],
      });

      const result = await repository.searchSymbols('NONEXISTENT', 10, 0);

      expect(result).toHaveLength(0);
    });
  });

  describe('getStats', () => {
    it('should return repository statistics', async () => {
      const mockStats = {
        totalRecords: 1000,
        activeRecords: 950,
        lastUpdate: new Date('2024-01-15T08:00:00Z'),
        exchangeBreakdown: { NSE: 500, BSE: 450 },
        segmentBreakdown: { NSE: 500, BSE: 450 },
      };

      // Mock the table manager's getTableStats method
      jest.spyOn(repository as any, 'tableManager', 'get').mockReturnValue({
        getTableStats: jest.fn().mockResolvedValue(mockStats),
      });

      const result = await repository.getStats();

      expect(result).toEqual(mockStats);
    });

    it('should handle stats errors', async () => {
      jest.spyOn(repository as any, 'tableManager', 'get').mockReturnValue({
        getTableStats: jest.fn().mockRejectedValue(new Error('Stats error')),
      });

      await expect(repository.getStats()).rejects.toThrow();
    });
  });

  describe('getHealthStatus', () => {
    it('should return healthy status', async () => {
      jest.spyOn(repository as any, 'tableManager', 'get').mockReturnValue({
        validateTable: jest.fn().mockResolvedValue(true),
      });

      mockQuestDBService.executeQuery.mockResolvedValue({
        rowCount: 1,
        data: [{ count: 1000 }],
      });

      const result = await repository.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: true,
        tableExists: true,
        connectionStatus: true,
        recordCount: expect.any(Number),
      });
    });

    it('should return unhealthy status on errors', async () => {
      jest.spyOn(repository as any, 'tableManager', 'get').mockReturnValue({
        validateTable: jest.fn().mockRejectedValue(new Error('Validation error')),
      });

      const result = await repository.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: false,
        tableExists: false,
        connectionStatus: false,
        recordCount: 0,
      });
    });
  });

  describe('cleanupOldData', () => {
    it('should cleanup old data successfully', async () => {
      jest.spyOn(repository as any, 'tableManager', 'get').mockReturnValue({
        cleanupOldData: jest.fn().mockResolvedValue(50),
      });

      const result = await repository.cleanupOldData(30);

      expect(result).toBe(50);
    });

    it('should handle cleanup errors', async () => {
      jest.spyOn(repository as any, 'tableManager', 'get').mockReturnValue({
        cleanupOldData: jest.fn().mockRejectedValue(new Error('Cleanup error')),
      });

      await expect(repository.cleanupOldData(30)).rejects.toThrow();
    });
  });
});
