import { Injectable, Logger } from '@nestjs/common';
import { Sender } from '@questdb/nodejs-client';
import { EnvService } from '@app/core/env';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { KiteInstrumentMasterRaw, KiteInstrumentMasterRawSchema } from './symbol.schema';

/**
 * Kite Instruments API Integration Service
 *
 * Handles fetching instrument master data from Zerodha Kite Connect API
 * and bulk insertion into QuestDB using the official QuestDB Node.js client.
 *
 * Features:
 * - Fetches CSV data from Kite instruments API
 * - Parses and validates instrument data
 * - Bulk insert operations using QuestDB Node.js client
 * - Error handling and retry logic
 * - Progress tracking and logging
 */
@Injectable()
export class KiteInstrumentsService {
  private readonly logger = new Logger(KiteInstrumentsService.name);
  private readonly KITE_INSTRUMENTS_URL = 'https://api.kite.trade/instruments';
  private readonly BATCH_SIZE = 1000;

  constructor(
    private readonly envService: EnvService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Fetch and process all instruments from Kite API
   */
  async fetchAndStoreInstruments(): Promise<{
    totalFetched: number;
    totalInserted: number;
    errors: string[];
    duration: number;
  }> {
    const startTime = this.dateTimeUtils.getTime();
    const errors: string[] = [];
    let totalFetched = 0;
    let totalInserted = 0;

    try {
      this.logger.log('Starting Kite instruments fetch and store operation');

      // Fetch CSV data from Kite API
      const csvData = await this.fetchInstrumentsCSV();
      this.logger.log(`Fetched CSV data: ${csvData.length} characters`);

      // Parse CSV data
      const instruments = this.parseInstrumentsCSV(csvData);
      totalFetched = instruments.length;
      this.logger.log(`Parsed ${totalFetched} instruments from CSV`);

      // Bulk insert into QuestDB
      totalInserted = await this.bulkInsertInstruments(instruments);
      this.logger.log(`Successfully inserted ${totalInserted} instruments`);

      const duration = this.dateTimeUtils.getTime() - startTime;

      return {
        totalFetched,
        totalInserted,
        errors,
        duration,
      };
    } catch (error) {
      const errorMessage = this.errorUtils.getErrorMessage(error);
      errors.push(errorMessage);

      this.logger.error('Failed to fetch and store instruments', {
        error: errorMessage,
        totalFetched,
        totalInserted,
      });

      const duration = this.dateTimeUtils.getTime() - startTime;

      return {
        totalFetched,
        totalInserted,
        errors,
        duration,
      };
    }
  }

  /**
   * Fetch instruments CSV data from Kite API
   */
  private async fetchInstrumentsCSV(): Promise<string> {
    try {
      this.logger.log('Fetching instruments CSV from Kite API');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await fetch(this.KITE_INSTRUMENTS_URL, {
        method: 'GET',
        headers: {
          'User-Agent': 'PatternTrade-API/1.0.0',
          Accept: 'text/csv',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const csvData = await response.text();

      if (!csvData || csvData.length === 0) {
        throw new Error('Empty response from Kite instruments API');
      }

      this.logger.log('Successfully fetched instruments CSV', {
        size: csvData.length,
        contentType: response.headers.get('content-type'),
      });

      return csvData;
    } catch (error) {
      this.logger.error('Failed to fetch instruments CSV', {
        url: this.KITE_INSTRUMENTS_URL,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Parse CSV data into instrument objects
   */
  private parseInstrumentsCSV(csvData: string): KiteInstrumentMasterRaw[] {
    try {
      this.logger.log('Parsing instruments CSV data');

      const lines = csvData.trim().split('\n');
      const headers = lines[0].split(',');

      this.logger.debug('CSV headers', { headers, totalLines: lines.length });

      const instruments: KiteInstrumentMasterRaw[] = [];
      const errors: string[] = [];

      // Process each line (skip header)
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = this.parseCSVLine(lines[i]);

          if (values.length !== headers.length) {
            errors.push(`Line ${i + 1}: Column count mismatch`);
            continue;
          }

          // Map CSV values to instrument object
          const instrument = this.mapCSVToInstrument(headers, values);

          // Validate with Zod schema
          const validatedInstrument = KiteInstrumentMasterRawSchema.parse(instrument);
          instruments.push(validatedInstrument);
        } catch (error) {
          errors.push(`Line ${i + 1}: ${this.errorUtils.getErrorMessage(error)}`);
        }
      }

      if (errors.length > 0) {
        this.logger.warn(`Parsing completed with ${errors.length} errors`, {
          totalLines: lines.length - 1,
          successfullyParsed: instruments.length,
          errorSample: errors.slice(0, 5),
        });
      }

      this.logger.log('CSV parsing completed', {
        totalLines: lines.length - 1,
        successfullyParsed: instruments.length,
        errors: errors.length,
      });

      return instruments;
    } catch (error) {
      this.logger.error('Failed to parse instruments CSV', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Parse a single CSV line handling quoted values
   */
  private parseCSVLine(line: string): string[] {
    const values: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    values.push(current.trim());
    return values;
  }

  /**
   * Map CSV headers and values to instrument object
   */
  private mapCSVToInstrument(headers: string[], values: string[]): Partial<KiteInstrumentMasterRaw> {
    const instrument: Record<string, unknown> = {};

    for (let i = 0; i < headers.length; i++) {
      const header = headers[i].toLowerCase().trim();
      const value = values[i]?.trim() || '';

      // Map CSV headers to our schema fields
      switch (header) {
        case 'instrument_token':
          instrument.instrument_token = value;
          break;
        case 'exchange_token':
          instrument.exchange_token = value;
          break;
        case 'tradingsymbol':
          instrument.tradingsymbol = value;
          break;
        case 'name':
          instrument.name = value;
          break;
        case 'last_price':
          instrument.last_price = value ? parseFloat(value) : 0;
          break;
        case 'expiry':
          instrument.expiry = value || null;
          break;
        case 'strike':
          instrument.strike = value ? parseFloat(value) : null;
          break;
        case 'tick_size':
          instrument.tick_size = value ? parseFloat(value) : 0;
          break;
        case 'lot_size':
          instrument.lot_size = value ? parseInt(value) : 0;
          break;
        case 'instrument_type':
          instrument.instrument_type = value;
          break;
        case 'segment':
          instrument.segment = value;
          break;
        case 'exchange':
          instrument.exchange = value;
          break;
      }
    }

    return instrument;
  }

  /**
   * Bulk insert instruments using QuestDB Node.js client
   */
  private async bulkInsertInstruments(instruments: KiteInstrumentMasterRaw[]): Promise<number> {
    let sender: Sender | null = null;
    let totalInserted = 0;

    try {
      this.logger.log('Starting bulk insert operation', {
        totalInstruments: instruments.length,
        batchSize: this.BATCH_SIZE,
      });

      // Create QuestDB sender
      const questdbHost = this.envService.get('QUESTDB_HOST') || 'localhost';
      const questdbPort = this.envService.get('QUESTDB_ILP_PORT') || 9009; // ILP port for bulk inserts

      sender = Sender.fromConfig(`http::addr=${questdbHost}:${questdbPort};`);

      const now = this.dateTimeUtils.getUtcNow();
      const timestamp = new Date(now).getTime() * 1000; // Microseconds

      // Process in batches
      for (let i = 0; i < instruments.length; i += this.BATCH_SIZE) {
        const batch = instruments.slice(i, i + this.BATCH_SIZE);

        this.logger.debug(`Processing batch ${Math.floor(i / this.BATCH_SIZE) + 1}`, {
          batchStart: i,
          batchSize: batch.length,
        });

        // Add each instrument to the sender
        for (const instrument of batch) {
          await sender
            .table('symbol_master')
            .stringColumn('instrument_token', instrument.instrument_token)
            .stringColumn('exchange_token', instrument.exchange_token)
            .stringColumn('trading_symbol', instrument.tradingsymbol)
            .stringColumn('name', instrument.name || '')
            .floatColumn('last_price', instrument.last_price || 0)
            .floatColumn('tick_size', instrument.tick_size || 0)
            .intColumn('lot_size', instrument.lot_size || 0)
            .stringColumn('expiry', instrument.expiry || '')
            .floatColumn('strike', instrument.strike || 0)
            .stringColumn('instrument_type', instrument.instrument_type)
            .stringColumn('segment', instrument.segment)
            .stringColumn('exchange', instrument.exchange)
            .booleanColumn('is_active', true)
            .stringColumn('downloaded_at', now)
            .stringColumn('updated_at', now)
            .timestampColumn('timestamp', timestamp)
            .at(timestamp);
        }

        // Flush the batch
        await sender.flush();
        totalInserted += batch.length;

        this.logger.debug(`Batch inserted successfully`, {
          batchNumber: Math.floor(i / this.BATCH_SIZE) + 1,
          batchSize: batch.length,
          totalInserted,
        });
      }

      this.logger.log('Bulk insert completed successfully', {
        totalInserted,
        totalBatches: Math.ceil(instruments.length / this.BATCH_SIZE),
      });

      return totalInserted;
    } catch (error) {
      this.logger.error('Failed to bulk insert instruments', {
        error: this.errorUtils.getErrorMessage(error),
        totalInserted,
      });
      throw error;
    } finally {
      // Always close the sender
      if (sender) {
        try {
          await sender.close();
        } catch (error) {
          this.logger.warn('Failed to close QuestDB sender', {
            error: this.errorUtils.getErrorMessage(error),
          });
        }
      }
    }
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean;
    lastFetch?: Date;
    apiStatus: 'available' | 'unavailable' | 'unknown';
    error?: string;
  }> {
    try {
      // Test API availability with a HEAD request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(this.KITE_INSTRUMENTS_URL, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      return {
        isHealthy: response.ok,
        apiStatus: response.ok ? 'available' : 'unavailable',
        lastFetch: new Date(),
      };
    } catch (error) {
      return {
        isHealthy: false,
        apiStatus: 'unavailable',
        error: this.errorUtils.getErrorMessage(error),
      };
    }
  }
}
