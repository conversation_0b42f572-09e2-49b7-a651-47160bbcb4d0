import { Test, TestingModule } from '@nestjs/testing';
import { SymbolModule } from './symbol.module';
import { SymbolService } from './symbol.service';
import { SymbolMasterRepository } from './symbol-master.repository';
import { SymbolDownloadQueueService } from './symbol-download.queue';
import { SymbolDownloadWorkerService } from './symbol-download.worker';
import { SymbolAuditService } from './symbol-audit.service';
import { CoreModule } from '@app/core';
import { UtilsModule } from '@app/utils';
import { CommonModule } from '@app/common';

/**
 * Integration Tests for Symbol Module
 * 
 * These tests verify the end-to-end functionality of the symbol module
 * including service interactions, data flow, and microservice communication.
 * 
 * Note: These tests require actual database connections and Redis for full integration.
 * For CI/CD environments, consider using test containers or mocked services.
 */
describe('Symbol Module Integration', () => {
  let module: TestingModule;
  let symbolService: SymbolService;
  let symbolRepository: SymbolMasterRepository;
  let queueService: SymbolDownloadQueueService;
  let workerService: SymbolDownloadWorkerService;
  let auditService: SymbolAuditService;

  beforeAll(async () => {
    // Create a test module with all dependencies
    module = await Test.createTestingModule({
      imports: [
        CoreModule,
        UtilsModule,
        CommonModule,
        SymbolModule,
      ],
    }).compile();

    // Get service instances
    symbolService = module.get<SymbolService>(SymbolService);
    symbolRepository = module.get<SymbolMasterRepository>(SymbolMasterRepository);
    queueService = module.get<SymbolDownloadQueueService>(SymbolDownloadQueueService);
    workerService = module.get<SymbolDownloadWorkerService>(SymbolDownloadWorkerService);
    auditService = module.get<SymbolAuditService>(SymbolAuditService);

    // Initialize services
    await module.init();
  });

  afterAll(async () => {
    await module.close();
  });

  describe('End-to-End Symbol Download Workflow', () => {
    it('should complete full download workflow', async () => {
      // This test requires actual Kite Connect API access
      // Skip in CI environments or when API credentials are not available
      if (!process.env.ZERODHA_API_KEY) {
        console.log('Skipping integration test - ZERODHA_API_KEY not provided');
        return;
      }

      // 1. Trigger download job
      const downloadRequest = await queueService.addSymbolDownloadJob('NSE', 'NSE', {
        forceRefresh: true,
        batchSize: 10, // Small batch for testing
        requestId: 'integration-test-' + Date.now(),
      });

      expect(downloadRequest.jobId).toBeDefined();
      expect(downloadRequest.requestId).toBeDefined();

      // 2. Wait for job to be processed (with timeout)
      let jobStatus = null;
      let attempts = 0;
      const maxAttempts = 30; // 30 seconds timeout

      while (attempts < maxAttempts) {
        jobStatus = await queueService.getJobStatus(downloadRequest.jobId);
        
        if (jobStatus && (jobStatus.state === 'completed' || jobStatus.state === 'failed')) {
          break;
        }

        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        attempts++;
      }

      // 3. Verify job completion
      expect(jobStatus).toBeDefined();
      expect(jobStatus.state).toBe('completed');
      expect(jobStatus.progress).toBe(100);

      // 4. Verify symbols were stored in repository
      const symbols = await symbolRepository.findSymbols({
        exchange: 'NSE',
        limit: 5,
        offset: 0,
      });

      expect(symbols.data.length).toBeGreaterThan(0);
      expect(symbols.total).toBeGreaterThan(0);

      // 5. Verify audit logs were created
      const auditLogs = await auditService.getAuditLogsByRequestId(downloadRequest.requestId);
      
      expect(auditLogs.length).toBeGreaterThanOrEqual(2); // Start and complete logs
      expect(auditLogs.some(log => log.operation === 'download_start')).toBe(true);
      expect(auditLogs.some(log => log.status === 'completed')).toBe(true);

      // 6. Test symbol retrieval
      const firstSymbol = symbols.data[0];
      const retrievedSymbol = await symbolRepository.findByInstrumentToken(firstSymbol.instrumentToken);
      
      expect(retrievedSymbol).toBeDefined();
      expect(retrievedSymbol.instrumentToken).toBe(firstSymbol.instrumentToken);
    }, 60000); // 60 second timeout for full workflow

    it('should handle download failures gracefully', async () => {
      // Test with invalid exchange to trigger failure
      const downloadRequest = await queueService.addSymbolDownloadJob('INVALID', 'INVALID', {
        requestId: 'integration-test-failure-' + Date.now(),
      });

      // Wait for job to fail
      let jobStatus = null;
      let attempts = 0;
      const maxAttempts = 15;

      while (attempts < maxAttempts) {
        jobStatus = await queueService.getJobStatus(downloadRequest.jobId);
        
        if (jobStatus && jobStatus.state === 'failed') {
          break;
        }

        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      }

      // Verify failure was handled properly
      expect(jobStatus).toBeDefined();
      expect(jobStatus.state).toBe('failed');
      expect(jobStatus.failedReason).toBeDefined();

      // Verify audit logs captured the failure
      const auditLogs = await auditService.getAuditLogsByRequestId(downloadRequest.requestId);
      expect(auditLogs.some(log => log.status === 'failed')).toBe(true);
    }, 30000);
  });

  describe('Service Health and Monitoring', () => {
    it('should report healthy status when all services are operational', async () => {
      const healthStatus = await symbolService.getHealthStatus();

      expect(healthStatus).toMatchObject({
        isHealthy: expect.any(Boolean),
        repositoryHealth: expect.any(Object),
        lastUpdate: expect.any(Date),
      });

      // Repository should be healthy
      const repoHealth = await symbolRepository.getHealthStatus();
      expect(repoHealth.isHealthy).toBe(true);

      // Queue should be healthy
      const queueHealth = await queueService.getComprehensiveHealthStatus();
      expect(queueHealth.isHealthy).toBe(true);

      // Audit service should be healthy
      const auditHealth = await auditService.getHealthStatus();
      expect(auditHealth.isHealthy).toBe(true);
    });

    it('should provide comprehensive statistics', async () => {
      const repoStats = await symbolRepository.getStats();
      expect(repoStats).toMatchObject({
        totalRecords: expect.any(Number),
        activeRecords: expect.any(Number),
        lastUpdate: expect.any(Date),
        exchangeBreakdown: expect.any(Object),
        segmentBreakdown: expect.any(Object),
      });

      const queueStats = await queueService.getQueueStats();
      expect(queueStats).toMatchObject({
        waiting: expect.any(Number),
        active: expect.any(Number),
        completed: expect.any(Number),
        failed: expect.any(Number),
        cronJobStatus: expect.any(String),
      });

      const auditStats = await auditService.getAuditStats();
      expect(Array.isArray(auditStats)).toBe(true);
    });
  });

  describe('Data Consistency and Validation', () => {
    it('should maintain data consistency across operations', async () => {
      // Create a test symbol
      const testSymbol = {
        instrumentToken: 'TEST_' + Date.now(),
        exchangeToken: 'TEST_EX_' + Date.now(),
        tradingSymbol: 'TEST_SYMBOL',
        name: 'Test Symbol for Integration',
        lastPrice: 100.50,
        tickSize: 0.05,
        lotSize: 1,
        instrumentType: 'EQ' as const,
        segment: 'NSE' as const,
        exchange: 'NSE' as const,
        isActive: true,
      };

      // 1. Insert symbol
      const insertResult = await symbolRepository.upsertSymbol(testSymbol);
      expect(insertResult.success).toBe(true);
      expect(insertResult.isNew).toBe(true);

      // 2. Retrieve symbol
      const retrievedSymbol = await symbolRepository.findByInstrumentToken(testSymbol.instrumentToken);
      expect(retrievedSymbol).toBeDefined();
      expect(retrievedSymbol.tradingSymbol).toBe(testSymbol.tradingSymbol);

      // 3. Update symbol
      const updatedSymbol = { ...testSymbol, lastPrice: 105.75 };
      const updateResult = await symbolRepository.upsertSymbol(updatedSymbol);
      expect(updateResult.success).toBe(true);
      expect(updateResult.isNew).toBe(false);

      // 4. Verify update
      const updatedRetrievedSymbol = await symbolRepository.findByInstrumentToken(testSymbol.instrumentToken);
      expect(updatedRetrievedSymbol.lastPrice).toBe(105.75);

      // 5. Search for symbol
      const searchResults = await symbolRepository.searchSymbols('TEST_SYMBOL', 10, 0);
      expect(searchResults.length).toBeGreaterThan(0);
      expect(searchResults.some(s => s.instrumentToken === testSymbol.instrumentToken)).toBe(true);
    });

    it('should handle concurrent operations safely', async () => {
      const concurrentOperations = [];
      const baseInstrumentToken = 'CONCURRENT_' + Date.now();

      // Create multiple concurrent upsert operations
      for (let i = 0; i < 5; i++) {
        const symbol = {
          instrumentToken: `${baseInstrumentToken}_${i}`,
          exchangeToken: `EX_${i}`,
          tradingSymbol: `CONCURRENT_${i}`,
          name: `Concurrent Test Symbol ${i}`,
          lastPrice: 100 + i,
          tickSize: 0.05,
          lotSize: 1,
          instrumentType: 'EQ' as const,
          segment: 'NSE' as const,
          exchange: 'NSE' as const,
          isActive: true,
        };

        concurrentOperations.push(symbolRepository.upsertSymbol(symbol));
      }

      // Execute all operations concurrently
      const results = await Promise.all(concurrentOperations);

      // Verify all operations succeeded
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Verify all symbols were stored
      for (let i = 0; i < 5; i++) {
        const symbol = await symbolRepository.findByInstrumentToken(`${baseInstrumentToken}_${i}`);
        expect(symbol).toBeDefined();
        expect(symbol.tradingSymbol).toBe(`CONCURRENT_${i}`);
      }
    });
  });

  describe('Cron Job Scheduling', () => {
    it('should have daily cron job configured', async () => {
      const queueStats = await queueService.getQueueStats();
      
      expect(queueStats.cronJobStatus).toBe('active');
      expect(queueStats.nextCronRun).toBeDefined();
      expect(queueStats.nextCronRun).toBeInstanceOf(Date);

      // Next run should be in the future
      expect(queueStats.nextCronRun.getTime()).toBeGreaterThan(Date.now());
    });

    it('should be able to trigger immediate download', async () => {
      const immediateDownload = await queueService.addSymbolDownloadJob('ALL', 'ALL', {
        forceRefresh: true,
        priority: 1, // High priority
        requestId: 'immediate-test-' + Date.now(),
      });

      expect(immediateDownload.jobId).toBeDefined();
      expect(immediateDownload.requestId).toBeDefined();

      // Job should be queued
      const jobStatus = await queueService.getJobStatus(immediateDownload.jobId);
      expect(jobStatus).toBeDefined();
      expect(['waiting', 'active', 'completed'].includes(jobStatus.state)).toBe(true);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary failures', async () => {
      // This test would require mocking temporary failures
      // For now, we'll test that the system can handle retries
      
      const healthStatus = await symbolService.getHealthStatus();
      expect(healthStatus.isHealthy).toBe(true);

      // Test that services can restart gracefully
      const repoHealth = await symbolRepository.getHealthStatus();
      expect(repoHealth.connectionStatus).toBe(true);
    });

    it('should maintain audit trail during failures', async () => {
      // Get current audit count
      const initialStats = await auditService.getAuditStats();
      const initialCount = initialStats.reduce((sum, stat) => sum + stat.count, 0);

      // Trigger an operation that might fail
      try {
        await auditService.logSymbolOperation(
          'test_operation',
          'TEST',
          'TEST',
          'resilience-test-' + Date.now()
        );
      } catch (error) {
        // Expected to potentially fail in test environment
      }

      // Verify audit logs are still accessible
      const finalStats = await auditService.getAuditStats();
      expect(Array.isArray(finalStats)).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle batch operations efficiently', async () => {
      const batchSize = 10;
      const symbols = [];

      // Create batch of test symbols
      for (let i = 0; i < batchSize; i++) {
        symbols.push({
          instrumentToken: `BATCH_${Date.now()}_${i}`,
          exchangeToken: `BATCH_EX_${i}`,
          tradingSymbol: `BATCH_${i}`,
          name: `Batch Test Symbol ${i}`,
          lastPrice: 100 + i,
          tickSize: 0.05,
          lotSize: 1,
          instrumentType: 'EQ' as const,
          segment: 'NSE' as const,
          exchange: 'NSE' as const,
          isActive: true,
        });
      }

      const startTime = Date.now();
      const result = await symbolRepository.batchUpsertSymbols(symbols, batchSize);
      const duration = Date.now() - startTime;

      expect(result.totalProcessed).toBe(batchSize);
      expect(result.errors.length).toBe(0);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle large result sets with pagination', async () => {
      const pageSize = 50;
      const firstPage = await symbolRepository.findSymbols({
        limit: pageSize,
        offset: 0,
      });

      expect(firstPage.data.length).toBeLessThanOrEqual(pageSize);
      expect(typeof firstPage.total).toBe('number');
      expect(typeof firstPage.hasMore).toBe('boolean');

      if (firstPage.hasMore) {
        const secondPage = await symbolRepository.findSymbols({
          limit: pageSize,
          offset: pageSize,
        });

        expect(secondPage.data.length).toBeLessThanOrEqual(pageSize);
        // Ensure no duplicate records between pages
        const firstPageTokens = new Set(firstPage.data.map(s => s.instrumentToken));
        const secondPageTokens = new Set(secondPage.data.map(s => s.instrumentToken));
        const intersection = new Set([...firstPageTokens].filter(x => secondPageTokens.has(x)));
        expect(intersection.size).toBe(0);
      }
    });
  });
});
