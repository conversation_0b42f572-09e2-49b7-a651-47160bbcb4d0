import { z } from 'zod/v4';
import { utcDateTimeSchema } from '@app/common/schema';

// ==================== KITE CONNECT INSTRUMENT MASTER SCHEMAS ====================

/**
 * Kite Connect instrument types enum
 * Based on Zerodha Kite Connect API documentation
 */
export const KiteInstrumentTypeEnum = z.enum(['EQ', 'FUT', 'CE', 'PE', 'INDEX']);
export type KiteInstrumentType = z.output<typeof KiteInstrumentTypeEnum>;

/**
 * Kite Connect exchange enum
 * Based on Zerodha Kite Connect supported exchanges
 */
export const KiteExchangeEnum = z.enum(['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX']);
export type KiteExchange = z.output<typeof KiteExchangeEnum>;

/**
 * Kite Connect segment enum
 * Based on Zerodha Kite Connect supported segments
 */
export const KiteSegmentEnum = z.enum(['NSE', 'BSE', 'NFO-FUT', 'NFO-OPT', 'BFO-FUT', 'BFO-OPT', 'CDS', 'MCX']);
export type KiteSegment = z.output<typeof KiteSegmentEnum>;

/**
 * Raw Kite Connect instrument master data schema
 * Matches the CSV format returned by Kite Connect API
 */
export const KiteInstrumentMasterRawSchema = z.object({
  instrument_token: z.string().describe('Numerical identifier for WebSocket subscriptions'),
  exchange_token: z.string().describe('Exchange-issued numerical identifier'),
  tradingsymbol: z.string().min(1).describe('Exchange trading symbol'),
  name: z.string().describe('Company name (for equity instruments)'),
  last_price: z.number().nonnegative().describe('Last traded market price'),
  expiry: z.string().optional().describe('Expiry date for derivatives (YYYY-MM-DD format)'),
  strike: z.number().nonnegative().optional().describe('Strike price for options'),
  tick_size: z.number().positive().describe('Value of a single price tick'),
  lot_size: z.number().int().positive().describe('Quantity of a single lot'),
  instrument_type: KiteInstrumentTypeEnum.describe('Instrument type'),
  segment: KiteSegmentEnum.describe('Market segment'),
  exchange: KiteExchangeEnum.describe('Exchange'),
});

export type KiteInstrumentMasterRaw = z.output<typeof KiteInstrumentMasterRawSchema>;

/**
 * Processed symbol master data schema for internal use
 * Normalized and validated version of Kite instrument data
 */
export const SymbolMasterSchema = z.object({
  instrumentToken: z.string().describe('Kite instrument token (primary identifier)'),
  exchangeToken: z.string().describe('Exchange token'),
  tradingSymbol: z.string().min(1).max(50).describe('Trading symbol'),
  name: z.string().max(255).describe('Instrument name'),
  lastPrice: z.number().nonnegative().describe('Last traded price'),
  expiry: z.string().optional().describe('Expiry date for derivatives (YYYY-MM-DD format)'),
  strike: z.number().nonnegative().optional().describe('Strike price for options'),
  tickSize: z.number().positive().describe('Minimum price movement'),
  lotSize: z.number().int().positive().describe('Lot size'),
  instrumentType: KiteInstrumentTypeEnum.describe('Type of instrument'),
  segment: KiteSegmentEnum.describe('Market segment'),
  exchange: KiteExchangeEnum.describe('Exchange'),
  isActive: z.boolean().default(true).describe('Whether instrument is active'),
  downloadedAt: utcDateTimeSchema.describe('When this data was downloaded'),
  updatedAt: utcDateTimeSchema.describe('Last update timestamp'),
});

export type SymbolMaster = z.output<typeof SymbolMasterSchema>;

/**
 * Symbol master upsert data schema
 * Used for inserting/updating symbol data in QuestDB
 */
export const SymbolMasterUpsertSchema = SymbolMasterSchema.omit({
  downloadedAt: true,
  updatedAt: true,
});

export type SymbolMasterUpsert = z.output<typeof SymbolMasterUpsertSchema>;

/**
 * Symbol master query filters schema
 */
export const SymbolMasterQueryFiltersSchema = z.object({
  exchange: KiteExchangeEnum.optional().describe('Filter by exchange'),
  segment: KiteSegmentEnum.optional().describe('Filter by segment'),
  instrumentType: KiteInstrumentTypeEnum.optional().describe('Filter by instrument type'),
  tradingSymbol: z.string().optional().describe('Filter by trading symbol (partial match)'),
  isActive: z.boolean().optional().describe('Filter by active status'),
  limit: z.number().int().positive().max(1000).default(100).describe('Maximum results to return'),
  offset: z.number().int().nonnegative().default(0).describe('Number of results to skip'),
});

export type SymbolMasterQueryFilters = z.output<typeof SymbolMasterQueryFiltersSchema>;

// ==================== SYMBOL DOWNLOAD OPERATION SCHEMAS ====================

/**
 * Symbol download request schema
 */
export const SymbolDownloadRequestSchema = z.object({
  exchange: z
    .union([KiteExchangeEnum, z.literal('ALL')])
    .default('ALL')
    .describe('Exchange to download'),
  segment: z
    .union([KiteSegmentEnum, z.literal('ALL')])
    .default('ALL')
    .describe('Segment to download'),
  forceRefresh: z.boolean().default(false).describe('Force refresh even if recently downloaded'),
  batchSize: z.number().int().positive().max(1000).default(500).describe('Batch size for processing'),
  requestId: z.string().optional().describe('Optional request identifier for tracking'),
});

export type SymbolDownloadRequest = z.output<typeof SymbolDownloadRequestSchema>;

/**
 * Symbol download job data schema for BullMQ
 */
export const SymbolDownloadJobDataSchema = SymbolDownloadRequestSchema.extend({
  requestId: z.string().describe('Unique request identifier'),
  scheduledAt: utcDateTimeSchema.describe('When the job was scheduled'),
  priority: z.number().int().min(1).max(10).default(5).describe('Job priority (1=highest, 10=lowest)'),
});

export type SymbolDownloadJobData = z.output<typeof SymbolDownloadJobDataSchema>;

/**
 * Symbol download result schema
 */
export const SymbolDownloadResultSchema = z.object({
  exchange: z.string().describe('Exchange processed'),
  segment: z.string().describe('Segment processed'),
  symbolsProcessed: z.number().int().nonnegative().describe('Total symbols processed'),
  symbolsAdded: z.number().int().nonnegative().describe('New symbols added'),
  symbolsUpdated: z.number().int().nonnegative().describe('Existing symbols updated'),
  symbolsSkipped: z.number().int().nonnegative().describe('Symbols skipped'),
  duration: z.number().nonnegative().describe('Processing duration in milliseconds'),
  startedAt: utcDateTimeSchema.describe('When processing started'),
  completedAt: utcDateTimeSchema.describe('When processing completed'),
  errors: z.array(z.string()).default([]).describe('Any errors encountered'),
});

export type SymbolDownloadResult = z.output<typeof SymbolDownloadResultSchema>;

// ==================== SYMBOL AUDIT LOG SCHEMAS ====================

/**
 * Symbol operation types enum
 */
export const SymbolOperationTypeEnum = z.enum([
  'DOWNLOAD_STARTED',
  'DOWNLOAD_COMPLETED',
  'DOWNLOAD_FAILED',
  'SYMBOL_ADDED',
  'SYMBOL_UPDATED',
  'SYMBOL_DEACTIVATED',
  'BATCH_PROCESSED',
  'CLEANUP_PERFORMED',
]);

export type SymbolOperationType = z.output<typeof SymbolOperationTypeEnum>;

/**
 * Symbol audit log schema for Drizzle ORM
 */
export const SymbolAuditLogSchema = z.object({
  id: z.number().int().positive().optional().describe('Auto-generated ID'),
  operationType: SymbolOperationTypeEnum.describe('Type of operation performed'),
  exchange: z.string().optional().describe('Exchange involved in operation'),
  segment: z.string().optional().describe('Segment involved in operation'),
  instrumentToken: z.string().optional().describe('Specific instrument token if applicable'),
  requestId: z.string().optional().describe('Request ID for tracking'),
  jobId: z.string().optional().describe('BullMQ job ID'),
  status: z.enum(['SUCCESS', 'FAILED', 'IN_PROGRESS']).describe('Operation status'),
  details: z.record(z.string(), z.unknown()).optional().describe('Additional operation details'),
  errorMessage: z.string().optional().describe('Error message if operation failed'),
  duration: z.number().nonnegative().optional().describe('Operation duration in milliseconds'),
  recordsAffected: z.number().int().nonnegative().optional().describe('Number of records affected'),
  createdAt: utcDateTimeSchema.describe('When the log entry was created'),
  createdBy: z.string().default('SYSTEM').describe('Who/what created this log entry'),
});

export type SymbolAuditLog = z.output<typeof SymbolAuditLogSchema>;

/**
 * Symbol audit log creation schema (without auto-generated fields)
 */
export const CreateSymbolAuditLogSchema = SymbolAuditLogSchema.omit({
  id: true,
  createdAt: true,
});

export type CreateSymbolAuditLog = z.output<typeof CreateSymbolAuditLogSchema>;

// ==================== API RESPONSE SCHEMAS ====================

/**
 * Symbol master API response schema
 */
export const SymbolMasterResponseSchema = z.object({
  success: z.literal(true),
  data: z.array(SymbolMasterSchema),
  meta: z.object({
    total: z.number().int().nonnegative(),
    limit: z.number().int().positive(),
    offset: z.number().int().nonnegative(),
    hasMore: z.boolean(),
  }),
  timestamp: utcDateTimeSchema,
});

export type SymbolMasterResponse = z.output<typeof SymbolMasterResponseSchema>;

/**
 * Symbol download status response schema
 */
export const SymbolDownloadStatusResponseSchema = z.object({
  success: z.literal(true),
  data: z.object({
    jobId: z.string(),
    status: z.enum(['WAITING', 'ACTIVE', 'COMPLETED', 'FAILED', 'DELAYED', 'PAUSED']),
    progress: z.number().min(0).max(100),
    result: SymbolDownloadResultSchema.optional(),
    error: z.string().optional(),
    createdAt: utcDateTimeSchema,
    processedAt: utcDateTimeSchema.optional(),
    completedAt: utcDateTimeSchema.optional(),
  }),
  timestamp: utcDateTimeSchema,
});

export type SymbolDownloadStatusResponse = z.output<typeof SymbolDownloadStatusResponseSchema>;

// ==================== MICROSERVICE MESSAGE SCHEMAS ====================

/**
 * Symbol service message patterns for microservice communication
 */
export const SymbolServiceMessagePatterns = {
  // Symbol data operations
  GET_SYMBOLS: 'symbol.get_symbols',
  GET_SYMBOL_BY_TOKEN: 'symbol.get_symbol_by_token',
  SEARCH_SYMBOLS: 'symbol.search_symbols',

  // Download operations
  TRIGGER_DOWNLOAD: 'symbol.trigger_download',
  GET_DOWNLOAD_STATUS: 'symbol.get_download_status',
  CANCEL_DOWNLOAD: 'symbol.cancel_download',

  // Health and status
  GET_HEALTH: 'symbol.get_health',
  GET_STATS: 'symbol.get_stats',
} as const;

/**
 * Symbol service request/response schemas for microservice communication
 */
export const SymbolServiceSchemas = {
  GetSymbolsRequest: SymbolMasterQueryFiltersSchema,
  GetSymbolsResponse: SymbolMasterResponseSchema,

  GetSymbolByTokenRequest: z.object({
    instrumentToken: z.string().min(1),
  }),
  GetSymbolByTokenResponse: z.object({
    success: z.literal(true),
    data: SymbolMasterSchema.optional(),
    timestamp: utcDateTimeSchema,
  }),

  TriggerDownloadRequest: SymbolDownloadRequestSchema,
  TriggerDownloadResponse: z.object({
    success: z.literal(true),
    data: z.object({
      jobId: z.string(),
      requestId: z.string(),
      scheduledAt: utcDateTimeSchema,
    }),
    timestamp: utcDateTimeSchema,
  }),

  GetDownloadStatusRequest: z.object({
    jobId: z.string().min(1),
  }),
  GetDownloadStatusResponse: SymbolDownloadStatusResponseSchema,
} as const;

// ==================== EXPORT COLLECTIONS ====================

/**
 * Collection of all symbol-related schemas
 */
export const SymbolSchemas = {
  // Core data schemas
  KiteInstrumentMasterRaw: KiteInstrumentMasterRawSchema,
  SymbolMaster: SymbolMasterSchema,
  SymbolMasterUpsert: SymbolMasterUpsertSchema,
  SymbolMasterQueryFilters: SymbolMasterQueryFiltersSchema,

  // Operation schemas
  SymbolDownloadRequest: SymbolDownloadRequestSchema,
  SymbolDownloadJobData: SymbolDownloadJobDataSchema,
  SymbolDownloadResult: SymbolDownloadResultSchema,

  // Audit schemas
  SymbolAuditLog: SymbolAuditLogSchema,
  CreateSymbolAuditLog: CreateSymbolAuditLogSchema,

  // API response schemas
  SymbolMasterResponse: SymbolMasterResponseSchema,
  SymbolDownloadStatusResponse: SymbolDownloadStatusResponseSchema,

  // Microservice schemas
  ServiceSchemas: SymbolServiceSchemas,
} as const;

/**
 * Collection of all symbol-related enums
 */
export const SymbolEnums = {
  KiteInstrumentType: KiteInstrumentTypeEnum,
  KiteExchange: KiteExchangeEnum,
  KiteSegment: KiteSegmentEnum,
  SymbolOperationType: SymbolOperationTypeEnum,
} as const;

/**
 * Collection of all symbol-related constants
 */
export const SymbolConstants = {
  MessagePatterns: SymbolServiceMessagePatterns,
  DefaultBatchSize: 500,
  MaxBatchSize: 1000,
  DefaultQueryLimit: 100,
  MaxQueryLimit: 1000,
  CronSchedule: '0 8 * * *', // 8:00 AM IST daily
  CronTimezone: 'Asia/Kolkata',
} as const;
