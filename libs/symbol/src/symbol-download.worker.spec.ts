import { Test, TestingModule } from '@nestjs/testing';
import { SymbolDownloadWorkerService } from './symbol-download.worker';
import { SymbolService } from './symbol.service';
import { QueueHealthService } from '@app/core/queue';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { Job } from 'bullmq';

describe('SymbolDownloadWorkerService', () => {
  let worker: SymbolDownloadWorkerService;
  let mockSymbolService: jest.Mocked<SymbolService>;
  let mockQueueHealthService: jest.Mocked<QueueHealthService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockErrorUtils: jest.Mocked<ErrorUtilsService>;
  let mockEnvService: jest.Mocked<EnvService>;

  const mockJobData = {
    exchange: 'NSE',
    segment: 'NSE',
    forceRefresh: false,
    batchSize: 500,
    requestId: 'test-request-123',
    scheduledAt: new Date('2024-01-15T10:00:00Z'),
    priority: 5,
  };

  const mockDownloadResult = {
    exchange: 'NSE',
    segment: 'NSE',
    symbolsProcessed: 100,
    symbolsAdded: 50,
    symbolsUpdated: 50,
    symbolsSkipped: 0,
    duration: 30000,
    startedAt: new Date('2024-01-15T10:00:00Z'),
    completedAt: new Date('2024-01-15T10:00:30Z'),
    errors: [],
  };

  beforeEach(async () => {
    mockSymbolService = {
      downloadAndStoreSymbolMaster: jest.fn(),
    } as any;

    mockQueueHealthService = {
      registerWorkerHealthCheck: jest.fn(),
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(() => new Date('2024-01-15T10:00:00Z')),
      getTime: jest.fn(() => Date.now()),
    } as any;

    mockErrorUtils = {
      getErrorMessage: jest.fn((error) => error instanceof Error ? error.message : String(error)),
    } as any;

    mockEnvService = {
      get: jest.fn((key) => {
        const envVars: Record<string, string> = {
          'REDIS_HOST': 'localhost',
          'REDIS_PORT': '6379',
        };
        return envVars[key];
      }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SymbolDownloadWorkerService,
        { provide: SymbolService, useValue: mockSymbolService },
        { provide: QueueHealthService, useValue: mockQueueHealthService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
        { provide: EnvService, useValue: mockEnvService },
      ],
    }).compile();

    worker = module.get<SymbolDownloadWorkerService>(SymbolDownloadWorkerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processJob', () => {
    let mockJob: jest.Mocked<Job>;

    beforeEach(() => {
      mockJob = {
        id: 'job-123',
        data: mockJobData,
        updateProgress: jest.fn(),
        attemptsMade: 1,
      } as any;
    });

    it('should process job successfully', async () => {
      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

      const result = await worker['processJob'](mockJob);

      expect(result).toMatchObject({
        success: true,
        data: mockDownloadResult,
        jobId: 'job-123',
        queueName: expect.any(String),
        processedAt: expect.any(Date),
        duration: expect.any(Number),
        attempts: 1,
      });

      expect(mockJob.updateProgress).toHaveBeenCalledWith(10);
      expect(mockJob.updateProgress).toHaveBeenCalledWith(90);
      expect(mockJob.updateProgress).toHaveBeenCalledWith(100);
      expect(mockSymbolService.downloadAndStoreSymbolMaster).toHaveBeenCalledWith(
        'NSE',
        'NSE',
        false,
        'test-request-123'
      );
    });

    it('should handle ALL exchange and segment', async () => {
      const allJobData = { ...mockJobData, exchange: 'ALL', segment: 'ALL' };
      mockJob.data = allJobData;

      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue({
        ...mockDownloadResult,
        exchange: 'ALL',
        segment: 'ALL',
      });

      const result = await worker['processJob'](mockJob);

      expect(result.success).toBe(true);
      expect(mockSymbolService.downloadAndStoreSymbolMaster).toHaveBeenCalledWith(
        undefined, // ALL exchange becomes undefined
        undefined, // ALL segment becomes undefined
        false,
        'test-request-123'
      );
    });

    it('should handle job processing errors', async () => {
      const error = new Error('Download failed');
      mockSymbolService.downloadAndStoreSymbolMaster.mockRejectedValue(error);

      await expect(worker['processJob'](mockJob)).rejects.toThrow('Download failed');

      expect(mockJob.updateProgress).toHaveBeenCalledWith(10);
      expect(mockErrorUtils.getErrorMessage).toHaveBeenCalledWith(error);
    });

    it('should handle missing job ID', async () => {
      mockJob.id = undefined;
      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

      const result = await worker['processJob'](mockJob);

      expect(result.jobId).toBe('unknown');
    });

    it('should log job start and completion', async () => {
      const logSpy = jest.spyOn(worker['logger'], 'log');
      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

      await worker['processJob'](mockJob);

      expect(logSpy).toHaveBeenCalledWith(
        'Processing symbol download job for NSE:NSE',
        expect.objectContaining({
          jobId: 'job-123',
          exchange: 'NSE',
          segment: 'NSE',
          forceRefresh: false,
          batchSize: 500,
          requestId: 'test-request-123',
        })
      );

      expect(logSpy).toHaveBeenCalledWith(
        'Symbol download completed successfully',
        expect.objectContaining({
          jobId: 'job-123',
          requestId: 'test-request-123',
          result: mockDownloadResult,
        })
      );
    });

    it('should log job failures', async () => {
      const logSpy = jest.spyOn(worker['logger'], 'error');
      const error = new Error('Download failed');
      mockSymbolService.downloadAndStoreSymbolMaster.mockRejectedValue(error);

      await expect(worker['processJob'](mockJob)).rejects.toThrow();

      expect(logSpy).toHaveBeenCalledWith(
        'Symbol download failed',
        expect.objectContaining({
          jobId: 'job-123',
          exchange: 'NSE',
          segment: 'NSE',
          requestId: 'test-request-123',
          error: 'Download failed',
          duration: expect.any(Number),
        })
      );
    });

    it('should update progress at different stages', async () => {
      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

      await worker['processJob'](mockJob);

      expect(mockJob.updateProgress).toHaveBeenCalledTimes(3);
      expect(mockJob.updateProgress).toHaveBeenNthCalledWith(1, 10);
      expect(mockJob.updateProgress).toHaveBeenNthCalledWith(2, 90);
      expect(mockJob.updateProgress).toHaveBeenNthCalledWith(3, 100);
    });

    it('should handle progress update failures gracefully', async () => {
      mockJob.updateProgress.mockRejectedValue(new Error('Progress update failed'));
      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

      // Should not throw even if progress update fails
      const result = await worker['processJob'](mockJob);

      expect(result.success).toBe(true);
    });

    it('should include job metadata in result', async () => {
      mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

      const result = await worker['processJob'](mockJob);

      expect(result).toMatchObject({
        success: true,
        data: mockDownloadResult,
        jobId: 'job-123',
        queueName: expect.any(String),
        processedAt: expect.any(Date),
        duration: expect.any(Number),
        attempts: 1,
      });
    });

    it('should handle different job data variations', async () => {
      const variations = [
        { ...mockJobData, forceRefresh: true },
        { ...mockJobData, batchSize: 1000 },
        { ...mockJobData, priority: 1 },
        { ...mockJobData, requestId: undefined },
      ];

      for (const variation of variations) {
        mockJob.data = variation;
        mockSymbolService.downloadAndStoreSymbolMaster.mockResolvedValue(mockDownloadResult);

        const result = await worker['processJob'](mockJob);

        expect(result.success).toBe(true);
        expect(mockSymbolService.downloadAndStoreSymbolMaster).toHaveBeenCalledWith(
          'NSE',
          'NSE',
          variation.forceRefresh,
          variation.requestId || 'test-request-123'
        );

        jest.clearAllMocks();
      }
    });
  });

  describe('constructor', () => {
    it('should register health check', () => {
      expect(mockQueueHealthService.registerWorkerHealthCheck).toHaveBeenCalledWith(
        'SymbolDownloadWorkerService',
        expect.any(Function)
      );
    });

    it('should configure worker with correct options', () => {
      // Verify that the worker is configured with concurrency of 1
      // This is implicit in the constructor call
      expect(worker).toBeDefined();
    });
  });

  describe('error handling', () => {
    let mockJob: jest.Mocked<Job>;

    beforeEach(() => {
      mockJob = {
        id: 'job-123',
        data: mockJobData,
        updateProgress: jest.fn(),
        attemptsMade: 1,
      } as any;
    });

    it('should handle service unavailable errors', async () => {
      const error = new Error('Service unavailable');
      mockSymbolService.downloadAndStoreSymbolMaster.mockRejectedValue(error);

      await expect(worker['processJob'](mockJob)).rejects.toThrow('Service unavailable');
    });

    it('should handle timeout errors', async () => {
      const error = new Error('Request timeout');
      mockSymbolService.downloadAndStoreSymbolMaster.mockRejectedValue(error);

      await expect(worker['processJob'](mockJob)).rejects.toThrow('Request timeout');
    });

    it('should handle validation errors', async () => {
      const error = new Error('Invalid data');
      mockSymbolService.downloadAndStoreSymbolMaster.mockRejectedValue(error);

      await expect(worker['processJob'](mockJob)).rejects.toThrow('Invalid data');
    });

    it('should preserve error context', async () => {
      const error = new Error('Database connection failed');
      error.stack = 'Error stack trace';
      mockSymbolService.downloadAndStoreSymbolMaster.mockRejectedValue(error);

      await expect(worker['processJob'](mockJob)).rejects.toThrow('Database connection failed');
      expect(mockErrorUtils.getErrorMessage).toHaveBeenCalledWith(error);
    });
  });
});
