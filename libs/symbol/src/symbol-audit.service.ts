import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/core/drizzle';
import { AuditLoggingService } from '@app/common/security';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { eq, and, gte, lte, desc, count, sql } from 'drizzle-orm';
import {
  SymbolAuditLogTable,
  SymbolDownloadStatsTable,
  NewSymbolAuditLog,
  NewSymbolDownloadStats,
  SymbolAuditLog,
  SymbolDownloadStats,
} from './symbol-audit.model';
import { SymbolOperationType, SymbolDownloadResult, CreateSymbolAuditLog } from './symbol.schema';

/**
 * Symbol Audit Service for comprehensive audit logging
 *
 * Provides comprehensive audit logging for all symbol operations using:
 * - Drizzle ORM for database operations
 * - Integration with existing audit logging service
 * - Structured logging for monitoring and debugging
 * - Performance metrics tracking
 * - Compliance and security audit trails
 *
 * Features:
 * - Track all symbol download operations
 * - Log individual symbol updates and changes
 * - Monitor performance metrics and statistics
 * - Provide audit trails for compliance
 * - Support for querying and reporting
 */
@Injectable()
export class SymbolAuditService {
  private readonly logger = new Logger(SymbolAuditService.name);

  constructor(
    private readonly drizzleService: DrizzleService,
    private readonly auditLoggingService: AuditLoggingService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== SYMBOL OPERATION AUDIT LOGGING ====================

  /**
   * Log symbol download operation start
   */
  async logDownloadStart(
    exchange: string,
    segment: string,
    requestId: string,
    jobId?: string,
    details?: Record<string, unknown>,
  ): Promise<number> {
    try {
      const auditData: NewSymbolAuditLog = {
        operationType: 'DOWNLOAD_STARTED',
        exchange,
        segment,
        requestId,
        jobId,
        status: 'IN_PROGRESS',
        details,
        operationStartedAt: this.dateTimeUtils.getUtcNow(),
        createdBy: 'SYSTEM',
      };

      const [result] = await this.drizzleService.db
        .insert(SymbolAuditLogTable)
        .values(auditData)
        .returning({ id: SymbolAuditLogTable.id });

      // Also log to application audit service
      this.auditLoggingService.logOperationStart(
        {
          operation: 'SYMBOL_DOWNLOAD',
          correlationId: requestId,
          userId: 'SYSTEM',
          accountId: undefined,
          startTime: this.dateTimeUtils.getUtcNow(),
          metadata: { exchange, segment, jobId, ...details },
        },
        'SYMBOL',
      );

      this.logger.log('Symbol download operation started', {
        auditId: result.id,
        exchange,
        segment,
        requestId,
        jobId,
      });

      return result.id;
    } catch (error) {
      this.logger.error('Failed to log download start', {
        exchange,
        segment,
        requestId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Log symbol download operation completion
   */
  async logDownloadComplete(auditId: number, result: SymbolDownloadResult, requestId: string): Promise<void> {
    try {
      const completedAt = this.dateTimeUtils.getUtcNow();

      await this.drizzleService.db
        .update(SymbolAuditLogTable)
        .set({
          status: 'SUCCESS',
          details: result,
          recordsAffected: result.symbolsProcessed,
          duration: result.duration,
          operationCompletedAt: completedAt,
          updatedAt: completedAt,
        })
        .where(eq(SymbolAuditLogTable.id, auditId));

      // Log to application audit service
      this.auditLoggingService.logOperationSuccess(
        {
          operation: 'SYMBOL_DOWNLOAD',
          correlationId: requestId,
          userId: 'SYSTEM',
          accountId: undefined,
          startTime: result.startedAt,
          metadata: { exchange: result.exchange, segment: result.segment },
        },
        result,
        'SYMBOL',
      );

      // Create download statistics record
      await this.createDownloadStats(result);

      this.logger.log('Symbol download operation completed', {
        auditId,
        requestId,
        result,
      });
    } catch (error) {
      this.logger.error('Failed to log download completion', {
        auditId,
        requestId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Log symbol download operation failure
   */
  async logDownloadFailure(
    auditId: number,
    error: Error,
    requestId: string,
    exchange: string,
    segment: string,
    duration?: number,
  ): Promise<void> {
    try {
      const completedAt = this.dateTimeUtils.getUtcNow();
      const errorMessage = this.errorUtils.getErrorMessage(error);

      await this.drizzleService.db
        .update(SymbolAuditLogTable)
        .set({
          status: 'FAILED',
          errorMessage,
          duration,
          operationCompletedAt: completedAt,
          updatedAt: completedAt,
        })
        .where(eq(SymbolAuditLogTable.id, auditId));

      // Log to application audit service
      this.auditLoggingService.logOperationFailure(
        {
          operation: 'SYMBOL_DOWNLOAD',
          correlationId: requestId,
          userId: 'SYSTEM',
          accountId: undefined,
          startTime: this.dateTimeUtils.getUtcNow(),
          metadata: { exchange, segment },
        },
        error,
        'SYMBOL',
      );

      this.logger.error('Symbol download operation failed', {
        auditId,
        requestId,
        exchange,
        segment,
        error: errorMessage,
      });
    } catch (auditError) {
      this.logger.error('Failed to log download failure', {
        auditId,
        requestId,
        originalError: this.errorUtils.getErrorMessage(error),
        auditError: this.errorUtils.getErrorMessage(auditError),
      });
      throw auditError;
    }
  }

  /**
   * Log individual symbol operation (add/update/deactivate)
   */
  async logSymbolOperation(
    operationType: SymbolOperationType,
    instrumentToken: string,
    exchange: string,
    segment: string,
    requestId?: string,
    details?: Record<string, unknown>,
  ): Promise<number> {
    try {
      const auditData: NewSymbolAuditLog = {
        operationType,
        exchange,
        segment,
        instrumentToken,
        requestId,
        status: 'SUCCESS',
        details,
        operationStartedAt: this.dateTimeUtils.getUtcNow(),
        operationCompletedAt: this.dateTimeUtils.getUtcNow(),
        createdBy: 'SYSTEM',
      };

      const [result] = await this.drizzleService.db
        .insert(SymbolAuditLogTable)
        .values(auditData)
        .returning({ id: SymbolAuditLogTable.id });

      this.logger.debug('Symbol operation logged', {
        auditId: result.id,
        operationType,
        instrumentToken,
        exchange,
        segment,
      });

      return result.id;
    } catch (error) {
      this.logger.error('Failed to log symbol operation', {
        operationType,
        instrumentToken,
        exchange,
        segment,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Log batch processing operation
   */
  async logBatchOperation(
    exchange: string,
    segment: string,
    batchSize: number,
    processedCount: number,
    addedCount: number,
    updatedCount: number,
    errorCount: number,
    duration: number,
    requestId?: string,
  ): Promise<number> {
    try {
      const auditData: NewSymbolAuditLog = {
        operationType: 'BATCH_PROCESSED',
        exchange,
        segment,
        requestId,
        status: errorCount === 0 ? 'SUCCESS' : 'FAILED',
        details: {
          batchSize,
          processedCount,
          addedCount,
          updatedCount,
          errorCount,
        },
        recordsAffected: processedCount,
        duration,
        operationStartedAt: this.dateTimeUtils.getUtcNow(),
        operationCompletedAt: this.dateTimeUtils.getUtcNow(),
        createdBy: 'SYSTEM',
      };

      const [result] = await this.drizzleService.db
        .insert(SymbolAuditLogTable)
        .values(auditData)
        .returning({ id: SymbolAuditLogTable.id });

      this.logger.log('Batch operation logged', {
        auditId: result.id,
        exchange,
        segment,
        batchSize,
        processedCount,
        addedCount,
        updatedCount,
        errorCount,
        duration,
      });

      return result.id;
    } catch (error) {
      this.logger.error('Failed to log batch operation', {
        exchange,
        segment,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== DOWNLOAD STATISTICS ====================

  /**
   * Create download statistics record
   */
  private async createDownloadStats(result: SymbolDownloadResult): Promise<void> {
    try {
      const statsData: NewSymbolDownloadStats = {
        downloadDate: this.dateTimeUtils.getUtcNow(),
        exchange: result.exchange,
        segment: result.segment,
        totalSymbols: result.symbolsProcessed,
        symbolsAdded: result.symbolsAdded,
        symbolsUpdated: result.symbolsUpdated,
        symbolsSkipped: result.symbolsSkipped,
        downloadDuration: result.duration,
        processingDuration: result.duration,
        successRate:
          result.errors.length === 0
            ? 100
            : Math.round(((result.symbolsProcessed - result.errors.length) / result.symbolsProcessed) * 100),
        errorCount: result.errors.length,
        downloadStatus: result.errors.length === 0 ? 'COMPLETED' : result.symbolsProcessed > 0 ? 'PARTIAL' : 'FAILED',
        downloadStartedAt: result.startedAt,
        downloadCompletedAt: result.completedAt,
        createdBy: 'SYSTEM',
      };

      await this.drizzleService.db.insert(SymbolDownloadStatsTable).values(statsData);

      this.logger.debug('Download statistics created', {
        exchange: result.exchange,
        segment: result.segment,
        stats: statsData,
      });
    } catch (error) {
      this.logger.error('Failed to create download statistics', {
        result,
        error: this.errorUtils.getErrorMessage(error),
      });
      // Don't throw here as this is supplementary data
    }
  }

  // ==================== QUERY AND REPORTING METHODS ====================

  /**
   * Get audit logs for a specific request ID
   */
  async getAuditLogsByRequestId(requestId: string): Promise<SymbolAuditLog[]> {
    try {
      const logs = await this.drizzleService.db
        .select()
        .from(SymbolAuditLogTable)
        .where(eq(SymbolAuditLogTable.requestId, requestId))
        .orderBy(desc(SymbolAuditLogTable.createdAt));

      return logs;
    } catch (error) {
      this.logger.error('Failed to get audit logs by request ID', {
        requestId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldAuditLogs(olderThanDays: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      this.logger.log('Cleaning up old audit logs', {
        cutoffDate: cutoffDate.toISOString(),
        olderThanDays,
      });

      const result = await this.drizzleService.db
        .delete(SymbolAuditLogTable)
        .where(lte(SymbolAuditLogTable.createdAt, cutoffDate));

      this.logger.log('Old audit logs cleaned up', {
        deletedCount: result.rowCount || 0,
        cutoffDate: cutoffDate.toISOString(),
      });

      return result.rowCount || 0;
    } catch (error) {
      this.logger.error('Failed to cleanup old audit logs', {
        olderThanDays,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get audit service health status
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean;
    totalAuditLogs: number;
    recentOperations: number;
    lastOperation?: Date;
    issues: string[];
  }> {
    try {
      const issues: string[] = [];

      // Get total audit logs count
      const [totalResult] = await this.drizzleService.db.select({ count: count() }).from(SymbolAuditLogTable);

      // Get recent operations (last 24 hours)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const [recentResult] = await this.drizzleService.db
        .select({ count: count() })
        .from(SymbolAuditLogTable)
        .where(gte(SymbolAuditLogTable.createdAt, yesterday));

      // Get last operation
      const [lastOpResult] = await this.drizzleService.db
        .select({ createdAt: SymbolAuditLogTable.createdAt })
        .from(SymbolAuditLogTable)
        .orderBy(desc(SymbolAuditLogTable.createdAt))
        .limit(1);

      const isHealthy = issues.length === 0;

      return {
        isHealthy,
        totalAuditLogs: totalResult.count,
        recentOperations: recentResult.count,
        lastOperation: lastOpResult?.createdAt || undefined,
        issues,
      };
    } catch (error) {
      this.logger.error('Failed to get audit service health status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        totalAuditLogs: 0,
        recentOperations: 0,
        issues: ['Failed to get health status'],
      };
    }
  }
}
