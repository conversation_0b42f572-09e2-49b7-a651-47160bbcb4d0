import { Test, TestingModule } from '@nestjs/testing';
import { SymbolDownloadQueueService } from './symbol-download.queue';
import { QueueService } from '@app/core/queue';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';

describe('SymbolDownloadQueueService', () => {
  let queueService: SymbolDownloadQueueService;
  let mockQueueService: jest.Mocked<QueueService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockErrorUtils: jest.Mocked<ErrorUtilsService>;
  let mockEnvService: jest.Mocked<EnvService>;

  const mockJob = {
    id: 'job-123',
    name: 'download-symbols-NSE-NSE-test-request',
    data: {
      exchange: 'NSE',
      segment: 'NSE',
      forceRefresh: false,
      batchSize: 500,
      requestId: 'test-request-123',
      scheduledAt: new Date('2024-01-15T10:00:00Z'),
      priority: 5,
    },
    opts: { attempts: 3 },
    timestamp: Date.now(),
    processedOn: Date.now(),
    finishedOn: Date.now(),
    attemptsMade: 1,
    progress: 100,
    returnvalue: { success: true },
    failedReason: null,
  };

  beforeEach(async () => {
    mockQueueService = {
      // Mock methods are added as needed
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(() => new Date('2024-01-15T10:00:00Z')),
      getTime: jest.fn(() => Date.now()),
    } as any;

    mockErrorUtils = {
      getErrorMessage: jest.fn((error) => error instanceof Error ? error.message : String(error)),
    } as any;

    mockEnvService = {
      get: jest.fn((key) => {
        const envVars: Record<string, string> = {
          'REDIS_HOST': 'localhost',
          'REDIS_PORT': '6379',
        };
        return envVars[key];
      }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SymbolDownloadQueueService,
        { provide: QueueService, useValue: mockQueueService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
        { provide: EnvService, useValue: mockEnvService },
      ],
    }).compile();

    queueService = module.get<SymbolDownloadQueueService>(SymbolDownloadQueueService);

    // Mock the inherited methods from BaseQueueService
    queueService['addJob'] = jest.fn().mockResolvedValue(mockJob);
    queueService['addJobWithPriority'] = jest.fn().mockResolvedValue(mockJob);
    queueService['addRepeatingJob'] = jest.fn().mockResolvedValue(mockJob);
    queueService['removeRepeatingJob'] = jest.fn().mockResolvedValue();
    queueService['getJob'] = jest.fn().mockResolvedValue(mockJob);
    queueService['getJobs'] = jest.fn().mockResolvedValue([mockJob]);
    queueService['removeJob'] = jest.fn().mockResolvedValue();
    queueService['getHealthStatus'] = jest.fn().mockResolvedValue({
      waiting: 5,
      active: 2,
      completed: 100,
      failed: 3,
      delayed: 1,
      paused: 0,
    });
    queueService['cleanQueue'] = jest.fn().mockResolvedValue(10);
    queueService['pause'] = jest.fn().mockResolvedValue();
    queueService['resume'] = jest.fn().mockResolvedValue();
    queueService['getRepeatingJob'] = jest.fn().mockResolvedValue(mockJob);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should initialize and setup daily cron job', async () => {
      await queueService.onModuleInit();

      expect(queueService['removeRepeatingJob']).toHaveBeenCalledWith('daily-symbol-master-download');
      expect(queueService['addRepeatingJob']).toHaveBeenCalledWith(
        'daily-symbol-master-download',
        expect.objectContaining({
          exchange: 'ALL',
          segment: 'ALL',
          forceRefresh: true,
        }),
        expect.objectContaining({
          pattern: expect.any(String),
          tz: expect.any(String),
        }),
        expect.any(Object)
      );
    });

    it('should handle initialization errors gracefully', async () => {
      queueService['removeRepeatingJob'] = jest.fn().mockRejectedValue(new Error('Setup failed'));

      await expect(queueService.onModuleInit()).rejects.toThrow('Setup failed');
    });
  });

  describe('addSymbolDownloadJob', () => {
    it('should add job successfully with default options', async () => {
      const result = await queueService.addSymbolDownloadJob('NSE', 'NSE');

      expect(result).toMatchObject({
        jobId: 'job-123',
        requestId: expect.stringMatching(/^manual-\d+-[a-z0-9]+$/),
      });

      expect(queueService['addJob']).toHaveBeenCalledWith(
        expect.stringMatching(/^download-symbols-NSE-NSE-manual-/),
        expect.objectContaining({
          exchange: 'NSE',
          segment: 'NSE',
          forceRefresh: false,
          batchSize: expect.any(Number),
          requestId: expect.any(String),
          scheduledAt: expect.any(Date),
          priority: 5,
        }),
        expect.objectContaining({
          removeOnComplete: 50,
          removeOnFail: 20,
        })
      );
    });

    it('should add job with custom options', async () => {
      const options = {
        forceRefresh: true,
        batchSize: 1000,
        priority: 1,
        delay: 5000,
        requestId: 'custom-request-123',
      };

      const result = await queueService.addSymbolDownloadJob('BSE', 'BSE', options);

      expect(result).toMatchObject({
        jobId: 'job-123',
        requestId: 'custom-request-123',
      });

      expect(queueService['addJobWithPriority']).toHaveBeenCalledWith(
        'download-symbols-BSE-BSE-custom-request-123',
        expect.objectContaining({
          exchange: 'BSE',
          segment: 'BSE',
          forceRefresh: true,
          batchSize: 1000,
          requestId: 'custom-request-123',
          priority: 1,
        }),
        1,
        expect.objectContaining({
          delay: 5000,
          removeOnComplete: 50,
          removeOnFail: 20,
        })
      );
    });

    it('should validate job data', async () => {
      // Test with invalid data that should fail Zod validation
      await expect(
        queueService.addSymbolDownloadJob('', '') // Empty strings should fail validation
      ).rejects.toThrow();
    });

    it('should handle job creation errors', async () => {
      queueService['addJob'] = jest.fn().mockRejectedValue(new Error('Queue error'));

      await expect(queueService.addSymbolDownloadJob('NSE', 'NSE')).rejects.toThrow();
    });
  });

  describe('addBulkSymbolDownloadJobs', () => {
    it('should add multiple jobs with staggered delays', async () => {
      const exchanges = [
        { exchange: 'NSE', segment: 'NSE' },
        { exchange: 'BSE', segment: 'BSE' },
        { exchange: 'NFO', segment: 'NFO-FUT' },
      ];

      const result = await queueService.addBulkSymbolDownloadJobs(exchanges, {
        staggerDelayMs: 1000,
        requestId: 'bulk-test',
      });

      expect(result).toMatchObject({
        jobs: expect.arrayContaining([
          expect.objectContaining({
            exchange: 'NSE',
            segment: 'NSE',
            jobId: 'job-123',
            requestId: expect.stringMatching(/^bulk-test-\d+$/),
          }),
        ]),
        totalJobs: 3,
      });

      expect(queueService['addJob']).toHaveBeenCalledTimes(3);
    });

    it('should handle bulk job creation errors', async () => {
      queueService['addJob'] = jest.fn()
        .mockResolvedValueOnce(mockJob)
        .mockRejectedValueOnce(new Error('Job creation failed'))
        .mockResolvedValueOnce(mockJob);

      const exchanges = [
        { exchange: 'NSE', segment: 'NSE' },
        { exchange: 'BSE', segment: 'BSE' },
        { exchange: 'NFO', segment: 'NFO-FUT' },
      ];

      await expect(queueService.addBulkSymbolDownloadJobs(exchanges)).rejects.toThrow();
    });
  });

  describe('addAllExchangesDownloadJobs', () => {
    it('should add jobs for all supported exchanges', async () => {
      const result = await queueService.addAllExchangesDownloadJobs();

      expect(result.totalJobs).toBeGreaterThan(0);
      expect(queueService['addJob']).toHaveBeenCalled();
    });
  });

  describe('getJobStatus', () => {
    it('should return enhanced job status', async () => {
      const mockJobWithState = {
        ...mockJob,
        getState: jest.fn().mockResolvedValue('completed'),
      };
      queueService['getJob'] = jest.fn().mockResolvedValue(mockJobWithState);

      const result = await queueService.getJobStatus('job-123');

      expect(result).toMatchObject({
        id: 'job-123',
        state: 'completed',
        progress: 100,
        attempts: 1,
        maxAttempts: 3,
        createdAt: expect.any(Date),
        processedAt: expect.any(Date),
        finishedAt: expect.any(Date),
        timestamp: expect.any(Date),
      });
    });

    it('should return null for non-existent job', async () => {
      queueService['getJob'] = jest.fn().mockResolvedValue(null);

      const result = await queueService.getJobStatus('non-existent');

      expect(result).toBeNull();
    });

    it('should handle job status errors', async () => {
      queueService['getJob'] = jest.fn().mockRejectedValue(new Error('Status error'));

      const result = await queueService.getJobStatus('job-123');

      expect(result).toBeNull();
    });
  });

  describe('getJobStatusByRequestId', () => {
    it('should return jobs matching request ID', async () => {
      const matchingJobs = [
        { ...mockJob, data: { ...mockJob.data, requestId: 'test-request' } },
        { ...mockJob, data: { ...mockJob.data, requestId: 'test-request' } },
      ];
      const nonMatchingJob = { ...mockJob, data: { ...mockJob.data, requestId: 'other-request' } };

      queueService['getJobs'] = jest.fn().mockResolvedValue([...matchingJobs, nonMatchingJob]);

      // Mock getState for each job
      matchingJobs.forEach(job => {
        job.getState = jest.fn().mockResolvedValue('completed');
      });

      const result = await queueService.getJobStatusByRequestId('test-request');

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: 'job-123',
        state: 'completed',
      });
    });

    it('should handle empty results', async () => {
      queueService['getJobs'] = jest.fn().mockResolvedValue([]);

      const result = await queueService.getJobStatusByRequestId('non-existent');

      expect(result).toHaveLength(0);
    });
  });

  describe('getQueueStats', () => {
    it('should return comprehensive queue statistics', async () => {
      queueService['getRepeatingJob'] = jest.fn().mockResolvedValue({
        opts: { repeat: { next: Date.now() + 3600000 } },
      });

      const result = await queueService.getQueueStats();

      expect(result).toMatchObject({
        waiting: 5,
        active: 2,
        completed: 100,
        failed: 3,
        delayed: 1,
        paused: 0,
        cronJobStatus: 'active',
        nextCronRun: expect.any(Date),
        totalProcessed: 103,
      });
    });

    it('should handle missing cron job', async () => {
      queueService['getRepeatingJob'] = jest.fn().mockResolvedValue(null);

      const result = await queueService.getQueueStats();

      expect(result.cronJobStatus).toBe('inactive');
    });
  });

  describe('cancelJob', () => {
    it('should cancel job successfully', async () => {
      const result = await queueService.cancelJob('job-123');

      expect(result).toBe(true);
      expect(queueService['removeJob']).toHaveBeenCalledWith('job-123');
    });

    it('should handle non-existent job', async () => {
      queueService['getJob'] = jest.fn().mockResolvedValue(null);

      const result = await queueService.cancelJob('non-existent');

      expect(result).toBe(false);
    });

    it('should handle cancellation errors', async () => {
      queueService['removeJob'] = jest.fn().mockRejectedValue(new Error('Cancel error'));

      await expect(queueService.cancelJob('job-123')).rejects.toThrow();
    });
  });

  describe('cancelJobsByRequestId', () => {
    it('should cancel multiple jobs by request ID', async () => {
      const matchingJobs = [
        { ...mockJob, id: 'job-1', data: { ...mockJob.data, requestId: 'test-request' } },
        { ...mockJob, id: 'job-2', data: { ...mockJob.data, requestId: 'test-request' } },
      ];

      queueService['getJobs'] = jest.fn().mockResolvedValue(matchingJobs);

      const result = await queueService.cancelJobsByRequestId('test-request');

      expect(result).toBe(2);
      expect(queueService['removeJob']).toHaveBeenCalledTimes(2);
    });

    it('should handle partial cancellation failures', async () => {
      const matchingJobs = [
        { ...mockJob, id: 'job-1', data: { ...mockJob.data, requestId: 'test-request' } },
        { ...mockJob, id: 'job-2', data: { ...mockJob.data, requestId: 'test-request' } },
      ];

      queueService['getJobs'] = jest.fn().mockResolvedValue(matchingJobs);
      queueService['removeJob'] = jest.fn()
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Cancel failed'));

      const result = await queueService.cancelJobsByRequestId('test-request');

      expect(result).toBe(1); // Only one successful cancellation
    });
  });

  describe('cleanOldJobs', () => {
    it('should clean old jobs with default options', async () => {
      const result = await queueService.cleanOldJobs();

      expect(result).toMatchObject({
        cleaned: 10,
        kept: 70, // 50 completed + 20 failed
      });

      expect(queueService['cleanQueue']).toHaveBeenCalledWith(
        expect.any(Number), // 24 hours in ms
        50, // keepCompleted
        20  // keepFailed
      );
    });

    it('should clean old jobs with custom options', async () => {
      const options = {
        olderThanHours: 48,
        keepCompleted: 100,
        keepFailed: 50,
      };

      const result = await queueService.cleanOldJobs(options);

      expect(queueService['cleanQueue']).toHaveBeenCalledWith(
        48 * 60 * 60 * 1000, // 48 hours in ms
        100,
        50
      );
    });
  });

  describe('queue management', () => {
    it('should pause queue', async () => {
      await queueService.pauseQueue();

      expect(queueService['pause']).toHaveBeenCalled();
    });

    it('should resume queue', async () => {
      await queueService.resumeQueue();

      expect(queueService['resume']).toHaveBeenCalled();
    });

    it('should handle pause/resume errors', async () => {
      queueService['pause'] = jest.fn().mockRejectedValue(new Error('Pause failed'));

      await expect(queueService.pauseQueue()).rejects.toThrow();
    });
  });

  describe('getComprehensiveHealthStatus', () => {
    it('should return healthy status', async () => {
      queueService['getRepeatingJob'] = jest.fn().mockResolvedValue({
        opts: { repeat: { next: Date.now() + 3600000 } },
      });

      const result = await queueService.getComprehensiveHealthStatus();

      expect(result).toMatchObject({
        isHealthy: true,
        queueStats: expect.any(Object),
        cronJobStatus: 'active',
        issues: [],
      });
    });

    it('should detect issues and return unhealthy status', async () => {
      queueService['getRepeatingJob'] = jest.fn().mockResolvedValue(null);
      queueService['getHealthStatus'] = jest.fn().mockResolvedValue({
        waiting: 150, // High number of waiting jobs
        active: 2,
        completed: 10,
        failed: 5, // High failure rate
        delayed: 1,
        paused: 0,
      });

      const result = await queueService.getComprehensiveHealthStatus();

      expect(result.isHealthy).toBe(false);
      expect(result.issues).toContain('Daily cron job is not active');
      expect(result.issues).toContain('High failure rate detected');
      expect(result.issues).toContain('Large number of waiting jobs');
    });
  });
});
