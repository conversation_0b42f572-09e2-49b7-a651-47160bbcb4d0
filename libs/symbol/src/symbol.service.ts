import { Injectable, Logger } from '@nestjs/common';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { KiteConnect } from 'kiteconnect';
import {
  KiteInstrumentMasterRaw,
  KiteInstrumentMasterRawSchema,
  SymbolMasterUpsert,
  SymbolMaster,
  SymbolDownloadResult,
} from './symbol.schema';
import { SymbolMasterRepository } from './symbol-master.repository';
import { SymbolAuditService } from './symbol-audit.service';

/**
 * Symbol service for managing symbol master data with Kite Connect integration
 *
 * Provides comprehensive symbol master data management including:
 * - Fetching instrument master data from Zerodha Kite Connect API
 * - Data transformation and validation
 * - Repository operations with QuestDB storage
 * - Error handling and retry mechanisms
 * - Performance monitoring and logging
 */
@Injectable()
export class SymbolService {
  private readonly logger = new Logger(SymbolService.name);
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second

  constructor(
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
    private readonly envService: EnvService,
    private readonly symbolRepository: SymbolMasterRepository,
    private readonly symbolAuditService: SymbolAuditService,
  ) {}

  // ==================== KITE CONNECT INTEGRATION ====================

  /**
   * Get KiteConnect instance with proper configuration
   */
  private getKiteConnectInstance(apiKey?: string, accessToken?: string): any {
    // For now, use a mock API key since we don't have env config set up
    const kiteApiKey = apiKey || 'demo_api_key';

    const kite = new KiteConnect({
      api_key: kiteApiKey,
    });

    if (accessToken) {
      kite.setAccessToken(accessToken);
    }

    return kite;
  }

  /**
   * Retry operation with exponential backoff
   */
  private async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.maxRetries,
    delay: number = this.retryDelay,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          break;
        }

        const backoffDelay = delay * Math.pow(2, attempt - 1);
        this.logger.warn(`Operation failed, retrying in ${backoffDelay}ms`, {
          attempt,
          maxRetries,
          error: this.errorUtils.getErrorMessage(error),
        });

        await new Promise((resolve) => setTimeout(resolve, backoffDelay));
      }
    }

    throw lastError!;
  }

  /**
   * Fetch instrument master data from Kite Connect API
   * Downloads the complete instrument list and parses it
   */
  async fetchKiteInstrumentMaster(exchange?: string): Promise<KiteInstrumentMasterRaw[]> {
    const startTime = this.dateTimeUtils.getTime();

    try {
      this.logger.log('Fetching instrument master from Kite Connect API', { exchange });

      // Get KiteConnect instance (no access token needed for instruments API)
      const kite = this.getKiteConnectInstance();

      // Fetch instruments data using the correct method
      let instruments: any[];
      if (exchange) {
        this.logger.debug(`Fetching instruments for exchange: ${exchange}`);
        instruments = await this.retryOperation(() => kite.getInstruments(exchange));
      } else {
        this.logger.debug('Fetching instruments for all exchanges');
        instruments = await this.retryOperation(() => kite.getInstruments());
      }

      this.logger.log(`Fetched ${instruments.length} instruments from Kite API`, {
        exchange: exchange || 'ALL',
        duration: `${this.dateTimeUtils.getTime() - startTime}ms`,
      });

      // Transform and validate the data
      const validatedInstruments: KiteInstrumentMasterRaw[] = [];
      const errors: string[] = [];

      for (const instrument of instruments) {
        try {
          // Transform Kite API response to our schema format
          const transformedInstrument = {
            instrument_token: String(instrument.instrument_token),
            exchange_token: String(instrument.exchange_token),
            tradingsymbol: instrument.tradingsymbol,
            name: instrument.name || '',
            last_price: Number(instrument.last_price) || 0,
            expiry: instrument.expiry || undefined,
            strike: instrument.strike ? Number(instrument.strike) : undefined,
            tick_size: Number(instrument.tick_size),
            lot_size: Number(instrument.lot_size),
            instrument_type: instrument.instrument_type,
            segment: instrument.segment,
            exchange: instrument.exchange,
          };

          const validatedInstrument = KiteInstrumentMasterRawSchema.parse(transformedInstrument);
          validatedInstruments.push(validatedInstrument);
        } catch (error) {
          errors.push(`Invalid instrument data: ${this.errorUtils.getErrorMessage(error)}`);
        }
      }

      if (errors.length > 0) {
        this.logger.warn(`Found ${errors.length} invalid instruments`, {
          sampleErrors: errors.slice(0, 5),
        });
      }

      this.logger.log(`Validated ${validatedInstruments.length} instruments`, {
        totalFetched: instruments.length,
        validCount: validatedInstruments.length,
        errorCount: errors.length,
      });

      return validatedInstruments;
    } catch (error) {
      this.logger.error('Failed to fetch instrument master from Kite API', {
        exchange,
        error: this.errorUtils.getErrorMessage(error),
        duration: `${this.dateTimeUtils.getTime() - startTime}ms`,
      });
      throw new Error(`Failed to fetch Kite instrument master: ${this.errorUtils.getErrorMessage(error)}`);
    }
  }

  /**
   * Fetch all symbols from all exchanges using Kite Connect API
   */
  async fetchAllSymbols(): Promise<KiteInstrumentMasterRaw[]> {
    this.logger.log('Fetching all symbols from all exchanges via Kite Connect');
    return this.fetchKiteInstrumentMaster();
  }

  /**
   * Fetch symbols by exchange and segment
   */
  async fetchSymbolsByExchange(exchange: string, segment?: string): Promise<KiteInstrumentMasterRaw[]> {
    this.logger.log(`Fetching symbols for ${exchange}${segment ? `:${segment}` : ''}`);

    const allSymbols = await this.fetchKiteInstrumentMaster(exchange);

    if (segment) {
      return allSymbols.filter((symbol) => symbol.segment === segment);
    }

    return allSymbols;
  }

  // ==================== REPOSITORY INTEGRATION ====================

  /**
   * Download and store symbol master data with comprehensive audit logging
   */
  async downloadAndStoreSymbolMaster(
    exchange?: string,
    segment?: string,
    forceRefresh: boolean = false,
    requestId?: string,
  ): Promise<SymbolDownloadResult> {
    const startTime = this.dateTimeUtils.getTime();
    const startedAt = this.dateTimeUtils.getUtcNow();
    const finalExchange = exchange || 'ALL';
    const finalSegment = segment || 'ALL';
    const finalRequestId = requestId || `download-${Date.now()}`;

    // Start audit logging
    const auditId = await this.symbolAuditService.logDownloadStart(
      finalExchange,
      finalSegment,
      finalRequestId,
      undefined, // jobId will be set by worker
      { forceRefresh },
    );

    try {
      this.logger.log('Starting symbol master download and storage', {
        auditId,
        exchange: finalExchange,
        segment: finalSegment,
        forceRefresh,
        requestId: finalRequestId,
      });

      // Fetch symbols from Kite API
      let kiteInstruments: KiteInstrumentMasterRaw[];
      if (exchange && segment) {
        kiteInstruments = await this.fetchSymbolsByExchange(exchange, segment);
      } else if (exchange) {
        kiteInstruments = await this.fetchKiteInstrumentMaster(exchange);
      } else {
        kiteInstruments = await this.fetchAllSymbols();
      }

      // Store in repository
      const result = await this.symbolRepository.upsertKiteInstrumentData(kiteInstruments);

      const completedAt = this.dateTimeUtils.getUtcNow();
      const duration = this.dateTimeUtils.getTime() - startTime;

      const downloadResult: SymbolDownloadResult = {
        exchange: finalExchange,
        segment: finalSegment,
        symbolsProcessed: result.totalProcessed,
        symbolsAdded: result.totalAdded,
        symbolsUpdated: result.totalUpdated,
        symbolsSkipped: kiteInstruments.length - result.totalProcessed,
        duration,
        startedAt,
        completedAt,
        errors: result.errors.map((e) => e.error),
      };

      // Log successful completion
      await this.symbolAuditService.logDownloadComplete(auditId, downloadResult, finalRequestId);

      this.logger.log('Symbol master download completed successfully', {
        auditId,
        requestId: finalRequestId,
        result: downloadResult,
      });

      return downloadResult;
    } catch (error) {
      const duration = this.dateTimeUtils.getTime() - startTime;

      // Log failure
      await this.symbolAuditService.logDownloadFailure(
        auditId,
        error instanceof Error ? error : new Error(String(error)),
        finalRequestId,
        finalExchange,
        finalSegment,
        duration,
      );

      this.logger.error('Symbol master download failed', {
        auditId,
        exchange: finalExchange,
        segment: finalSegment,
        requestId: finalRequestId,
        error: this.errorUtils.getErrorMessage(error),
        duration,
      });

      throw new Error(`Symbol master download failed: ${this.errorUtils.getErrorMessage(error)}`);
    }
  }

  /**
   * Upsert symbol data (legacy method for compatibility)
   */
  async upsertSymbol(symbolData: SymbolMasterUpsert, forceRefresh: boolean = false): Promise<{ isNew: boolean }> {
    try {
      const result = await this.symbolRepository.upsertSymbol(symbolData);
      return { isNew: result.isNew };
    } catch (error) {
      this.logger.error('Failed to upsert symbol', {
        instrumentToken: symbolData.instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Find symbol by instrument token
   */
  async findSymbolByToken(instrumentToken: string): Promise<SymbolMaster | null> {
    try {
      return await this.symbolRepository.findByInstrumentToken(instrumentToken);
    } catch (error) {
      this.logger.error('Failed to find symbol by token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Search symbols by trading symbol
   */
  async searchSymbols(searchTerm: string, limit: number = 50): Promise<SymbolMaster[]> {
    try {
      return await this.symbolRepository.searchSymbols(searchTerm, limit);
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Update last sync timestamp (legacy method for compatibility)
   */
  updateLastSyncTimestamp(exchange: string, segment: string): void {
    this.logger.debug('Last sync timestamp updated', { exchange, segment });
    // This is now handled automatically by the repository
  }

  /**
   * Cleanup stale symbols (legacy method for compatibility)
   */
  cleanupStaleSymbols(exchange: string, segment: string): void {
    this.logger.debug('Cleanup stale symbols called', { exchange, segment });
    // This could be implemented as a separate cleanup job if needed
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean;
    repositoryHealth: any;
    lastUpdate: Date | null;
  }> {
    try {
      const repositoryHealth = await this.symbolRepository.getHealthStatus();

      return {
        isHealthy: repositoryHealth.isHealthy,
        repositoryHealth,
        lastUpdate: repositoryHealth.lastUpdate,
      };
    } catch (error) {
      this.logger.error('Failed to get health status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        repositoryHealth: null,
        lastUpdate: null,
      };
    }
  }
}
