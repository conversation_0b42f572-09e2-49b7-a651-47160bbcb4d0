import { Test, TestingModule } from '@nestjs/testing';
import { SymbolService } from './symbol.service';
import { SymbolMasterRepository } from './symbol-master.repository';
import { SymbolAuditService } from './symbol-audit.service';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { KiteConnect } from 'kiteconnect';

// Mock KiteConnect
jest.mock('kiteconnect');

describe('SymbolService', () => {
  let service: SymbolService;
  let mockRepository: jest.Mocked<SymbolMasterRepository>;
  let mockAuditService: jest.Mocked<SymbolAuditService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockErrorUtils: jest.Mocked<ErrorUtilsService>;
  let mockEnvService: jest.Mocked<EnvService>;

  const mockKiteInstruments = [
    {
      instrument_token: '738561',
      exchange_token: '2885',
      tradingsymbol: 'RELIANCE',
      name: 'Reliance Industries Limited',
      last_price: 2450.50,
      expiry: undefined,
      strike: undefined,
      tick_size: 0.05,
      lot_size: 1,
      instrument_type: 'EQ',
      segment: 'NSE',
      exchange: 'NSE',
    },
    {
      instrument_token: '2953217',
      exchange_token: '11536',
      tradingsymbol: 'TCS',
      name: 'Tata Consultancy Services Limited',
      last_price: 3500.75,
      expiry: undefined,
      strike: undefined,
      tick_size: 0.05,
      lot_size: 1,
      instrument_type: 'EQ',
      segment: 'NSE',
      exchange: 'NSE',
    },
  ];

  const mockSymbolMaster = {
    instrumentToken: '738561',
    exchangeToken: '2885',
    tradingSymbol: 'RELIANCE',
    name: 'Reliance Industries Limited',
    lastPrice: 2450.50,
    expiry: undefined,
    strike: undefined,
    tickSize: 0.05,
    lotSize: 1,
    instrumentType: 'EQ',
    segment: 'NSE',
    exchange: 'NSE',
    isActive: true,
    downloadedAt: new Date('2024-01-15T08:00:00Z'),
    updatedAt: new Date('2024-01-15T08:00:00Z'),
  };

  beforeEach(async () => {
    // Create mocks
    mockRepository = {
      findByInstrumentToken: jest.fn(),
      findSymbols: jest.fn(),
      searchSymbols: jest.fn(),
      upsertSymbol: jest.fn(),
      upsertKiteInstrumentData: jest.fn(),
      getStats: jest.fn(),
      getHealthStatus: jest.fn(),
    } as any;

    mockAuditService = {
      logDownloadStart: jest.fn(),
      logDownloadComplete: jest.fn(),
      logDownloadFailure: jest.fn(),
      logSymbolOperation: jest.fn(),
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(() => new Date('2024-01-15T10:00:00Z')),
      getTime: jest.fn(() => Date.now()),
    } as any;

    mockErrorUtils = {
      getErrorMessage: jest.fn((error) => error instanceof Error ? error.message : String(error)),
    } as any;

    mockEnvService = {
      get: jest.fn((key) => {
        const envVars: Record<string, string> = {
          'ZERODHA_API_KEY': 'test_api_key',
        };
        return envVars[key];
      }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SymbolService,
        { provide: SymbolMasterRepository, useValue: mockRepository },
        { provide: SymbolAuditService, useValue: mockAuditService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
        { provide: EnvService, useValue: mockEnvService },
      ],
    }).compile();

    service = module.get<SymbolService>(SymbolService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchKiteInstrumentMaster', () => {
    it('should fetch and validate instruments from Kite API', async () => {
      // Mock KiteConnect instance
      const mockKite = {
        getInstruments: jest.fn().mockResolvedValue(mockKiteInstruments),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      const result = await service.fetchKiteInstrumentMaster();

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        instrument_token: '738561',
        tradingsymbol: 'RELIANCE',
        exchange: 'NSE',
      });
      expect(mockKite.getInstruments).toHaveBeenCalledWith();
    });

    it('should fetch instruments for specific exchange', async () => {
      const mockKite = {
        getInstruments: jest.fn().mockResolvedValue(mockKiteInstruments),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      const result = await service.fetchKiteInstrumentMaster('NSE');

      expect(result).toHaveLength(2);
      expect(mockKite.getInstruments).toHaveBeenCalledWith('NSE');
    });

    it('should handle API errors gracefully', async () => {
      const mockKite = {
        getInstruments: jest.fn().mockRejectedValue(new Error('API Error')),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      await expect(service.fetchKiteInstrumentMaster()).rejects.toThrow('Failed to fetch Kite instrument master');
    });

    it('should filter out invalid instruments', async () => {
      const invalidInstruments = [
        ...mockKiteInstruments,
        {
          // Missing required fields
          instrument_token: '',
          tradingsymbol: '',
        },
      ];

      const mockKite = {
        getInstruments: jest.fn().mockResolvedValue(invalidInstruments),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      const result = await service.fetchKiteInstrumentMaster();

      // Should only return valid instruments
      expect(result).toHaveLength(2);
    });
  });

  describe('downloadAndStoreSymbolMaster', () => {
    it('should download and store symbols successfully', async () => {
      // Mock successful operations
      const mockKite = {
        getInstruments: jest.fn().mockResolvedValue(mockKiteInstruments),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      mockAuditService.logDownloadStart.mockResolvedValue(1);
      mockRepository.upsertKiteInstrumentData.mockResolvedValue({
        totalProcessed: 2,
        totalAdded: 1,
        totalUpdated: 1,
        errors: [],
      });

      const result = await service.downloadAndStoreSymbolMaster('NSE', 'NSE', false, 'test-request');

      expect(result).toMatchObject({
        exchange: 'NSE',
        segment: 'NSE',
        symbolsProcessed: 2,
        symbolsAdded: 1,
        symbolsUpdated: 1,
        symbolsSkipped: 0,
        errors: [],
      });

      expect(mockAuditService.logDownloadStart).toHaveBeenCalledWith(
        'NSE',
        'NSE',
        'test-request',
        undefined,
        { forceRefresh: false }
      );
      expect(mockAuditService.logDownloadComplete).toHaveBeenCalled();
    });

    it('should handle download failures and log them', async () => {
      const mockKite = {
        getInstruments: jest.fn().mockRejectedValue(new Error('Network error')),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      mockAuditService.logDownloadStart.mockResolvedValue(1);

      await expect(
        service.downloadAndStoreSymbolMaster('NSE', 'NSE', false, 'test-request')
      ).rejects.toThrow('Symbol master download failed');

      expect(mockAuditService.logDownloadFailure).toHaveBeenCalledWith(
        1,
        expect.any(Error),
        'test-request',
        'NSE',
        'NSE',
        expect.any(Number)
      );
    });

    it('should use default values when parameters are not provided', async () => {
      const mockKite = {
        getInstruments: jest.fn().mockResolvedValue(mockKiteInstruments),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      mockAuditService.logDownloadStart.mockResolvedValue(1);
      mockRepository.upsertKiteInstrumentData.mockResolvedValue({
        totalProcessed: 2,
        totalAdded: 2,
        totalUpdated: 0,
        errors: [],
      });

      const result = await service.downloadAndStoreSymbolMaster();

      expect(result.exchange).toBe('ALL');
      expect(result.segment).toBe('ALL');
      expect(mockAuditService.logDownloadStart).toHaveBeenCalledWith(
        'ALL',
        'ALL',
        expect.stringMatching(/^download-\d+$/),
        undefined,
        { forceRefresh: false }
      );
    });
  });

  describe('findSymbolByToken', () => {
    it('should find symbol by instrument token', async () => {
      mockRepository.findByInstrumentToken.mockResolvedValue(mockSymbolMaster);

      const result = await service.findSymbolByToken('738561');

      expect(result).toEqual(mockSymbolMaster);
      expect(mockRepository.findByInstrumentToken).toHaveBeenCalledWith('738561');
    });

    it('should return null when symbol not found', async () => {
      mockRepository.findByInstrumentToken.mockResolvedValue(null);

      const result = await service.findSymbolByToken('999999');

      expect(result).toBeNull();
      expect(mockRepository.findByInstrumentToken).toHaveBeenCalledWith('999999');
    });

    it('should handle repository errors', async () => {
      mockRepository.findByInstrumentToken.mockRejectedValue(new Error('Database error'));

      await expect(service.findSymbolByToken('738561')).rejects.toThrow('Database error');
    });
  });

  describe('searchSymbols', () => {
    it('should search symbols by trading symbol', async () => {
      const mockResults = [mockSymbolMaster];
      mockRepository.searchSymbols.mockResolvedValue(mockResults);

      const result = await service.searchSymbols('RELIANCE', 10);

      expect(result).toEqual(mockResults);
      expect(mockRepository.searchSymbols).toHaveBeenCalledWith('RELIANCE', 10);
    });

    it('should use default limit when not provided', async () => {
      mockRepository.searchSymbols.mockResolvedValue([]);

      await service.searchSymbols('TEST');

      expect(mockRepository.searchSymbols).toHaveBeenCalledWith('TEST', 50);
    });

    it('should handle search errors', async () => {
      mockRepository.searchSymbols.mockRejectedValue(new Error('Search error'));

      await expect(service.searchSymbols('RELIANCE')).rejects.toThrow('Search error');
    });
  });

  describe('getHealthStatus', () => {
    it('should return healthy status when all services are healthy', async () => {
      const mockRepositoryHealth = {
        isHealthy: true,
        tableExists: true,
        connectionStatus: true,
        lastUpdate: new Date(),
        recordCount: 1000,
      };

      mockRepository.getHealthStatus.mockResolvedValue(mockRepositoryHealth);

      const result = await service.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: true,
        repositoryHealth: mockRepositoryHealth,
        lastUpdate: mockRepositoryHealth.lastUpdate,
      });
    });

    it('should return unhealthy status when repository is unhealthy', async () => {
      const mockRepositoryHealth = {
        isHealthy: false,
        tableExists: false,
        connectionStatus: false,
        lastUpdate: null,
        recordCount: 0,
      };

      mockRepository.getHealthStatus.mockResolvedValue(mockRepositoryHealth);

      const result = await service.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: false,
        repositoryHealth: mockRepositoryHealth,
        lastUpdate: null,
      });
    });

    it('should handle health check errors gracefully', async () => {
      mockRepository.getHealthStatus.mockRejectedValue(new Error('Health check failed'));

      const result = await service.getHealthStatus();

      expect(result).toMatchObject({
        isHealthy: false,
        repositoryHealth: null,
        lastUpdate: null,
      });
    });
  });

  describe('retry mechanism', () => {
    it('should retry failed operations', async () => {
      const mockKite = {
        getInstruments: jest.fn()
          .mockRejectedValueOnce(new Error('Temporary error'))
          .mockRejectedValueOnce(new Error('Temporary error'))
          .mockResolvedValue(mockKiteInstruments),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      const result = await service.fetchKiteInstrumentMaster();

      expect(result).toHaveLength(2);
      expect(mockKite.getInstruments).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retries', async () => {
      const mockKite = {
        getInstruments: jest.fn().mockRejectedValue(new Error('Persistent error')),
      };
      (KiteConnect as jest.MockedClass<typeof KiteConnect>).mockImplementation(() => mockKite as any);

      await expect(service.fetchKiteInstrumentMaster()).rejects.toThrow('Failed to fetch Kite instrument master');
      expect(mockKite.getInstruments).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });
});
