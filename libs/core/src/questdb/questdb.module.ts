import { Module } from '@nestjs/common';
import { QuestDBService } from './questdb.service';
import { HealthCheckLibModule } from '../health-check/health-check.module';
import { EnvModule } from '../env/env.module';

/**
 * QuestDB Module
 *
 * Provides QuestDB service for high-performance time-series database operations.
 * Follows the same architectural patterns as DrizzleModule for consistency.
 *
 * Features:
 * - Connection pooling and management
 * - Automatic reconnection with exponential backoff
 * - Health monitoring and status reporting
 * - Time-series specific operations
 * - Integration with existing core services
 *
 * Dependencies:
 * - HealthCheckLibModule: For health status reporting
 * - EnvModule: For configuration management
 *
 * Exports:
 * - QuestDBService: Main service for QuestDB operations
 */
@Module({
  imports: [HealthCheckLibModule, EnvModule],
  providers: [QuestDBService],
  exports: [QuestDBService],
})
export class QuestDBModule {}
