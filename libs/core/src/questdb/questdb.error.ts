import { BaseError } from '@app/common/errors';
import { z } from 'zod/v4';

/**
 * QuestDB error types enum
 * Defines all possible error scenarios for QuestDB operations
 */
export const QuestDBErrorEnum = z.enum([
  'CONNECTION_FAILED',
  'QUERY_EXECUTION_FAILED',
  'INVALID_TIME_SERIES_DATA',
  'BULK_INSERT_FAILED',
  'CONFIGURATION_INVALID',
  'CONNECTION_TIMEOUT',
  'HEALTH_CHECK_FAILED',
  'POOL_EXHAUSTED',
  'TRANSACTION_FAILED',
  'SCHEMA_VALIDATION_FAILED',
  'TIMESTAMP_FORMAT_INVALID',
  'CONNECTION_POOL_ERROR',
]);

/**
 * Error messages mapping for QuestDB errors
 */
export const QuestDBErrorMessages: Record<QuestDBErrorEnumType, string> = {
  CONNECTION_FAILED: 'Failed to establish connection to QuestDB',
  QUERY_EXECUTION_FAILED: 'Query execution failed',
  INVALID_TIME_SERIES_DATA: 'Invalid time-series data format',
  BULK_INSERT_FAILED: 'Bulk insert operation failed',
  CONFIGURATION_INVALID: 'QuestDB configuration is invalid',
  CONNECTION_TIMEOUT: 'Connection to QuestDB timed out',
  HEALTH_CHECK_FAILED: 'QuestDB health check failed',
  POOL_EXHAUSTED: 'Connection pool exhausted',
  TRANSACTION_FAILED: 'Database transaction failed',
  SCHEMA_VALIDATION_FAILED: 'Schema validation failed',
  TIMESTAMP_FORMAT_INVALID: 'Invalid timestamp format for time-series data',
  CONNECTION_POOL_ERROR: 'Connection pool error occurred',
};

/**
 * Type definition for QuestDB error enum
 */
export type QuestDBErrorEnumType = z.output<typeof QuestDBErrorEnum>;

/**
 * QuestDB-specific error class
 * Extends BaseError with QuestDB domain-specific error handling
 */
export class QuestDBError extends BaseError<QuestDBErrorEnumType> {
  constructor(name: QuestDBErrorEnumType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain: 'QUESTDB' as const,
      message: details?.message || QuestDBErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

/**
 * Helper function to create QuestDB connection errors
 */
export function createQuestDBConnectionError(cause?: unknown, customMessage?: string): QuestDBError {
  return new QuestDBError('CONNECTION_FAILED', {
    message: customMessage,
    cause,
  });
}

/**
 * Helper function to create QuestDB query execution errors
 */
export function createQuestDBQueryError(query: string, cause?: unknown): QuestDBError {
  return new QuestDBError('QUERY_EXECUTION_FAILED', {
    message: `Query execution failed: ${query.substring(0, 100)}${query.length > 100 ? '...' : ''}`,
    cause,
  });
}

/**
 * Helper function to create QuestDB time-series data errors
 */
export function createQuestDBTimeSeriesError(data: unknown, cause?: unknown): QuestDBError {
  return new QuestDBError('INVALID_TIME_SERIES_DATA', {
    message: `Invalid time-series data: ${JSON.stringify(data).substring(0, 200)}`,
    cause,
  });
}

/**
 * Helper function to create QuestDB bulk insert errors
 */
export function createQuestDBBulkInsertError(recordCount: number, cause?: unknown): QuestDBError {
  return new QuestDBError('BULK_INSERT_FAILED', {
    message: `Bulk insert failed for ${recordCount} records`,
    cause,
  });
}

/**
 * Helper function to create QuestDB configuration errors
 */
export function createQuestDBConfigError(configField: string, cause?: unknown): QuestDBError {
  return new QuestDBError('CONFIGURATION_INVALID', {
    message: `Invalid QuestDB configuration for field: ${configField}`,
    cause,
  });
}
