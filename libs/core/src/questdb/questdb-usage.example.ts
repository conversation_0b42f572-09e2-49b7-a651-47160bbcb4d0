/**
 * QuestDB Service Usage Examples
 *
 * This file demonstrates how to use the QuestDB service for various
 * time-series database operations in the PatternTrade API.
 *
 * The QuestDB service provides high-performance time-series database
 * operations using QuestDB via PostgreSQL wire protocol.
 */

import { Injectable, Logger } from '@nestjs/common';
import { QuestDBService, OHLCVData, TimeSeriesInsertOptions, TimeSeriesQueryOptions } from './questdb.service';
import { DateTimeUtilsService } from '@app/utils';
import { QuestDBError } from './questdb.error';

@Injectable()
export class QuestDBUsageExample {
  private readonly logger = new Logger(QuestDBUsageExample.name);

  constructor(
    private readonly questdbService: QuestDBService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Example 1: Basic time-series data insertion
   * Demonstrates how to insert individual time-series records
   */
  async insertMarketTickData(): Promise<void> {
    try {
      const tickData: TimeSeriesInsertOptions = {
        table: 'market_ticks',
        timestamp: this.dateTimeUtils.getNewDate(),
        data: {
          symbol: 'AAPL',
          price: 150.25,
          volume: 1000,
          exchange: 'NASDAQ',
          bid: 150.2,
          ask: 150.3,
        },
      };

      const result = await this.questdbService.insertTimeSeriesData(tickData);
      this.logger.log(`Inserted tick data: ${result.rowCount} rows affected`);
    } catch (error) {
      this.logger.error('Failed to insert tick data:', error);
    }
  }

  /**
   * Example 2: Bulk insertion for high-throughput scenarios
   * Demonstrates how to efficiently insert large volumes of market data
   */
  async bulkInsertMarketData(): Promise<void> {
    try {
      // Generate sample market data
      const marketData = Array.from({ length: 1000 }, (_, i) => ({
        symbol: i % 2 === 0 ? 'AAPL' : 'GOOGL',
        timestamp: this.dateTimeUtils.getNewDate(i * 1000).toISOString(),
        price: 150 + Math.random() * 10,
        volume: Math.floor(Math.random() * 10000),
        exchange: 'NASDAQ',
      }));

      const result = await this.questdbService.bulkInsertTimeSeriesData('market_ticks', marketData);

      this.logger.log(`Bulk inserted ${result.rowCount} market data records`);
    } catch (error) {
      this.logger.error('Failed to bulk insert market data:', error);
    }
  }

  /**
   * Example 3: OHLCV data operations
   * Demonstrates how to work with candlestick/OHLCV data
   */
  async insertCandlestickData(): Promise<void> {
    try {
      const ohlcvData: OHLCVData = {
        symbol: 'AAPL',
        timestamp: this.dateTimeUtils.getNewDate(),
        open: 150.0,
        high: 155.5,
        low: 149.75,
        close: 154.25,
        volume: 2500000,
      };

      const result = await this.questdbService.insertOHLCVData(ohlcvData, 'candles_1m');
      this.logger.log(`Inserted OHLCV data: ${result.rowCount} rows affected`);
    } catch (error) {
      this.logger.error('Failed to insert OHLCV data:', error);
    }
  }

  /**
   * Example 4: Bulk OHLCV data insertion
   * Demonstrates efficient insertion of multiple candlestick records
   */
  async bulkInsertCandlestickData(): Promise<void> {
    try {
      const candleData: OHLCVData[] = [
        {
          symbol: 'AAPL',
          timestamp: '2023-01-01T09:30:00.000Z',
          open: 150.0,
          high: 152.0,
          low: 149.5,
          close: 151.75,
          volume: 1000000,
        },
        {
          symbol: 'AAPL',
          timestamp: '2023-01-01T09:31:00.000Z',
          open: 151.75,
          high: 153.25,
          low: 151.0,
          close: 152.5,
          volume: 850000,
        },
        // Add more candle data...
      ];

      const result = await this.questdbService.bulkInsertOHLCVData(candleData, 'candles_1m');
      this.logger.log(`Bulk inserted ${result.rowCount} candlestick records`);
    } catch (error) {
      this.logger.error('Failed to bulk insert candlestick data:', error);
    }
  }

  /**
   * Example 5: Time-series data querying
   * Demonstrates how to query time-series data with various filters
   */
  async queryMarketDataByTimeRange(): Promise<void> {
    try {
      const queryOptions: TimeSeriesQueryOptions = {
        table: 'market_ticks',
        startTime: '2023-01-01T09:30:00.000Z',
        endTime: '2023-01-01T16:00:00.000Z',
        columns: ['symbol', 'timestamp', 'price', 'volume'],
        orderBy: 'DESC',
        limit: 1000,
      };

      const result = await this.questdbService.queryTimeSeriesData(queryOptions);
      this.logger.log(`Retrieved ${result.data.length} market data records`);

      // Process the results
      result.data.forEach((record) => {
        this.logger.debug(`${String(record.symbol)}: $${String(record.price)} at ${String(record.timestamp)}`);
      });
    } catch (error) {
      this.logger.error('Failed to query market data:', error);
    }
  }

  /**
   * Example 6: Getting latest market data
   * Demonstrates how to retrieve the most recent data for specific symbols
   */
  async getLatestPrices(): Promise<void> {
    try {
      const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA'];
      const result = await this.questdbService.getLatestMarketData(symbols, 'candles_1m');

      this.logger.log(`Retrieved latest data for ${result.data.length} symbols`);

      result.data.forEach((record) => {
        this.logger.log(`${String(record.symbol)}: $${String(record.close)} (${String(record.timestamp)})`);
      });
    } catch (error) {
      this.logger.error('Failed to get latest prices:', error);
    }
  }

  /**
   * Example 7: Complex aggregation queries
   * Demonstrates advanced QuestDB queries for analytics
   */
  async performMarketAnalytics(): Promise<void> {
    try {
      // Calculate average price and volume for the last hour
      const query = `
        SELECT 
          symbol,
          AVG(price) as avg_price,
          SUM(volume) as total_volume,
          COUNT(*) as tick_count,
          MIN(price) as min_price,
          MAX(price) as max_price
        FROM market_ticks
        WHERE timestamp > dateadd('h', -1, now())
        GROUP BY symbol
        ORDER BY total_volume DESC
      `;

      const result = await this.questdbService.executeQuery(query);
      this.logger.log(`Analytics results: ${result.data.length} symbols analyzed`);

      result.data.forEach((analytics) => {
        this.logger.log(
          `${String(analytics.symbol)}: Avg $${Number(analytics.avg_price).toFixed(2)}, ` +
            `Volume: ${String(analytics.total_volume)}, Ticks: ${String(analytics.tick_count)}`,
        );
      });
    } catch (error) {
      this.logger.error('Failed to perform market analytics:', error);
    }
  }

  /**
   * Example 8: Transaction-based operations
   * Demonstrates how to use transactions for data consistency
   */
  async performTransactionalInsert(): Promise<void> {
    try {
      const queries = [
        {
          query: 'INSERT INTO market_ticks (symbol, timestamp, price, volume) VALUES ($1, $2, $3, $4)',
          params: ['AAPL', this.dateTimeUtils.getUtcNow(), 150.25, 1000],
        },
        {
          query: 'INSERT INTO trade_log (symbol, timestamp, action, quantity) VALUES ($1, $2, $3, $4)',
          params: ['AAPL', this.dateTimeUtils.getUtcNow(), 'BUY', 100],
        },
      ];

      const results = await this.questdbService.executeTransaction(queries);
      this.logger.log(`Transaction completed: ${results.length} queries executed`);
    } catch (error) {
      this.logger.error('Transaction failed:', error);
    }
  }

  /**
   * Example 9: Health monitoring
   * Demonstrates how to monitor QuestDB connection health
   */
  monitorConnectionHealth(): void {
    try {
      const isHealthy = this.questdbService.isConnectionHealthy();
      const stats = this.questdbService.getConnectionStats();

      this.logger.log(`QuestDB Connection Health: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);
      this.logger.log(`Connection Stats:`, {
        totalConnections: stats.totalConnections,
        idleConnections: stats.idleConnections,
        waitingCount: stats.waitingCount,
        healthCheckCount: stats.healthCheckCount,
        failedHealthChecks: stats.failedHealthChecks,
      });
    } catch (error) {
      this.logger.error('Failed to check connection health:', error);
    }
  }

  /**
   * Example 10: Error handling patterns
   * Demonstrates proper error handling for QuestDB operations
   */
  async demonstrateErrorHandling(): Promise<void> {
    try {
      // This will fail due to invalid table name
      await this.questdbService.executeQuery('SELECT * FROM non_existent_table');
    } catch (error) {
      if (error instanceof QuestDBError) {
        this.logger.error(`QuestDB Error [${error.name}]: ${error.message}`);

        // Handle specific error types
        switch (error.name) {
          case 'CONNECTION_FAILED':
            this.logger.error('Database connection issue - check configuration');
            break;
          case 'QUERY_EXECUTION_FAILED':
            this.logger.error('SQL query issue - check syntax and table names');
            break;
          case 'INVALID_TIME_SERIES_DATA':
            this.logger.error('Data format issue - check timestamp and data structure');
            break;
          default:
            this.logger.error('Unknown QuestDB error');
        }
      } else {
        this.logger.error('Non-QuestDB error:', error);
      }
    }
  }
}
