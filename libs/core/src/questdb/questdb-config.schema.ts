import { z } from 'zod/v4';

/**
 * QuestDB connection configuration schema
 * Used for validating and typing QuestDB connection parameters
 * Leverages Zod 4's improved validation and error handling
 */
export const QuestDBConnectionConfigSchema = z
  .object({
    host: z.string().min(1, 'Host cannot be empty').describe('QuestDB server hostname or IP address'),
    port: z.coerce
      .number()
      .int('Port must be an integer')
      .min(1, 'Port must be greater than 0')
      .max(65535, 'Port must be less than or equal to 65535')
      .describe('QuestDB server port number'),
    database: z.string().min(1, 'Database name cannot be empty').describe('QuestDB database name'),
    username: z.string().optional().describe('Optional username for authentication'),
    password: z.string().optional().describe('Optional password for authentication'),
    ssl: z.boolean().describe('Whether to use SSL/TLS encryption'),
    maxConnections: z
      .number()
      .int('Max connections must be an integer')
      .positive('Max connections must be positive')
      .describe('Maximum number of concurrent connections'),
    connectionTimeoutMillis: z
      .number()
      .int('Connection timeout must be an integer')
      .positive('Connection timeout must be positive')
      .optional()
      .describe('Connection timeout in milliseconds'),
    idleTimeoutMillis: z
      .number()
      .int('Idle timeout must be an integer')
      .positive('Idle timeout must be positive')
      .optional()
      .describe('Idle connection timeout in milliseconds'),
  })
  .describe('QuestDB connection configuration parameters');

/**
 * Type definition for QuestDB connection configuration
 * Exported from Zod schema following PatternTrade API standards
 */
export type QuestDBConnectionConfig = z.output<typeof QuestDBConnectionConfigSchema>;

/**
 * Default configuration values for QuestDB connections
 * Uses Zod 4's improved type inference for better type safety
 */
export const DEFAULT_QUESTDB_CONFIG: Partial<QuestDBConnectionConfig> = {
  host: 'localhost',
  port: 8812,
  database: 'qdb',
  ssl: false,
  maxConnections: 10,
  connectionTimeoutMillis: 30000, // 30 seconds
  idleTimeoutMillis: 10000, // 10 seconds
} as const;

/**
 * Schema for environment variables used to create QuestDB configuration
 * Provides better validation and error messages for environment setup
 */
export const QuestDBEnvSchema = z.object({
  QUESTDB_HOST: z.string().min(1, 'QUESTDB_HOST environment variable is required').describe('QuestDB server hostname'),
  QUESTDB_PORT: z.coerce
    .number()
    .int('QUESTDB_PORT must be an integer')
    .min(1, 'QUESTDB_PORT must be greater than 0')
    .max(65535, 'QUESTDB_PORT must be a valid port number')
    .describe('QuestDB server port'),
  QUESTDB_DATABASE: z
    .string()
    .min(1, 'QUESTDB_DATABASE environment variable is required')
    .describe('QuestDB database name'),
  QUESTDB_USERNAME: z.string().optional().describe('Optional QuestDB username'),
  QUESTDB_PASSWORD: z.string().optional().describe('Optional QuestDB password'),
  QUESTDB_SSL: z.coerce.boolean().describe('Whether to use SSL for QuestDB connection'),
  QUESTDB_MAX_CONNECTIONS: z.coerce
    .number()
    .int('QUESTDB_MAX_CONNECTIONS must be an integer')
    .positive('QUESTDB_MAX_CONNECTIONS must be positive')
    .describe('Maximum number of QuestDB connections'),
});

/**
 * Type for QuestDB environment variables
 */
export type QuestDBEnv = z.output<typeof QuestDBEnvSchema>;

/**
 * Validates and creates a QuestDB connection configuration from environment variables
 * Uses Zod 4's improved parsing and error handling
 * @param env - Environment variables object
 * @returns Validated QuestDB connection configuration
 * @throws {ZodError} When environment variables are invalid
 */
export function createQuestDBConfig(env: QuestDBEnv): QuestDBConnectionConfig {
  // First validate the environment variables
  const validatedEnv = QuestDBEnvSchema.parse(env);

  // Create configuration object with defaults
  const config: QuestDBConnectionConfig = {
    ...DEFAULT_QUESTDB_CONFIG,
    host: validatedEnv.QUESTDB_HOST,
    port: validatedEnv.QUESTDB_PORT,
    database: validatedEnv.QUESTDB_DATABASE,
    username: validatedEnv.QUESTDB_USERNAME,
    password: validatedEnv.QUESTDB_PASSWORD,
    ssl: validatedEnv.QUESTDB_SSL,
    maxConnections: validatedEnv.QUESTDB_MAX_CONNECTIONS,
  };

  // Final validation of the complete configuration
  return QuestDBConnectionConfigSchema.parse(config);
}
