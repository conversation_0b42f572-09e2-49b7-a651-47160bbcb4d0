import { Injectable, Logger } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';

@Injectable()
export class SchedulerService {
  constructor(private readonly schedulerRegistry: SchedulerRegistry) {}
  private logger = new Logger(SchedulerService.name);

  addInterval(name: string, milliseconds: number, callback: () => Promise<void>) {
    const interval = setInterval(() => {
      callback().catch((error) => {
        this.logger.error(`Error in interval '${name}':`, error);
      });
    }, milliseconds);
    this.schedulerRegistry.addInterval(name, interval);
    this.logger.log(`Interval ${name} added with ${milliseconds} milliseconds`);
  }

  deleteInterval(name: string) {
    this.schedulerRegistry.deleteInterval(name);
    this.logger.log(`Interval ${name} deleted`);
  }
}
