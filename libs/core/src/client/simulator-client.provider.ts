import { constant } from '@app/common/constants';
import type { FactoryProvider } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxyFactory } from '@nestjs/microservices';
import { EnvService } from '../env';
import { ErrorUtilsService } from '@app/utils';
import { getRedisTransportOption } from '../transport';
import { RedisTransportConfigSchema } from './client.schema';

export const SimulatorClientProvider: FactoryProvider = {
  provide: constant.SIMULATOR_CLIENT,
  useFactory: (envService: EnvService, errorUtiils: ErrorUtilsService) => {
    // Get the Redis transport options
    const transportOptions = getRedisTransportOption(envService);

    // Validate the transport configuration with Zod
    try {
      RedisTransportConfigSchema.parse(transportOptions.options);
    } catch (error) {
      throw new Error(
        `Invalid Redis transport configuration for Simulator client: ${errorUtiils.getErrorMessage(error)}`,
      );
    }

    return ClientProxyFactory.create(transportOptions);
  },
  inject: [EnvService, ErrorUtilsService],
};

export function SimulatorClient() {
  return Inject(constant.SIMULATOR_CLIENT);
}
