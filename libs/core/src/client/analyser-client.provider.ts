import { constant } from '@app/common/constants';
import type { FactoryProvider } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxyFactory } from '@nestjs/microservices';
import { EnvService } from '../env';
import { getRedisTransportOption } from '../transport';
import { RedisTransportConfigSchema } from './client.schema';
import { ErrorUtilsService } from '../../../utils/src/error-utils.service';

export const AnalyserClientProvider: FactoryProvider = {
  provide: constant.ANALYSER_CLIENT,
  useFactory: (envService: EnvService, errorUtiils: ErrorUtilsService) => {
    // Get the Redis transport options
    const transportOptions = getRedisTransportOption(envService);

    // Validate the transport configuration with Zod
    try {
      RedisTransportConfigSchema.parse(transportOptions.options);
    } catch (error) {
      throw new Error(
        `Invalid Redis transport configuration for Analyser client: ${errorUtiils.getErrorMessage(error)}`,
      );
    }

    return ClientProxyFactory.create(transportOptions);
  },
  inject: [EnvService, ErrorUtilsService],
};

export function AnalyserClient() {
  return Inject(constant.ANALYSER_CLIENT);
}
