import { Test, TestingModule } from '@nestjs/testing';
import { ClientProxy } from '@nestjs/microservices';
import { DatastoreClientProvider, DatastoreClient, checkDatastoreServiceHealth } from './datastore-client.provider';
import { EnvService } from '../env/env.service';
import { ErrorUtilsService } from '@app/utils';

describe('DatastoreClientProvider', () => {
  let module: TestingModule;
  let client: ClientProxy;
  let envService: EnvService;
  let errorUtils: ErrorUtilsService;

  const mockEnvService = {
    get: jest.fn(),
  };

  const mockErrorUtils = {
    getErrorMessage: jest.fn((error) => error.message || 'Unknown error'),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        DatastoreClientProvider,
        {
          provide: EnvService,
          useValue: mockEnvService,
        },
        {
          provide: ErrorUtilsService,
          useValue: mockErrorUtils,
        },
      ],
    }).compile();

    envService = module.get<EnvService>(EnvService);
    errorUtils = module.get<ErrorUtilsService>(ErrorUtilsService);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe('DatastoreClientProvider', () => {
    beforeEach(() => {
      mockEnvService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'REDIS_HOST':
            return 'localhost';
          case 'REDIS_PORT':
            return 6379;
          case 'REDIS_USERNAME':
            return undefined;
          case 'REDIS_PASSWORD':
            return undefined;
          default:
            return undefined;
        }
      });
    });

    it('should create a client proxy', () => {
      expect(() => {
        client = module.get(DatastoreClientProvider.provide);
      }).not.toThrow();
    });

    it('should throw error with invalid configuration', async () => {
      mockEnvService.get.mockImplementation(() => undefined);

      await expect(async () => {
        const testModule = await Test.createTestingModule({
          providers: [
            DatastoreClientProvider,
            {
              provide: EnvService,
              useValue: mockEnvService,
            },
            {
              provide: ErrorUtilsService,
              useValue: mockErrorUtils,
            },
          ],
        }).compile();
        testModule.get(DatastoreClientProvider.provide);
      }).rejects.toThrow();
    });
  });

  describe('DatastoreClient decorator', () => {
    it('should return the correct injection token', () => {
      const decorator = DatastoreClient();
      expect(decorator).toBeDefined();
    });
  });

  describe('checkDatastoreServiceHealth', () => {
    let mockClient: any;

    beforeEach(() => {
      mockClient = {
        send: jest.fn(),
      };
    });

    it('should return healthy status for successful ping', async () => {
      const mockResponse = { success: true };
      mockClient.send.mockReturnValue({
        toPromise: () => Promise.resolve(mockResponse),
      });

      const result = await checkDatastoreServiceHealth(mockClient);

      expect(result.isHealthy).toBe(true);
      expect(result.latency).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should return unhealthy status for failed ping', async () => {
      const mockResponse = { success: false };
      mockClient.send.mockReturnValue({
        toPromise: () => Promise.resolve(mockResponse),
      });

      const result = await checkDatastoreServiceHealth(mockClient);

      expect(result.isHealthy).toBe(false);
      expect(result.error).toBe('Invalid response from service');
    });

    it('should handle connection errors', async () => {
      const error = new Error('Connection failed');
      mockClient.send.mockReturnValue({
        toPromise: () => Promise.reject(error),
      });

      const result = await checkDatastoreServiceHealth(mockClient);

      expect(result.isHealthy).toBe(false);
      expect(result.error).toBe('Connection failed');
      expect(result.latency).toBeDefined();
    });
  });
});

describe('DatastoreClientMetricsTracker', () => {
  let tracker: any;

  beforeEach(async () => {
    const { DatastoreClientMetricsTracker } = await import('./datastore-client.provider');
    tracker = new DatastoreClientMetricsTracker();
  });

  it('should initialize with default metrics', () => {
    const metrics = tracker.getMetrics();

    expect(metrics.totalRequests).toBe(0);
    expect(metrics.successfulRequests).toBe(0);
    expect(metrics.failedRequests).toBe(0);
    expect(metrics.averageLatency).toBe(0);
    expect(metrics.connectionStatus).toBe('disconnected');
    expect(metrics.errorRate).toBe(0);
  });

  it('should record successful requests', () => {
    tracker.recordSuccess(100);
    tracker.recordSuccess(200);

    const metrics = tracker.getMetrics();

    expect(metrics.totalRequests).toBe(2);
    expect(metrics.successfulRequests).toBe(2);
    expect(metrics.failedRequests).toBe(0);
    expect(metrics.averageLatency).toBe(150);
    expect(metrics.connectionStatus).toBe('connected');
    expect(metrics.errorRate).toBe(0);
  });

  it('should record failed requests', () => {
    tracker.recordFailure(50);
    tracker.recordSuccess(100);

    const metrics = tracker.getMetrics();

    expect(metrics.totalRequests).toBe(2);
    expect(metrics.successfulRequests).toBe(1);
    expect(metrics.failedRequests).toBe(1);
    expect(metrics.averageLatency).toBe(75);
    expect(metrics.connectionStatus).toBe('connected');
    expect(metrics.errorRate).toBe(50);
  });

  it('should reset metrics', () => {
    tracker.recordSuccess(100);
    tracker.reset();

    const metrics = tracker.getMetrics();

    expect(metrics.totalRequests).toBe(0);
    expect(metrics.successfulRequests).toBe(0);
    expect(metrics.failedRequests).toBe(0);
    expect(metrics.averageLatency).toBe(0);
    expect(metrics.connectionStatus).toBe('disconnected');
    expect(metrics.errorRate).toBe(0);
  });

  it('should limit latency history', () => {
    // Record more than maxLatencyHistory (100) entries
    for (let i = 0; i < 150; i++) {
      tracker.recordSuccess(i);
    }

    const metrics = tracker.getMetrics();

    expect(metrics.totalRequests).toBe(150);
    expect(metrics.successfulRequests).toBe(150);
    // Average should be based on last 100 entries (50-149)
    expect(metrics.averageLatency).toBe(99.5);
  });
});
