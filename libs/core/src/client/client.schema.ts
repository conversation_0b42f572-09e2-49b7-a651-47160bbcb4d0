import { z } from 'zod/v4';
import { utcDateTimeSchema } from '@app/common/schema';

// Client connection status enum
export const ClientConnectionStatusEnum = z.enum(['CONNECTED', 'CONNECTING', 'DISCONNECTED', 'ERROR', 'RECONNECTING']);

export type ClientConnectionStatus = z.output<typeof ClientConnectionStatusEnum>;

// Microservice client types
export const MicroserviceClientTypeEnum = z.enum(['ANALYSER', 'API', 'OMS', 'SIMULATOR', 'TICKER']);

export type MicroserviceClientType = z.output<typeof MicroserviceClientTypeEnum>;

// Redis transport configuration schema
export const RedisTransportConfigSchema = z.object({
  host: z.string().min(1),
  port: z.number().min(1).max(65535),
  username: z.string().optional(),
  password: z.string().optional(),
  db: z.number().min(0).max(15).default(0),
  retryDelayOnFailover: z.number().min(100).default(100),
  enableReadyCheck: z.boolean().default(true),
  maxRetriesPerRequest: z.number().min(1).default(3),
  lazyConnect: z.boolean().default(true),
  keepAlive: z.number().min(1000).default(30000),
  family: z.number().min(4).max(6).default(4),
  keyPrefix: z.string().optional(),
});

export type RedisTransportConfig = z.output<typeof RedisTransportConfigSchema>;

// Client configuration schema
export const ClientConfigSchema = z.object({
  name: z.string().min(1),
  type: MicroserviceClientTypeEnum,
  transport: z.object({
    type: z.enum(['REDIS', 'TCP', 'HTTP']).default('REDIS'),
    options: RedisTransportConfigSchema,
  }),
  timeout: z.number().min(1000).max(30000).default(5000),
  retryAttempts: z.number().min(0).max(10).default(3),
  retryDelay: z.number().min(100).max(5000).default(1000),
  maxConcurrentRequests: z.number().min(1).max(1000).default(100),
});

export type ClientConfig = z.output<typeof ClientConfigSchema>;

// Client connection info schema
export const ClientConnectionInfoSchema = z.object({
  clientId: z.string().min(1),
  name: z.string().min(1),
  type: MicroserviceClientTypeEnum,
  status: ClientConnectionStatusEnum,
  connectedAt: utcDateTimeSchema.optional(),
  disconnectedAt: utcDateTimeSchema.optional(),
  lastPingAt: utcDateTimeSchema.optional(),
  lastErrorAt: utcDateTimeSchema.optional(),
  errorCount: z.number().min(0).default(0),
  requestCount: z.number().min(0).default(0),
  responseCount: z.number().min(0).default(0),
  averageResponseTime: z.number().min(0).default(0),
  metadata: z.record(z.string(), z.unknown()).optional(),
});

export type ClientConnectionInfo = z.output<typeof ClientConnectionInfoSchema>;

// Client health check schema
export const ClientHealthCheckSchema = z.object({
  clientId: z.string().min(1),
  name: z.string().min(1),
  type: MicroserviceClientTypeEnum,
  isHealthy: z.boolean(),
  status: ClientConnectionStatusEnum,
  responseTime: z.number().min(0),
  checkedAt: utcDateTimeSchema,
  details: z.object({
    transport: z.object({
      connected: z.boolean(),
      lastError: z.string().optional(),
    }),
    requests: z.object({
      total: z.number().min(0),
      successful: z.number().min(0),
      failed: z.number().min(0),
      averageResponseTime: z.number().min(0),
    }),
  }),
});

export type ClientHealthCheck = z.output<typeof ClientHealthCheckSchema>;

// Client request schema
export const ClientRequestSchema = z.object({
  pattern: z.string().min(1),
  data: z.unknown(),
  timeout: z.number().min(1000).max(30000).default(5000),
  retryAttempts: z.number().min(0).max(10).default(3),
  metadata: z.record(z.string(), z.unknown()).optional(),
});

export type ClientRequest = z.output<typeof ClientRequestSchema>;

// Client response schema
export const ClientResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z
    .object({
      code: z.string(),
      message: z.string(),
      details: z.unknown().optional(),
    })
    .optional(),
  metadata: z.object({
    requestId: z.string(),
    timestamp: utcDateTimeSchema,
    processingTime: z.number().min(0),
    clientType: MicroserviceClientTypeEnum,
  }),
});

export type ClientResponse = z.output<typeof ClientResponseSchema>;

// Client registry schema
export const ClientRegistrySchema = z.object({
  clients: z.array(ClientConnectionInfoSchema),
  totalClients: z.number().min(0),
  connectedClients: z.number().min(0),
  updatedAt: utcDateTimeSchema,
});

export type ClientRegistry = z.output<typeof ClientRegistrySchema>;

// Client error schema
export const ClientErrorSchema = z.object({
  clientId: z.string().min(1),
  clientType: MicroserviceClientTypeEnum,
  errorType: z.enum([
    'CONNECTION_ERROR',
    'TIMEOUT_ERROR',
    'VALIDATION_ERROR',
    'TRANSPORT_ERROR',
    'SERVICE_UNAVAILABLE',
    'AUTHENTICATION_ERROR',
  ]),
  message: z.string().min(1),
  details: z.unknown().optional(),
  occurredAt: utcDateTimeSchema,
  requestPattern: z.string().optional(),
  requestData: z.unknown().optional(),
});

export type ClientError = z.output<typeof ClientErrorSchema>;
