import type { FactoryProvider } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxyFactory } from '@nestjs/microservices';
import { EnvService } from '../env/env.service';
import { ErrorUtilsService } from '@app/utils';
import { getRedisTransportOption } from '../transport';
import { RedisTransportConfigSchema } from './client.schema';
import { constant } from '@app/common';

/**
 * Datastore Service Client Provider
 *
 * Creates a Redis transport client for communicating with the datastore
 * microservice. Provides proper configuration validation and error handling.
 *
 * Features:
 * - Redis transport configuration
 * - Environment-based configuration
 * - Configuration validation with Zod
 * - Error handling for connection issues
 * - Dependency injection support
 */
export const DatastoreClientProvider: FactoryProvider = {
  provide: constant.DATASTORE_CLIENT,
  useFactory: (envService: EnvService, errorUtils: ErrorUtilsService) => {
    try {
      // Get the Redis transport options
      const transportOptions = getRedisTransportOption(envService);

      // Validate the transport configuration with Zod
      RedisTransportConfigSchema.parse(transportOptions.options);

      // Create and return the client proxy
      return ClientProxyFactory.create(transportOptions);
    } catch (error) {
      throw new Error(
        `Invalid Redis transport configuration for Datastore service client: ${errorUtils.getErrorMessage(error)}`,
      );
    }
  },
  inject: [EnvService, ErrorUtilsService],
};

/**
 * Datastore Service Client Injection Decorator
 *
 * Convenience decorator for injecting the Datastore service client
 *
 * @example
 * ```typescript
 * constructor(
 *   @DatastoreClient() private readonly datastoreClient: ClientProxy
 * ) {}
 * ```
 */
export function DatastoreClient() {
  return Inject(constant.DATASTORE_CLIENT);
}

/**
 * Datastore Service Client Configuration
 *
 * Configuration object for the Datastore service client with default values
 * and environment-specific overrides.
 */
export const DatastoreClientConfig = {
  // Connection settings
  connectionTimeout: 30000, // 30 seconds
  requestTimeout: 60000, // 60 seconds for download operations
  retryAttempts: 3,
  retryDelay: 1000, // 1 second

  // Health check settings
  healthCheckInterval: 30000, // 30 seconds
  healthCheckTimeout: 10000, // 10 seconds

  // Queue settings
  maxConcurrentRequests: 10,
  queueTimeout: 300000, // 5 minutes

  // Service discovery
  serviceName: 'datastore',
  serviceVersion: '1.0.0',
} as const;

/**
 * Datastore Service Client Health Check
 *
 * Utility function to check if the Datastore service client is healthy
 * and can communicate with the datastore service.
 */
export async function checkDatastoreServiceHealth(client: any): Promise<{
  isHealthy: boolean;
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();

  try {
    // Send a simple ping to check connectivity
    const response = await client.send('datastore.ping', {}).toPromise();
    const latency = Date.now() - startTime;

    if (response && response.success) {
      return {
        isHealthy: true,
        latency,
      };
    } else {
      return {
        isHealthy: false,
        error: 'Invalid response from service',
      };
    }
  } catch (error) {
    return {
      isHealthy: false,
      latency: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Datastore Service Client Metrics
 *
 * Interface for tracking Datastore service client metrics
 */
export interface DatastoreClientMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  lastRequestTime: Date | null;
  connectionStatus: 'connected' | 'disconnected' | 'error';
  errorRate: number;
}

/**
 * Datastore Service Client Metrics Tracker
 *
 * Simple metrics tracking for the Datastore service client
 */
export class DatastoreClientMetricsTracker {
  private metrics: DatastoreClientMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    lastRequestTime: null,
    connectionStatus: 'disconnected',
    errorRate: 0,
  };

  private latencies: number[] = [];
  private readonly maxLatencyHistory = 100;

  /**
   * Record a successful request
   */
  recordSuccess(latency: number): void {
    this.metrics.totalRequests++;
    this.metrics.successfulRequests++;
    this.metrics.lastRequestTime = new Date();
    this.metrics.connectionStatus = 'connected';

    this.updateLatency(latency);
    this.updateErrorRate();
  }

  /**
   * Record a failed request
   */
  recordFailure(latency?: number): void {
    this.metrics.totalRequests++;
    this.metrics.failedRequests++;
    this.metrics.lastRequestTime = new Date();
    this.metrics.connectionStatus = 'error';

    if (latency !== undefined) {
      this.updateLatency(latency);
    }

    this.updateErrorRate();
  }

  /**
   * Get current metrics
   */
  getMetrics(): DatastoreClientMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  reset(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      lastRequestTime: null,
      connectionStatus: 'disconnected',
      errorRate: 0,
    };
    this.latencies = [];
  }

  /**
   * Update average latency
   */
  private updateLatency(latency: number): void {
    this.latencies.push(latency);

    // Keep only the last N latencies
    if (this.latencies.length > this.maxLatencyHistory) {
      this.latencies.shift();
    }

    // Calculate average
    this.metrics.averageLatency = this.latencies.reduce((sum, lat) => sum + lat, 0) / this.latencies.length;
  }

  /**
   * Update error rate
   */
  private updateErrorRate(): void {
    if (this.metrics.totalRequests > 0) {
      this.metrics.errorRate = (this.metrics.failedRequests / this.metrics.totalRequests) * 100;
    }
  }
}

/**
 * Datastore Service Client Factory
 *
 * Factory function for creating Datastore service clients with custom configuration
 */
export function createDatastoreServiceClient(envService: EnvService, config?: Partial<typeof DatastoreClientConfig>) {
  const finalConfig = { ...DatastoreClientConfig, ...config };
  const transportOptions = getRedisTransportOption(envService);

  // Apply custom configuration
  if (finalConfig.connectionTimeout && transportOptions.options) {
    (transportOptions.options as any).connectTimeout = finalConfig.connectionTimeout;
  }

  return ClientProxyFactory.create(transportOptions);
}

/**
 * Datastore Service Client Module Configuration
 *
 * Configuration for importing the Datastore service client in modules
 */
export const DatastoreClientModule = {
  providers: [DatastoreClientProvider],
  exports: [DatastoreClientProvider],
} as const;
