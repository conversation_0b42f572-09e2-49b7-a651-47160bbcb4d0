import { Test, TestingModule } from '@nestjs/testing';
import { ConfigRepository } from './config.repository';
import { DrizzleService } from '../drizzle/drizzle.service';
import { UtilsService, DbUtilsService } from '@app/utils';
import { Config, CreateConfig, UpdateConfig, ConfigNotFoundError, DuplicateConfigKeyError } from './config.schema';

describe('ConfigRepository', () => {
  let repository: ConfigRepository;
  let drizzleService: jest.Mocked<DrizzleService>;
  let utilsService: jest.Mocked<UtilsService>;
  let dbUtils: jest.Mocked<DbUtilsService>;

  const mockConfig: Config = {
    id: 1,
    key: 'API_BASE_URL',
    value: 'http://localhost:3000',
    description: 'Base URL for API endpoints',
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'system',
    updatedAt: '2024-01-01T00:00:00Z',
    updatedBy: 'system',
  };

  const mockDb = {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    returning: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  };

  beforeEach(async () => {
    const mockDrizzleService = {
      getDb: jest.fn().mockReturnValue(mockDb),
    };

    const mockUtilsService = {
      // Add any required methods
    };

    const mockDbUtils = {
      // Add any required methods
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: UtilsService,
          useValue: mockUtilsService,
        },
        {
          provide: DbUtilsService,
          useValue: mockDbUtils,
        },
      ],
    }).compile();

    repository = module.get<ConfigRepository>(ConfigRepository);
    drizzleService = module.get(DrizzleService);
    utilsService = module.get(UtilsService);
    dbUtils = module.get(DbUtilsService);

    // Mock the base repository methods
    jest.spyOn(repository as any, 'addCreateAuditFields').mockImplementation((data) => ({
      ...data,
      createdAt: '2024-01-01T00:00:00Z',
      createdBy: 'system',
      updatedAt: '2024-01-01T00:00:00Z',
      updatedBy: 'system',
    }));

    jest.spyOn(repository as any, 'addUpdateAuditFields').mockImplementation((data) => ({
      ...data,
      updatedAt: '2024-01-01T00:00:00Z',
      updatedBy: 'system',
    }));

    jest.spyOn(repository as any, 'executeWithErrorHandling').mockImplementation(async (operation, fn) => {
      return await fn();
    });

    jest.spyOn(repository as any, 'logOperation').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findByKey', () => {
    it('should find configuration by key and environment', async () => {
      mockDb.select.mockResolvedValue([mockConfig]);

      const result = await repository.findByKey('API_BASE_URL', 'local');

      expect(result).toEqual(mockConfig);
      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.from).toHaveBeenCalled();
      expect(mockDb.where).toHaveBeenCalled();
      expect(mockDb.limit).toHaveBeenCalledWith(1);
    });

    it('should return null if configuration not found', async () => {
      mockDb.select.mockResolvedValue([]);

      const result = await repository.findByKey('NONEXISTENT_KEY', 'local');

      expect(result).toBeNull();
    });

    it('should include inactive configurations when requested', async () => {
      const inactiveConfig = { ...mockConfig, isActive: false };
      mockDb.select.mockResolvedValue([inactiveConfig]);

      const result = await repository.findByKey('API_BASE_URL', 'local', true);

      expect(result).toEqual(inactiveConfig);
    });
  });

  describe('findByKeys', () => {
    it('should find multiple configurations by keys', async () => {
      const configs = [mockConfig, { ...mockConfig, id: 2, key: 'TIMEOUT', value: '5000' }];
      mockDb.select.mockResolvedValue(configs);

      const result = await repository.findByKeys(['API_BASE_URL', 'TIMEOUT'], 'local');

      expect(result).toEqual(configs);
      expect(result).toHaveLength(2);
    });

    it('should return empty array for empty keys array', async () => {
      const result = await repository.findByKeys([], 'local');

      expect(result).toEqual([]);
      expect(mockDb.select).not.toHaveBeenCalled();
    });
  });

  describe('findByCategory', () => {
    it('should find configurations by category', async () => {
      const serviceConfigs = [mockConfig, { ...mockConfig, id: 2, key: 'ANOTHER_SERVICE_URL' }];
      mockDb.select.mockResolvedValue(serviceConfigs);

      const result = await repository.findByCategory('service', 'local');

      expect(result).toEqual(serviceConfigs);
    });
  });

  describe('findByEnvironment', () => {
    it('should find all configurations for environment', async () => {
      const allConfigs = [mockConfig, { ...mockConfig, id: 2, key: 'TIMEOUT', category: 'broker' }];
      mockDb.select.mockResolvedValue(allConfigs);

      const result = await repository.findByEnvironment('local');

      expect(result).toEqual(allConfigs);
    });
  });

  describe('create', () => {
    it('should create new configuration', async () => {
      const createData: CreateConfig = {
        key: 'NEW_CONFIG',
        value: 'new-value',
        description: 'New configuration',
        category: 'application',
        environment: 'local',
        dataType: 'string',
        isActive: true,
      };

      // Mock findByKey to return null (no existing config)
      jest.spyOn(repository, 'findByKey').mockResolvedValue(null);

      // Mock the insert operation
      mockDb.insert.mockResolvedValue([{ ...mockConfig, ...createData, id: 2 }]);

      const result = await repository.create(createData);

      expect(result.key).toBe('NEW_CONFIG');
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalled();
      expect(mockDb.returning).toHaveBeenCalled();
    });

    it('should throw DuplicateConfigKeyError if key already exists', async () => {
      const createData: CreateConfig = {
        key: 'API_BASE_URL',
        value: 'new-value',
        description: 'Duplicate configuration',
        category: 'application',
        environment: 'local',
        dataType: 'string',
        isActive: true,
      };

      // Mock findByKey to return existing config
      jest.spyOn(repository, 'findByKey').mockResolvedValue(mockConfig);

      await expect(repository.create(createData)).rejects.toThrow(DuplicateConfigKeyError);
    });
  });

  describe('updateByKey', () => {
    it('should update configuration by key', async () => {
      const updateData: UpdateConfig = {
        value: 'updated-value',
        description: 'Updated description',
      };
      const updatedConfig = { ...mockConfig, ...updateData };

      // Mock findByKey to return existing config
      jest.spyOn(repository, 'findByKey').mockResolvedValue(mockConfig);

      // Mock the update operation
      jest.spyOn(repository, 'update').mockResolvedValue(updatedConfig);

      const result = await repository.updateByKey('API_BASE_URL', 'local', updateData);

      expect(result.value).toBe('updated-value');
      expect(repository.update).toHaveBeenCalledWith(mockConfig.id, updateData);
    });

    it('should throw ConfigNotFoundError if configuration not found', async () => {
      const updateData: UpdateConfig = {
        value: 'updated-value',
      };

      // Mock findByKey to return null
      jest.spyOn(repository, 'findByKey').mockResolvedValue(null);

      await expect(repository.updateByKey('NONEXISTENT_KEY', 'local', updateData)).rejects.toThrow(ConfigNotFoundError);
    });
  });

  describe('activate/deactivate', () => {
    it('should activate configuration', async () => {
      const activatedConfig = { ...mockConfig, isActive: true };
      jest.spyOn(repository, 'updateByKey').mockResolvedValue(activatedConfig);

      const result = await repository.activate('API_BASE_URL', 'local');

      expect(result.isActive).toBe(true);
      expect(repository.updateByKey).toHaveBeenCalledWith('API_BASE_URL', 'local', { isActive: true });
    });

    it('should deactivate configuration', async () => {
      const deactivatedConfig = { ...mockConfig, isActive: false };
      jest.spyOn(repository, 'updateByKey').mockResolvedValue(deactivatedConfig);

      const result = await repository.deactivate('API_BASE_URL', 'local');

      expect(result.isActive).toBe(false);
      expect(repository.updateByKey).toHaveBeenCalledWith('API_BASE_URL', 'local', { isActive: false });
    });
  });

  describe('bulkCreate', () => {
    it('should create multiple configurations', async () => {
      const configs: CreateConfig[] = [
        {
          key: 'CONFIG_1',
          value: 'value1',
          description: 'First config',
          category: 'application',
          environment: 'local',
          dataType: 'string',
          isActive: true,
        },
        {
          key: 'CONFIG_2',
          value: 'value2',
          description: 'Second config',
          category: 'application',
          environment: 'local',
          dataType: 'string',
          isActive: true,
        },
      ];

      const createdConfigs = configs.map((config, index) => ({
        ...mockConfig,
        ...config,
        id: index + 1,
      }));

      mockDb.insert.mockResolvedValue(createdConfigs);

      const result = await repository.bulkCreate(configs);

      expect(result).toHaveLength(2);
      expect(result[0].key).toBe('CONFIG_1');
      expect(result[1].key).toBe('CONFIG_2');
    });

    it('should return empty array for empty input', async () => {
      const result = await repository.bulkCreate([]);

      expect(result).toEqual([]);
      expect(mockDb.insert).not.toHaveBeenCalled();
    });
  });

  describe('deleteByKey', () => {
    it('should delete configuration by key', async () => {
      // Mock findByKey to return existing config
      jest.spyOn(repository, 'findByKey').mockResolvedValue(mockConfig);

      // Mock the delete operation
      jest.spyOn(repository, 'delete').mockResolvedValue(undefined);

      await repository.deleteByKey('API_BASE_URL', 'local');

      expect(repository.delete).toHaveBeenCalledWith(mockConfig.id);
    });

    it('should throw ConfigNotFoundError if configuration not found', async () => {
      // Mock findByKey to return null
      jest.spyOn(repository, 'findByKey').mockResolvedValue(null);

      await expect(repository.deleteByKey('NONEXISTENT_KEY', 'local')).rejects.toThrow(ConfigNotFoundError);
    });
  });
});
