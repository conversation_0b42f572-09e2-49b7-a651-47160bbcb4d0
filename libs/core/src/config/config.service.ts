import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigRepository } from './config.repository';
import { CacheService } from '../cache/cache.service';
import { EnvService } from '../env/env.service';
import {
  Config,
  CreateConfig,
  UpdateConfig,
  ConfigQuery,
  AppConfig,
  appConfigSchema,
  ConfigDataType,
} from './config.schema';
import {
  ConfigError,
  ConfigNotFoundError,
  InvalidConfigValueError,
  ConfigInactiveError,
  createConfigCacheError,
  createConfigValidationError,
} from './config.error';
import type { Environment } from '@app/common/constants';

/**
 * Configuration service providing type-safe access to application configuration
 * Features caching, environment-specific loading, and type conversion
 */
@Injectable()
export class ConfigService implements OnModuleInit {
  private readonly logger = new Logger(ConfigService.name);
  private currentEnvironment: Environment;
  private configCache = new Map<string, unknown>();
  private readonly CACHE_TTL = 300; // 5 minutes
  private readonly CACHE_PREFIX = 'config:';

  constructor(
    private readonly configRepository: ConfigRepository,
    private readonly cacheService: CacheService,
    private readonly envService: EnvService,
  ) {
    this.currentEnvironment = this.envService.get('NODE_ENV');
  }

  async onModuleInit() {
    this.logger.log('Initializing configuration service...');

    try {
      // Pre-load and cache all configurations for current environment
      await this.preloadConfigurations();
      this.logger.log('Configuration service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize configuration service', error);
      throw new ConfigError('VALIDATION_FAILED', {
        message: 'Configuration service initialization failed',
        cause: error,
      });
    }
  }

  // ==================== CONFIGURATION ACCESS METHODS ====================

  /**
   * Get configuration value with type safety and caching
   * @param key - Configuration key
   * @param environment - Target environment (defaults to current)
   * @returns Promise<T> - Configuration value
   */
  async get<T = string>(key: string, environment?: Environment): Promise<T> {
    const env = environment || this.currentEnvironment;
    const cacheKey = `${env}:${key}`;

    try {
      // Try cache first
      const cached = await this.getCachedValue<T>(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // Fetch from database
      const config = await this.configRepository.findByKey(key, env);
      if (!config) {
        throw new ConfigNotFoundError(key, env);
      }

      if (!config.isActive) {
        throw new ConfigInactiveError(key);
      }

      // Parse and cache the value
      const parsedValue = this.parseConfigValue<T>(config.value, config.dataType);
      await this.setCachedValue(cacheKey, parsedValue);

      return parsedValue;
    } catch (error) {
      this.logger.error(`Failed to get configuration '${key}' for environment '${env}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Get multiple configuration values
   * @param keys - Array of configuration keys
   * @param environment - Target environment (defaults to current)
   * @returns Promise<Record<string, unknown>> - Configuration values
   */
  async getMany(keys: string[], environment?: Environment): Promise<Record<string, unknown>> {
    const env = environment || this.currentEnvironment;

    try {
      const configs = await this.configRepository.findByKeys(keys, env);
      const result: Record<string, unknown> = {};

      for (const config of configs) {
        if (config.isActive) {
          const parsedValue = this.parseConfigValue(config.value, config.dataType);
          result[config.key] = parsedValue;

          // Cache individual values
          const cacheKey = `${env}:${config.key}`;
          await this.setCachedValue(cacheKey, parsedValue);
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to get configurations for environment '${env}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Get all application configuration as typed object
   * @param environment - Target environment (defaults to current)
   * @returns Promise<AppConfig> - Complete application configuration
   */
  async getAppConfig(environment?: Environment): Promise<AppConfig> {
    const env = environment || this.currentEnvironment;
    const cacheKey = `${env}:app_config`;

    try {
      // Try cache first
      const cached = await this.getCachedValue<AppConfig>(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // Fetch all configurations for environment
      const configs = await this.configRepository.findByEnvironment(env);
      const configObject: Record<string, unknown> = {};

      for (const config of configs) {
        if (config.isActive) {
          configObject[config.key] = this.parseConfigValue(config.value, config.dataType);
        }
      }

      // Validate against schema
      const appConfig = appConfigSchema.parse(configObject);

      // Cache the result
      await this.setCachedValue(cacheKey, appConfig);

      return appConfig;
    } catch (error) {
      this.logger.error(`Failed to get app configuration for environment '${env}'`, error);
      throw error instanceof ConfigError ? error : createConfigValidationError('app_config', error);
    }
  }

  /**
   * Get configuration by category
   * @param category - Configuration category
   * @param environment - Target environment (defaults to current)
   * @returns Promise<Record<string, unknown>> - Configuration values by category
   */
  async getByCategory(category: string, environment?: Environment): Promise<Record<string, unknown>> {
    const env = environment || this.currentEnvironment;

    try {
      const configs = await this.configRepository.findByCategory(category, env);
      const result: Record<string, unknown> = {};

      for (const config of configs) {
        if (config.isActive) {
          result[config.key] = this.parseConfigValue(config.value, config.dataType);
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to get configurations for category '${category}' and environment '${env}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  // ==================== CONFIGURATION MANAGEMENT METHODS ====================

  /**
   * Create new configuration
   * @param data - Configuration creation data
   * @returns Promise<Config> - Created configuration
   */
  async create(data: CreateConfig): Promise<Config> {
    try {
      const config = await this.configRepository.create(data);

      // Invalidate cache
      await this.invalidateCache(data.environment);

      this.logger.log(`Created configuration '${data.key}' for environment '${data.environment}'`);
      return config;
    } catch (error) {
      this.logger.error(`Failed to create configuration '${data.key}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Update configuration
   * @param key - Configuration key
   * @param updates - Update data
   * @param environment - Target environment (defaults to current)
   * @returns Promise<Config> - Updated configuration
   */
  async update(key: string, updates: UpdateConfig, environment?: Environment): Promise<Config> {
    const env = environment || this.currentEnvironment;

    try {
      const config = await this.configRepository.updateByKey(key, env, updates);

      // Invalidate cache
      await this.invalidateCache(env);

      this.logger.log(`Updated configuration '${key}' for environment '${env}'`);
      return config;
    } catch (error) {
      this.logger.error(`Failed to update configuration '${key}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Delete configuration
   * @param key - Configuration key
   * @param environment - Target environment (defaults to current)
   * @returns Promise<void>
   */
  async delete(key: string, environment?: Environment): Promise<void> {
    const env = environment || this.currentEnvironment;

    try {
      await this.configRepository.deleteByKey(key, env);

      // Invalidate cache
      await this.invalidateCache(env);

      this.logger.log(`Deleted configuration '${key}' for environment '${env}'`);
    } catch (error) {
      this.logger.error(`Failed to delete configuration '${key}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Query configurations
   * @param query - Query parameters
   * @returns Promise<Config[]> - Matching configurations
   */
  async query(query: ConfigQuery): Promise<Config[]> {
    try {
      return await this.configRepository.query(query);
    } catch (error) {
      this.logger.error('Failed to query configurations', error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Pre-load configurations for current environment
   */
  private async preloadConfigurations(): Promise<void> {
    try {
      const configs = await this.configRepository.findByEnvironment(this.currentEnvironment);

      for (const config of configs) {
        if (config.isActive) {
          const cacheKey = `${this.currentEnvironment}:${config.key}`;
          const parsedValue = this.parseConfigValue(config.value, config.dataType);
          await this.setCachedValue(cacheKey, parsedValue);
        }
      }

      this.logger.log(`Pre-loaded ${configs.length} configurations for environment '${this.currentEnvironment}'`);
    } catch (error) {
      throw createConfigCacheError('preload', error);
    }
  }

  /**
   * Parse configuration value based on data type
   */
  private parseConfigValue<T>(value: string, dataType: ConfigDataType): T {
    try {
      switch (dataType) {
        case 'string':
          return value as T;
        case 'number': {
          const num = Number(value);
          if (isNaN(num)) {
            throw new InvalidConfigValueError('unknown', value, 'number');
          }
          return num as T;
        }
        case 'boolean':
          return (value.toLowerCase() === 'true') as T;
        case 'url':
          // Basic URL validation
          new URL(value);
          return value as T;
        case 'json':
          return JSON.parse(value) as T;
        default:
          return value as T;
      }
    } catch {
      throw new InvalidConfigValueError('unknown', value, dataType);
    }
  }

  /**
   * Get cached value
   */
  private async getCachedValue<T>(key: string): Promise<T | null> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}${key}`;
      const cached = await this.cacheService.get(cacheKey);
      return (cached as T) || null;
    } catch (error) {
      this.logger.warn(`Cache get failed for key '${key}'`, error);
      return null;
    }
  }

  /**
   * Set cached value
   */
  private async setCachedValue(key: string, value: unknown): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}${key}`;
      await this.cacheService.set(cacheKey, value, this.CACHE_TTL);
    } catch (error) {
      this.logger.warn(`Cache set failed for key '${key}'`, error);
    }
  }

  /**
   * Activate configuration
   * @param key - Configuration key
   * @param environment - Target environment (defaults to current)
   * @returns Promise<Config> - Activated configuration
   */
  async activate(key: string, environment?: Environment): Promise<Config> {
    const env = environment || this.currentEnvironment;

    try {
      const config = await this.configRepository.activate(key, env);

      // Invalidate cache
      await this.invalidateCache(env);

      this.logger.log(`Activated configuration '${key}' for environment '${env}'`);
      return config;
    } catch (error) {
      this.logger.error(`Failed to activate configuration '${key}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Deactivate configuration
   * @param key - Configuration key
   * @param environment - Target environment (defaults to current)
   * @returns Promise<Config> - Deactivated configuration
   */
  async deactivate(key: string, environment?: Environment): Promise<Config> {
    const env = environment || this.currentEnvironment;

    try {
      const config = await this.configRepository.deactivate(key, env);

      // Invalidate cache
      await this.invalidateCache(env);

      this.logger.log(`Deactivated configuration '${key}' for environment '${env}'`);
      return config;
    } catch (error) {
      this.logger.error(`Failed to deactivate configuration '${key}'`, error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Bulk create configurations
   * @param configs - Array of configuration creation data
   * @returns Promise<Config[]> - Created configurations
   */
  async bulkCreate(configs: CreateConfig[]): Promise<Config[]> {
    try {
      const createdConfigs = await this.configRepository.bulkCreate(configs);

      // Invalidate cache for all affected environments
      const environments = [...new Set(configs.map((c) => c.environment))];
      for (const env of environments) {
        await this.invalidateCache(env);
      }

      this.logger.log(`Bulk created ${createdConfigs.length} configurations`);
      return createdConfigs;
    } catch (error) {
      this.logger.error('Failed to bulk create configurations', error);
      throw error instanceof ConfigError ? error : new ConfigError('DATABASE_ERROR', { cause: error });
    }
  }

  /**
   * Refresh cache for current environment
   * @returns Promise<void>
   */
  async refreshCache(): Promise<void> {
    try {
      await this.invalidateCache(this.currentEnvironment);
      await this.preloadConfigurations();
      this.logger.log(`Refreshed cache for environment '${this.currentEnvironment}'`);
    } catch (error) {
      this.logger.error('Failed to refresh cache', error);
      throw createConfigCacheError('refresh', error);
    }
  }

  /**
   * Get current environment
   * @returns Environment - Current environment
   */
  getCurrentEnvironment(): Environment {
    return this.currentEnvironment;
  }

  /**
   * Check if configuration exists
   * @param key - Configuration key
   * @param environment - Target environment (defaults to current)
   * @returns Promise<boolean> - Whether configuration exists
   */
  async exists(key: string, environment?: Environment): Promise<boolean> {
    const env = environment || this.currentEnvironment;

    try {
      const config = await this.configRepository.findByKey(key, env, true);
      return config !== null;
    } catch (error) {
      this.logger.error(`Failed to check if configuration '${key}' exists`, error);
      return false;
    }
  }

  /**
   * Invalidate cache for environment
   */
  private async invalidateCache(environment: Environment): Promise<void> {
    try {
      // Clear memory cache
      this.configCache.clear();

      // Clear Redis cache (pattern-based deletion would be ideal)
      const pattern = `${this.CACHE_PREFIX}${environment}:*`;
      await this.cacheService.del(pattern);
    } catch (error) {
      this.logger.warn(`Cache invalidation failed for environment '${environment}'`, error);
    }
  }
}
