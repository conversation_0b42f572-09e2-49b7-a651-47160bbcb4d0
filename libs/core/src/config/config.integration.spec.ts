import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from './config.module';
import { ConfigService } from './config.service';
import { ConfigRepository } from './config.repository';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { CacheModule } from '../cache/cache.module';
import { EnvModule } from '../env/env.module';
import { UtilsModule } from '@app/utils';
import { CreateConfig, Config } from './config.schema';

describe('ConfigModule Integration', () => {
  let module: TestingModule;
  let configService: ConfigService;
  let configRepository: ConfigRepository;

  beforeAll(async () => {
    // Mock environment variables for testing
    process.env.NODE_ENV = 'test';
    process.env.DB_HOST = 'localhost';
    process.env.DB_PORT = '5432';
    process.env.DB_NAME = 'test_db';
    process.env.DB_USERNAME = 'test_user';
    process.env.DB_PASSWORD = 'test_password';
    process.env.REDIS_HOST = 'localhost';
    process.env.REDIS_PORT = '6379';
    process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-purposes';
    process.env.SESSION_SECRET_KEY = 'test-session-secret-key-for-testing';
    process.env.BROKER_ENCRYPTION_KEY = 'test-broker-encryption-key-32-chars';

    module = await Test.createTestingModule({
      imports: [
        ConfigModule,
        // Note: In a real integration test, you might want to use a test database
        // For this example, we'll mock the dependencies
      ],
    })
      .overrideProvider(DrizzleModule)
      .useValue({
        // Mock DrizzleModule for testing
      })
      .overrideProvider(CacheModule)
      .useValue({
        // Mock CacheModule for testing
      })
      .compile();

    configService = module.get<ConfigService>(ConfigService);
    configRepository = module.get<ConfigRepository>(ConfigRepository);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Module Initialization', () => {
    it('should be defined', () => {
      expect(configService).toBeDefined();
      expect(configRepository).toBeDefined();
    });

    it('should have ConfigService as a provider', () => {
      expect(configService).toBeInstanceOf(ConfigService);
    });

    it('should have ConfigRepository as a provider', () => {
      expect(configRepository).toBeInstanceOf(ConfigRepository);
    });
  });

  describe('Service Integration', () => {
    const testConfig: CreateConfig = {
      key: 'TEST_INTEGRATION_CONFIG',
      value: 'integration-test-value',
      description: 'Configuration for integration testing',
      category: 'application',
      environment: 'test',
      dataType: 'string',
      isActive: true,
    };

    it('should create and retrieve configuration', async () => {
      // Mock the repository methods for this test
      const createdConfig: Config = {
        id: 1,
        ...testConfig,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 'system',
        updatedAt: '2024-01-01T00:00:00Z',
        updatedBy: 'system',
      };

      jest.spyOn(configRepository, 'create').mockResolvedValue(createdConfig);
      jest.spyOn(configRepository, 'findByKey').mockResolvedValue(createdConfig);

      // Create configuration
      const created = await configService.create(testConfig);
      expect(created.key).toBe(testConfig.key);
      expect(created.value).toBe(testConfig.value);

      // Retrieve configuration
      const retrieved = await configService.get('TEST_INTEGRATION_CONFIG', 'test');
      expect(retrieved).toBe('integration-test-value');
    });

    it('should handle configuration updates', async () => {
      const originalConfig: Config = {
        id: 1,
        ...testConfig,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 'system',
        updatedAt: '2024-01-01T00:00:00Z',
        updatedBy: 'system',
      };

      const updatedConfig: Config = {
        ...originalConfig,
        value: 'updated-integration-test-value',
        updatedAt: '2024-01-01T01:00:00Z',
      };

      jest.spyOn(configRepository, 'updateByKey').mockResolvedValue(updatedConfig);

      const result = await configService.update(
        'TEST_INTEGRATION_CONFIG',
        { value: 'updated-integration-test-value' },
        'test',
      );

      expect(result.value).toBe('updated-integration-test-value');
    });

    it('should handle bulk operations', async () => {
      const bulkConfigs: CreateConfig[] = [
        {
          key: 'BULK_CONFIG_1',
          value: 'bulk-value-1',
          description: 'First bulk configuration',
          category: 'application',
          environment: 'test',
          dataType: 'string',
          isActive: true,
        },
        {
          key: 'BULK_CONFIG_2',
          value: 'bulk-value-2',
          description: 'Second bulk configuration',
          category: 'application',
          environment: 'test',
          dataType: 'string',
          isActive: true,
        },
      ];

      const createdBulkConfigs: Config[] = bulkConfigs.map((config, index) => ({
        id: index + 1,
        ...config,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 'system',
        updatedAt: '2024-01-01T00:00:00Z',
        updatedBy: 'system',
      }));

      jest.spyOn(configRepository, 'bulkCreate').mockResolvedValue(createdBulkConfigs);

      const result = await configService.bulkCreate(bulkConfigs);

      expect(result).toHaveLength(2);
      expect(result[0].key).toBe('BULK_CONFIG_1');
      expect(result[1].key).toBe('BULK_CONFIG_2');
    });

    it('should handle configuration activation/deactivation', async () => {
      const activeConfig: Config = {
        id: 1,
        ...testConfig,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 'system',
        updatedAt: '2024-01-01T00:00:00Z',
        updatedBy: 'system',
      };

      const inactiveConfig: Config = {
        ...activeConfig,
        isActive: false,
      };

      jest.spyOn(configRepository, 'deactivate').mockResolvedValue(inactiveConfig);
      jest.spyOn(configRepository, 'activate').mockResolvedValue(activeConfig);

      // Deactivate
      const deactivated = await configService.deactivate('TEST_INTEGRATION_CONFIG', 'test');
      expect(deactivated.isActive).toBe(false);

      // Activate
      const activated = await configService.activate('TEST_INTEGRATION_CONFIG', 'test');
      expect(activated.isActive).toBe(true);
    });

    it('should handle different data types correctly', async () => {
      const configs: Config[] = [
        {
          id: 1,
          key: 'STRING_CONFIG',
          value: 'string-value',
          description: 'String configuration',
          category: 'application',
          environment: 'test',
          dataType: 'string',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          createdBy: 'system',
          updatedAt: '2024-01-01T00:00:00Z',
          updatedBy: 'system',
        },
        {
          id: 2,
          key: 'NUMBER_CONFIG',
          value: '42',
          description: 'Number configuration',
          category: 'application',
          environment: 'test',
          dataType: 'number',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          createdBy: 'system',
          updatedAt: '2024-01-01T00:00:00Z',
          updatedBy: 'system',
        },
        {
          id: 3,
          key: 'BOOLEAN_CONFIG',
          value: 'true',
          description: 'Boolean configuration',
          category: 'application',
          environment: 'test',
          dataType: 'boolean',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          createdBy: 'system',
          updatedAt: '2024-01-01T00:00:00Z',
          updatedBy: 'system',
        },
      ];

      jest.spyOn(configRepository, 'findByKey').mockImplementation(async (key: string) => {
        return configs.find((c) => c.key === key) || null;
      });

      const stringValue = await configService.get<string>('STRING_CONFIG', 'test');
      expect(stringValue).toBe('string-value');
      expect(typeof stringValue).toBe('string');

      const numberValue = await configService.get<number>('NUMBER_CONFIG', 'test');
      expect(numberValue).toBe(42);
      expect(typeof numberValue).toBe('number');

      const booleanValue = await configService.get<boolean>('BOOLEAN_CONFIG', 'test');
      expect(booleanValue).toBe(true);
      expect(typeof booleanValue).toBe('boolean');
    });
  });

  describe('Error Handling', () => {
    it('should handle configuration not found gracefully', async () => {
      jest.spyOn(configRepository, 'findByKey').mockResolvedValue(null);

      await expect(configService.get('NONEXISTENT_CONFIG', 'test')).rejects.toThrow(
        "Configuration 'NONEXISTENT_CONFIG' not found for environment 'test'",
      );
    });

    it('should handle inactive configurations', async () => {
      const inactiveConfig: Config = {
        id: 1,
        key: 'INACTIVE_CONFIG',
        value: 'inactive-value',
        description: 'Inactive configuration',
        category: 'application',
        environment: 'test',
        dataType: 'string',
        isActive: false,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 'system',
        updatedAt: '2024-01-01T00:00:00Z',
        updatedBy: 'system',
      };

      jest.spyOn(configRepository, 'findByKey').mockResolvedValue(inactiveConfig);

      await expect(configService.get('INACTIVE_CONFIG', 'test')).rejects.toThrow(
        "Configuration 'INACTIVE_CONFIG' is inactive",
      );
    });
  });
});
