import { z } from 'zod/v4';
import { EnvironmentEnum } from '@app/common/constants';

// ==================== CONFIGURATION ENUMS ====================

/**
 * Configuration categories for organizing settings
 */
export const ConfigCategoryEnum = z.enum([
  'service', // Service URLs and endpoints
  'broker', // Broker-related timeouts, limits, intervals
  'security', // Rate limiting, session timeouts
  'database', // Non-sensitive DB settings like max connections
  'application', // General app settings
]);

/**
 * Data types for configuration values
 */
export const ConfigDataTypeEnum = z.enum(['string', 'number', 'boolean', 'url', 'json']);

// ==================== BASE CONFIGURATION SCHEMAS ====================

/**
 * Base configuration entry schema
 */
export const baseConfigSchema = z.object({
  id: z.number().int().positive().describe('Unique identifier'),
  key: z.string().min(1).max(255).describe('Configuration key identifier'),
  value: z.string().describe('Configuration value as string (parsed based on dataType)'),
  description: z.string().min(1).max(500).describe('Human-readable description'),
  category: ConfigCategoryEnum.describe('Configuration category'),
  environment: EnvironmentEnum.describe('Target environment'),
  isActive: z.boolean().default(true).describe('Whether configuration is active'),
  dataType: ConfigDataTypeEnum.describe('Data type for value parsing'),
  createdAt: z.string().describe('Creation timestamp'),
  createdBy: z.string().describe('Created by user'),
  updatedAt: z.string().describe('Last update timestamp'),
  updatedBy: z.string().describe('Last updated by user'),
  deletedAt: z.string().optional().describe('Deletion timestamp'),
  deletedBy: z.string().optional().describe('Deleted by user'),
});

/**
 * Schema for creating new configuration entries
 */
export const createConfigSchema = z.object({
  key: z.string().min(1).max(255),
  value: z.string(),
  description: z.string().min(1).max(500),
  category: ConfigCategoryEnum,
  environment: EnvironmentEnum,
  isActive: z.boolean().default(true),
  dataType: ConfigDataTypeEnum,
});

/**
 * Schema for updating configuration entries
 */
export const updateConfigSchema = z.object({
  value: z.string().optional(),
  description: z.string().min(1).max(500).optional(),
  category: ConfigCategoryEnum.optional(),
  environment: EnvironmentEnum.optional(),
  isActive: z.boolean().optional(),
  dataType: ConfigDataTypeEnum.optional(),
});

// ==================== CONFIGURATION VALUE SCHEMAS ====================

/**
 * Service configuration schema
 */
export const serviceConfigSchema = z.object({
  ANALYSER_SERVICE_URL: z.url().optional(),
  OMS_SERVICE_URL: z.url().optional(),
  TICKER_SERVICE_URL: z.url().optional(),
  SIMULATOR_SERVICE_URL: z.url().optional(),
  API_BASE_URL: z.url().default('http://localhost:3000'),
});

/**
 * Broker configuration schema
 */
export const brokerConfigSchema = z.object({
  BROKER_KEY_ROTATION_INTERVAL: z.coerce.number().int().positive().default(86400000), // 24 hours in ms
  BROKER_HEALTH_CHECK_INTERVAL: z.coerce.number().int().positive().default(30000), // 30 seconds
  BROKER_CONNECTION_TIMEOUT: z.coerce.number().int().positive().default(10000), // 10 seconds
  BROKER_MAX_RETRY_ATTEMPTS: z.coerce.number().int().positive().default(3),
  BROKER_SESSION_TIMEOUT: z.coerce.number().int().positive().default(3600000), // 1 hour
});

/**
 * Security configuration schema
 */
export const securityConfigSchema = z.object({
  BROKER_RATE_LIMIT_REQUESTS: z.coerce.number().int().positive().default(100),
  BROKER_RATE_LIMIT_WINDOW: z.coerce.number().int().positive().default(60000), // 1 minute
});

/**
 * Database configuration schema
 */
export const databaseConfigSchema = z.object({
  QUESTDB_MAX_CONNECTIONS: z.coerce.number().int().positive().default(10),
});

/**
 * Combined application configuration schema
 */
export const appConfigSchema = serviceConfigSchema
  .merge(brokerConfigSchema)
  .merge(securityConfigSchema)
  .merge(databaseConfigSchema);

// ==================== QUERY SCHEMAS ====================

/**
 * Schema for querying configurations
 */
export const configQuerySchema = z.object({
  category: ConfigCategoryEnum.optional(),
  environment: EnvironmentEnum.optional(),
  isActive: z.boolean().optional(),
  keys: z.array(z.string()).optional(),
});

/**
 * Schema for bulk configuration operations
 */
export const bulkConfigSchema = z.object({
  configs: z.array(createConfigSchema),
});

// ==================== EXPORTED TYPES ====================

export type Config = z.output<typeof baseConfigSchema>;
export type CreateConfig = z.output<typeof createConfigSchema>;
export type UpdateConfig = z.output<typeof updateConfigSchema>;
export type ConfigCategory = z.output<typeof ConfigCategoryEnum>;
export type ConfigDataType = z.output<typeof ConfigDataTypeEnum>;
export type ServiceConfig = z.output<typeof serviceConfigSchema>;
export type BrokerConfig = z.output<typeof brokerConfigSchema>;
export type SecurityConfig = z.output<typeof securityConfigSchema>;
export type DatabaseConfig = z.output<typeof databaseConfigSchema>;
export type AppConfig = z.output<typeof appConfigSchema>;
export type ConfigQuery = z.output<typeof configQuerySchema>;
export type BulkConfig = z.output<typeof bulkConfigSchema>;

// ==================== CONFIGURATION CONSTANTS ====================

/**
 * Default configuration values for seeding
 */
export const DEFAULT_CONFIGS = {
  service: {
    API_BASE_URL: 'http://localhost:3000',
  },
  broker: {
    BROKER_KEY_ROTATION_INTERVAL: 86400000, // 24 hours
    BROKER_HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
    BROKER_CONNECTION_TIMEOUT: 10000, // 10 seconds
    BROKER_MAX_RETRY_ATTEMPTS: 3,
    BROKER_SESSION_TIMEOUT: 3600000, // 1 hour
  },
  security: {
    BROKER_RATE_LIMIT_REQUESTS: 100,
    BROKER_RATE_LIMIT_WINDOW: 60000, // 1 minute
  },
  database: {
    QUESTDB_MAX_CONNECTIONS: 10,
  },
} as const;

/**
 * Configuration descriptions for seeding
 */
export const CONFIG_DESCRIPTIONS = {
  API_BASE_URL: 'Base URL for API endpoints and callbacks',
  ANALYSER_SERVICE_URL: 'URL for the market analysis service',
  OMS_SERVICE_URL: 'URL for the order management service',
  TICKER_SERVICE_URL: 'URL for the market data ticker service',
  SIMULATOR_SERVICE_URL: 'URL for the paper trading simulator service',
  BROKER_KEY_ROTATION_INTERVAL: 'Interval for rotating broker encryption keys (milliseconds)',
  BROKER_HEALTH_CHECK_INTERVAL: 'Interval for broker health checks (milliseconds)',
  BROKER_CONNECTION_TIMEOUT: 'Timeout for broker connections (milliseconds)',
  BROKER_MAX_RETRY_ATTEMPTS: 'Maximum retry attempts for broker operations',
  BROKER_SESSION_TIMEOUT: 'Timeout for broker sessions (milliseconds)',
  BROKER_RATE_LIMIT_REQUESTS: 'Maximum requests per rate limit window',
  BROKER_RATE_LIMIT_WINDOW: 'Rate limit window duration (milliseconds)',
  QUESTDB_MAX_CONNECTIONS: 'Maximum connections to QuestDB',
} as const;
