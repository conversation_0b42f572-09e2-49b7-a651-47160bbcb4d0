import { Module } from '@nestjs/common';
import { ConfigService } from './config.service';
import { ConfigRepository } from './config.repository';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { CacheModule } from '../cache/cache.module';
import { EnvModule } from '../env/env.module';
import { UtilsModule } from '@app/utils';

/**
 * Configuration module providing application configuration management
 *
 * Features:
 * - Database-backed configuration storage
 * - Environment-specific configuration loading
 * - Caching for performance optimization
 * - Type-safe configuration access
 * - CRUD operations for configuration management
 *
 * Dependencies:
 * - DrizzleModule: Database operations
 * - CacheModule: Redis caching
 * - EnvModule: Environment detection
 * - UtilsModule: Utility services
 */
@Module({
  imports: [DrizzleModule, CacheModule, EnvModule, UtilsModule],
  providers: [ConfigService, ConfigRepository],
  exports: [ConfigService, ConfigRepository],
})
export class ConfigModule {}
