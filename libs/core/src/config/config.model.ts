import { pgTable, varchar, boolean, text, index, unique } from 'drizzle-orm/pg-core';
import { baseModel } from '@app/common/models';

// ==================== DATABASE SCHEMA ====================

/**
 * Configuration table schema for PatternTrade platform
 * Stores non-sensitive application configuration values with environment support
 *
 * Features:
 * - Environment-specific configuration values
 * - Category-based organization
 * - Data type validation support
 * - Active/inactive status management
 * - Comprehensive audit trail
 * - Optimized indexing for performance
 */
export const ConfigTable = pgTable(
  'configurations',
  {
    ...baseModel,

    // Core configuration fields
    key: varchar('key', { length: 255 }).notNull(),
    value: text('value').notNull(),
    description: varchar('description', { length: 500 }).notNull(),

    // Organization and metadata
    category: varchar('category', { length: 50 }).notNull(),
    environment: varchar('environment', { length: 20 }).notNull(),
    dataType: varchar('data_type', { length: 20 }).notNull(),

    // Status management
    isActive: boolean('is_active').notNull().default(true),
  },
  (table) => ({
    // Unique constraint: one key per environment
    uniqueKeyEnvironment: unique('unique_key_environment').on(table.key, table.environment),

    // Performance indexes
    keyIndex: index('idx_config_key').on(table.key),
    environmentIndex: index('idx_config_environment').on(table.environment),
    categoryIndex: index('idx_config_category').on(table.category),
    activeIndex: index('idx_config_active').on(table.isActive),
    keyEnvironmentIndex: index('idx_config_key_environment').on(table.key, table.environment),
    categoryEnvironmentIndex: index('idx_config_category_environment').on(table.category, table.environment),

    // Audit indexes
    createdAtIndex: index('idx_config_created_at').on(table.createdAt),
    updatedAtIndex: index('idx_config_updated_at').on(table.updatedAt),
  }),
);

// ==================== TYPE EXPORTS ====================

/**
 * Type for configuration table select operations
 */
export type ConfigTableSelect = typeof ConfigTable.$inferSelect;

/**
 * Type for configuration table insert operations
 */
export type ConfigTableInsert = typeof ConfigTable.$inferInsert;

// ==================== TABLE METADATA ====================

/**
 * Configuration table metadata for repository operations
 */
export const CONFIG_TABLE_METADATA = {
  tableName: 'configurations',
  primaryKey: 'id',
  uniqueConstraints: ['key', 'environment'],
  indexes: ['key', 'environment', 'category', 'isActive', 'createdAt', 'updatedAt'],
  auditFields: ['createdAt', 'createdBy', 'updatedAt', 'updatedBy', 'deletedAt', 'deletedBy'],
} as const;

// ==================== QUERY HELPERS ====================

/**
 * Common query conditions for configuration operations
 */
export const CONFIG_QUERY_CONDITIONS = {
  active: { isActive: true },
  inactive: { isActive: false },
  notDeleted: { deletedAt: null },
} as const;

/**
 * Configuration categories for validation
 */
export const CONFIG_CATEGORIES = ['service', 'broker', 'security', 'database', 'application'] as const;

/**
 * Supported data types for configuration values
 */
export const CONFIG_DATA_TYPES = ['string', 'number', 'boolean', 'url', 'json'] as const;

/**
 * Supported environments for configuration
 */
export const CONFIG_ENVIRONMENTS = ['local', 'docker', 'development', 'staging', 'production'] as const;
