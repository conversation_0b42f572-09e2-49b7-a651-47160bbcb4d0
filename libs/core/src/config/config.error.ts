import { BaseError } from '@app/common/errors';
import { z } from 'zod/v4';

// ==================== CONFIG ERROR TYPES ====================

/**
 * Configuration error enum defining all possible error types
 */
export const ConfigErrorEnum = z.enum([
  'VALIDATION_FAILED',
  'CONFIG_NOT_FOUND',
  'DUPLICATE_CONFIG_KEY',
  'INVALID_CONFIG_VALUE',
  'CACHE_ERROR',
  'DATABASE_ERROR',
  'ENVIRONMENT_MISMATCH',
  'INVALID_DATA_TYPE',
  'CONFIG_INACTIVE',
  'BULK_OPERATION_FAILED',
]);

/**
 * Error messages for each configuration error type
 */
export const ConfigErrorMessages: Record<ConfigErrorEnumType, string> = {
  VALIDATION_FAILED: 'Configuration validation failed',
  CONFIG_NOT_FOUND: 'Configuration not found',
  DUPLICATE_CONFIG_KEY: 'Configuration key already exists',
  INVALID_CONFIG_VALUE: 'Invalid configuration value',
  CACHE_ERROR: 'Configuration cache error',
  DATABASE_ERROR: 'Configuration database error',
  ENVIRONMENT_MISMATCH: 'Configuration environment mismatch',
  INVALID_DATA_TYPE: 'Invalid configuration data type',
  CONFIG_INACTIVE: 'Configuration is inactive',
  BULK_OPERATION_FAILED: 'Bulk configuration operation failed',
};

/**
 * Type definition for configuration error enum
 */
export type ConfigErrorEnumType = z.output<typeof ConfigErrorEnum>;

// ==================== CONFIG ERROR CLASS ====================

/**
 * Configuration-specific error class extending BaseError
 * Provides domain-specific error handling for configuration operations
 */
export class ConfigError extends BaseError<ConfigErrorEnumType> {
  constructor(name: ConfigErrorEnumType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain: 'CONFIG' as const,
      message: details?.message || ConfigErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

// ==================== SPECIALIZED ERROR CLASSES ====================

/**
 * Configuration not found error
 */
export class ConfigNotFoundError extends ConfigError {
  constructor(key: string, environment?: string) {
    const message = environment
      ? `Configuration '${key}' not found for environment '${environment}'`
      : `Configuration '${key}' not found`;

    super('CONFIG_NOT_FOUND', { message });
  }
}

/**
 * Duplicate configuration key error
 */
export class DuplicateConfigKeyError extends ConfigError {
  constructor(key: string, environment: string) {
    const message = `Configuration key '${key}' already exists for environment '${environment}'`;
    super('DUPLICATE_CONFIG_KEY', { message });
  }
}

/**
 * Invalid configuration value error
 */
export class InvalidConfigValueError extends ConfigError {
  constructor(key: string, value: string, expectedType: string) {
    const message = `Invalid value '${value}' for configuration '${key}'. Expected type: ${expectedType}`;
    super('INVALID_CONFIG_VALUE', { message });
  }
}

/**
 * Configuration inactive error
 */
export class ConfigInactiveError extends ConfigError {
  constructor(key: string) {
    const message = `Configuration '${key}' is inactive`;
    super('CONFIG_INACTIVE', { message });
  }
}

/**
 * Environment mismatch error
 */
export class EnvironmentMismatchError extends ConfigError {
  constructor(key: string, requestedEnv: string, configEnv: string) {
    const message = `Configuration '${key}' requested for environment '${requestedEnv}' but found for '${configEnv}'`;
    super('ENVIRONMENT_MISMATCH', { message });
  }
}

// ==================== ERROR UTILITIES ====================

/**
 * Utility function to create configuration validation errors
 */
export function createConfigValidationError(key: string, validationError: unknown): ConfigError {
  return new ConfigError('VALIDATION_FAILED', {
    message: `Configuration validation failed for '${key}': ${String(validationError)}`,
    cause: validationError,
  });
}

/**
 * Utility function to create configuration cache errors
 */
export function createConfigCacheError(operation: string, cause?: unknown): ConfigError {
  return new ConfigError('CACHE_ERROR', {
    message: `Configuration cache error during ${operation}`,
    cause,
  });
}

/**
 * Utility function to create configuration database errors
 */
export function createConfigDatabaseError(operation: string, cause?: unknown): ConfigError {
  return new ConfigError('DATABASE_ERROR', {
    message: `Configuration database error during ${operation}`,
    cause,
  });
}
