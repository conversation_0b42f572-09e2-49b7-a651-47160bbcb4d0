import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from './config.service';
import { ConfigRepository } from './config.repository';
import { CacheService } from '../cache/cache.service';
import { EnvService } from '../env/env.service';
import {
  Config,
  CreateConfig,
  UpdateConfig,
  ConfigNotFoundError,
  ConfigInactiveError,
  InvalidConfigValueError,
} from './config.schema';
import { Environment } from '@app/common/constants';

describe('ConfigService', () => {
  let service: ConfigService;
  let configRepository: jest.Mocked<ConfigRepository>;
  let cacheService: jest.Mocked<CacheService>;
  let envService: jest.Mocked<EnvService>;

  const mockConfig: Config = {
    id: 1,
    key: 'API_BASE_URL',
    value: 'http://localhost:3000',
    description: 'Base URL for API endpoints',
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'system',
    updatedAt: '2024-01-01T00:00:00Z',
    updatedBy: 'system',
  };

  beforeEach(async () => {
    const mockConfigRepository = {
      findByKey: jest.fn(),
      findByKeys: jest.fn(),
      findByCategory: jest.fn(),
      findByEnvironment: jest.fn(),
      create: jest.fn(),
      updateByKey: jest.fn(),
      deleteByKey: jest.fn(),
      activate: jest.fn(),
      deactivate: jest.fn(),
      bulkCreate: jest.fn(),
      query: jest.fn(),
    };

    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const mockEnvService = {
      get: jest.fn().mockReturnValue('local'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        {
          provide: ConfigRepository,
          useValue: mockConfigRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: EnvService,
          useValue: mockEnvService,
        },
      ],
    }).compile();

    service = module.get<ConfigService>(ConfigService);
    configRepository = module.get(ConfigRepository);
    cacheService = module.get(CacheService);
    envService = module.get(EnvService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('get', () => {
    it('should return cached value if available', async () => {
      const cachedValue = 'http://cached:3000';
      cacheService.get.mockResolvedValue(cachedValue);

      const result = await service.get('API_BASE_URL');

      expect(result).toBe(cachedValue);
      expect(cacheService.get).toHaveBeenCalledWith('config:local:API_BASE_URL');
      expect(configRepository.findByKey).not.toHaveBeenCalled();
    });

    it('should fetch from database and cache if not in cache', async () => {
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(mockConfig);

      const result = await service.get('API_BASE_URL');

      expect(result).toBe('http://localhost:3000');
      expect(configRepository.findByKey).toHaveBeenCalledWith('API_BASE_URL', 'local');
      expect(cacheService.set).toHaveBeenCalledWith('config:local:API_BASE_URL', 'http://localhost:3000', 300);
    });

    it('should throw ConfigNotFoundError if configuration not found', async () => {
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(null);

      await expect(service.get('NONEXISTENT_KEY')).rejects.toThrow(ConfigNotFoundError);
    });

    it('should throw ConfigInactiveError if configuration is inactive', async () => {
      const inactiveConfig = { ...mockConfig, isActive: false };
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(inactiveConfig);

      await expect(service.get('API_BASE_URL')).rejects.toThrow(ConfigInactiveError);
    });

    it('should parse number values correctly', async () => {
      const numberConfig = {
        ...mockConfig,
        key: 'TIMEOUT',
        value: '5000',
        dataType: 'number' as const,
      };
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(numberConfig);

      const result = await service.get<number>('TIMEOUT');

      expect(result).toBe(5000);
      expect(typeof result).toBe('number');
    });

    it('should parse boolean values correctly', async () => {
      const booleanConfig = {
        ...mockConfig,
        key: 'FEATURE_ENABLED',
        value: 'true',
        dataType: 'boolean' as const,
      };
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(booleanConfig);

      const result = await service.get<boolean>('FEATURE_ENABLED');

      expect(result).toBe(true);
      expect(typeof result).toBe('boolean');
    });

    it('should parse JSON values correctly', async () => {
      const jsonConfig = {
        ...mockConfig,
        key: 'JSON_CONFIG',
        value: '{"key": "value", "number": 42}',
        dataType: 'json' as const,
      };
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(jsonConfig);

      const result = await service.get<object>('JSON_CONFIG');

      expect(result).toEqual({ key: 'value', number: 42 });
    });

    it('should throw InvalidConfigValueError for invalid number', async () => {
      const invalidNumberConfig = {
        ...mockConfig,
        key: 'INVALID_NUMBER',
        value: 'not-a-number',
        dataType: 'number' as const,
      };
      cacheService.get.mockResolvedValue(null);
      configRepository.findByKey.mockResolvedValue(invalidNumberConfig);

      await expect(service.get('INVALID_NUMBER')).rejects.toThrow(InvalidConfigValueError);
    });
  });

  describe('getMany', () => {
    it('should return multiple configuration values', async () => {
      const configs = [
        mockConfig,
        { ...mockConfig, id: 2, key: 'TIMEOUT', value: '5000', dataType: 'number' as const },
      ];
      configRepository.findByKeys.mockResolvedValue(configs);

      const result = await service.getMany(['API_BASE_URL', 'TIMEOUT']);

      expect(result).toEqual({
        API_BASE_URL: 'http://localhost:3000',
        TIMEOUT: 5000,
      });
    });

    it('should skip inactive configurations', async () => {
      const configs = [mockConfig, { ...mockConfig, id: 2, key: 'INACTIVE_CONFIG', isActive: false }];
      configRepository.findByKeys.mockResolvedValue(configs);

      const result = await service.getMany(['API_BASE_URL', 'INACTIVE_CONFIG']);

      expect(result).toEqual({
        API_BASE_URL: 'http://localhost:3000',
      });
    });
  });

  describe('create', () => {
    it('should create new configuration', async () => {
      const createData: CreateConfig = {
        key: 'NEW_CONFIG',
        value: 'new-value',
        description: 'New configuration',
        category: 'application',
        environment: 'local',
        dataType: 'string',
        isActive: true,
      };
      configRepository.create.mockResolvedValue({ ...mockConfig, ...createData, id: 2 });

      const result = await service.create(createData);

      expect(result.key).toBe('NEW_CONFIG');
      expect(configRepository.create).toHaveBeenCalledWith(createData);
    });
  });

  describe('update', () => {
    it('should update configuration', async () => {
      const updateData: UpdateConfig = {
        value: 'updated-value',
        description: 'Updated description',
      };
      const updatedConfig = { ...mockConfig, ...updateData };
      configRepository.updateByKey.mockResolvedValue(updatedConfig);

      const result = await service.update('API_BASE_URL', updateData);

      expect(result.value).toBe('updated-value');
      expect(configRepository.updateByKey).toHaveBeenCalledWith('API_BASE_URL', 'local', updateData);
    });
  });

  describe('delete', () => {
    it('should delete configuration', async () => {
      configRepository.deleteByKey.mockResolvedValue(undefined);

      await service.delete('API_BASE_URL');

      expect(configRepository.deleteByKey).toHaveBeenCalledWith('API_BASE_URL', 'local');
    });
  });

  describe('activate/deactivate', () => {
    it('should activate configuration', async () => {
      const activatedConfig = { ...mockConfig, isActive: true };
      configRepository.activate.mockResolvedValue(activatedConfig);

      const result = await service.activate('API_BASE_URL');

      expect(result.isActive).toBe(true);
      expect(configRepository.activate).toHaveBeenCalledWith('API_BASE_URL', 'local');
    });

    it('should deactivate configuration', async () => {
      const deactivatedConfig = { ...mockConfig, isActive: false };
      configRepository.deactivate.mockResolvedValue(deactivatedConfig);

      const result = await service.deactivate('API_BASE_URL');

      expect(result.isActive).toBe(false);
      expect(configRepository.deactivate).toHaveBeenCalledWith('API_BASE_URL', 'local');
    });
  });

  describe('exists', () => {
    it('should return true if configuration exists', async () => {
      configRepository.findByKey.mockResolvedValue(mockConfig);

      const result = await service.exists('API_BASE_URL');

      expect(result).toBe(true);
    });

    it('should return false if configuration does not exist', async () => {
      configRepository.findByKey.mockResolvedValue(null);

      const result = await service.exists('NONEXISTENT_KEY');

      expect(result).toBe(false);
    });
  });

  describe('getCurrentEnvironment', () => {
    it('should return current environment', () => {
      const result = service.getCurrentEnvironment();

      expect(result).toBe('local');
    });
  });
});
