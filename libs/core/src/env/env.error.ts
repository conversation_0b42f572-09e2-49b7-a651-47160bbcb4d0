import { BaseError } from '@app/common/errors';
import { z } from 'zod/v4';

// Env Error Types
export const EnvErrorEnum = z.enum(['VALIDATION_FAILED']);

export const EnvErrorMessages: Record<z.output<typeof EnvErrorEnum>, string> = {
  VALIDATION_FAILED: 'Environment variable validation failed',
};

export type EnvErrorType = z.output<typeof EnvErrorEnum>;

export class EnvError extends BaseError<EnvErrorType> {
  constructor(name: EnvErrorType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain: 'ENV' as const,
      message: details?.message || EnvErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
