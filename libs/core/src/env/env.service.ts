import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { EnvType, EnvSchema } from './env.schema';
import { EnvError } from './env.error';

/**
 * Environment service for handling SENSITIVE configuration values only
 *
 * This service manages:
 * - Database credentials (DB_HOST, DB_PASSWORD, etc.)
 * - API keys and secrets (JWT_SECRET, ANTHROPIC_API_KEY, etc.)
 * - Encryption keys (BROKER_ENCRYPTION_KEY, etc.)
 * - Redis credentials
 * - QuestDB credentials
 *
 * For NON-SENSITIVE configuration values (timeouts, URLs, limits), use ConfigService instead.
 * ConfigService provides database-backed configuration with environment-specific values.
 */
@Injectable()
export class EnvService implements OnModuleInit {
  private readonly logger = new Logger(EnvService.name);
  private env!: EnvType;

  constructor(private readonly nestConfigService: NestConfigService<EnvType, true>) {
    // Initialize coreConfig with default values - will be properly set in onModuleInit
    this.env = {} as EnvType;
  }

  onModuleInit() {
    this.logger.log('Initializing environment configuration...');

    try {
      // Validate core environment variables
      this.validateEnv();

      this.logger.log('Environment configuration initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize environment configuration', error);
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Environment configuration initialization failed',
        cause: error,
      });
    }
  }

  /**
   * Get core environment variable with type safety
   */
  get<T extends keyof EnvType>(key: T): EnvType[T] {
    try {
      return this.nestConfigService.getOrThrow(key, { infer: true });
    } catch (error) {
      throw new EnvError('VALIDATION_FAILED', {
        message: `Core environment variable '${key}' is not defined`,
        cause: error,
      });
    }
  }

  /**
   * Get arbitrary environment variable (use with caution)
   * This method should only be used for environment variables that are not part of the core schema
   * @param key Environment variable name
   * @param defaultValue Optional default value
   * @returns Environment variable value or undefined
   */
  getArbitrary(key: string, defaultValue?: string): string | undefined {
    try {
      // Use process.env directly for arbitrary keys since ConfigService is typed for core keys only
      const value = process.env[key];
      return value !== undefined ? String(value) : defaultValue;
    } catch (error) {
      this.logger.warn(`Failed to get arbitrary environment variable '${key}':`, error);
      return defaultValue;
    }
  }

  /**
   * Get all core configuration
   */
  getAllEnv(): EnvType {
    if (!this.env) {
      this.validateEnv();
    }
    return this.env;
  }

  /**
   * Validate core environment variables
   */
  private validateEnv(): void {
    try {
      const rawEnv = {
        NODE_ENV: this.nestConfigService.get('NODE_ENV'),
        PORT: this.nestConfigService.get('PORT'),
        DB_HOST: this.nestConfigService.get('DB_HOST'),
        DB_PORT: this.nestConfigService.get('DB_PORT'),
        DB_NAME: this.nestConfigService.get('DB_NAME'),
        DB_USERNAME: this.nestConfigService.get('DB_USERNAME'),
        DB_PASSWORD: this.nestConfigService.get('DB_PASSWORD'),
        DB_SSL: this.nestConfigService.get('DB_SSL'),
        REDIS_HOST: this.nestConfigService.get('REDIS_HOST'),
        REDIS_PORT: this.nestConfigService.get('REDIS_PORT'),
        REDIS_USERNAME: this.nestConfigService.get('REDIS_USERNAME'),
        REDIS_PASSWORD: this.nestConfigService.get('REDIS_PASSWORD'),
        REDIS_URL: this.nestConfigService.get('REDIS_URL'),
        QUESTDB_HOST: this.nestConfigService.get('QUESTDB_HOST'),
        QUESTDB_PORT: this.nestConfigService.get('QUESTDB_PORT'),
        QUESTDB_USERNAME: this.nestConfigService.get('QUESTDB_USERNAME'),
        QUESTDB_PASSWORD: this.nestConfigService.get('QUESTDB_PASSWORD'),
        QUESTDB_DATABASE: this.nestConfigService.get('QUESTDB_DATABASE'),
        QUESTDB_SSL: this.nestConfigService.get('QUESTDB_SSL'),
        JWT_SECRET: this.nestConfigService.get('JWT_SECRET'),
        SESSION_SECRET_KEY: this.nestConfigService.get('SESSION_SECRET_KEY'),
        ANTHROPIC_API_KEY: this.nestConfigService.get('ANTHROPIC_API_KEY'),
        OPENAI_API_KEY: this.nestConfigService.get('OPENAI_API_KEY'),

        // Broker Management (sensitive)
        BROKER_ENCRYPTION_KEY: this.nestConfigService.get('BROKER_ENCRYPTION_KEY'),
        BROKER_ENCRYPTION_SALT: this.nestConfigService.get('BROKER_ENCRYPTION_SALT'),
      };

      this.env = EnvSchema.parse(rawEnv);
      this.logger.log('Core environment configuration validated successfully');
    } catch (error) {
      this.logger.error('Core environment configuration validation failed', error);
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Core environment configuration validation failed',
        cause: error,
      });
    }
  }
}
