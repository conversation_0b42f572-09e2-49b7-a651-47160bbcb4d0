import type { FactoryProvider } from '@nestjs/common';

import { Redis } from 'ioredis';
import { EnvService } from '../env';

export const REDIS_SESSION_STORE = 'REDIS_SESSION_STORE';

export const redisSessionStoreProvider: FactoryProvider = {
  provide: REDIS_SESSION_STORE,
  useFactory: (envService: EnvService): Redis => {
    const redis = new Redis({
      host: envService.get('REDIS_HOST'),
      port: envService.get('REDIS_PORT'),
      username: envService.get('REDIS_USERNAME'),
      password: envService.get('REDIS_PASSWORD'),
    });
    return redis;
  },
  inject: [EnvService],
};
