import { z } from 'zod/v4';
import { utcDateTimeSchema } from '@app/common/schema';

// Session user information schema
export const SessionUserSchema = z.object({
  id: z.string().min(1),
  email: z.email(),
  orgId: z.string().min(1),
  role: z.string().min(1),
  permissions: z.array(z.string()).optional(),
});

export type SessionUser = z.output<typeof SessionUserSchema>;

// Session subscription schema
export const SessionSubscriptionSchema = z.object({
  id: z.string().min(1),
  plan: z.string().min(1),
  status: z.enum(['ACTIVE', 'SUSPENDED', 'CANCELLED', 'EXPIRED']),
  expiresAt: utcDateTimeSchema,
  features: z.array(z.string()).optional(),
});

export type SessionSubscription = z.output<typeof SessionSubscriptionSchema>;

// Core session data schema
export const SessionDataSchema = z.object({
  id: z.string().min(1),
  isAuthenticated: z.boolean(),
  user: SessionUserSchema.optional(),
  isOutdated: z.boolean().default(false),
  accessToken: z.string().optional(),
  orgId: z.string().optional(),
  canRegisterOrg: z.boolean().default(false),
  dbName: z.string().optional(),
  subscription: SessionSubscriptionSchema.optional(),
  createdAt: utcDateTimeSchema,
  updatedAt: utcDateTimeSchema,
  expiresAt: utcDateTimeSchema,
  lastAccessedAt: utcDateTimeSchema,
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

export type SessionData = z.output<typeof SessionDataSchema>;

// Session configuration schema
export const SessionConfigSchema = z.object({
  secret: z.string().min(32), // Minimum 32 characters for security
  name: z.string().min(1).default('sessionId'),
  resave: z.boolean().default(false),
  saveUninitialized: z.boolean().default(false),
  rolling: z.boolean().default(false),
  cookie: z.object({
    secure: z.boolean().default(false),
    httpOnly: z.boolean().default(true),
    maxAge: z.number().min(60000).max(86400000), // 1 minute to 24 hours
    sameSite: z.enum(['strict', 'lax', 'none']).default('lax'),
    domain: z.string().optional(),
    path: z.string().default('/'),
  }),
  store: z.object({
    prefix: z.string().default('sess:'),
    ttl: z.number().min(60).max(86400), // 1 minute to 24 hours in seconds
  }),
});

export type SessionConfig = z.output<typeof SessionConfigSchema>;

// Session creation request schema
export const CreateSessionSchema = z.object({
  user: SessionUserSchema,
  accessToken: z.string().min(1),
  subscription: SessionSubscriptionSchema.optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  expiresInSeconds: z.number().min(60).max(86400).default(3600), // Default 1 hour
});

export type CreateSessionRequest = z.output<typeof CreateSessionSchema>;

// Session update request schema
export const UpdateSessionSchema = z.object({
  accessToken: z.string().optional(),
  subscription: SessionSubscriptionSchema.optional(),
  isOutdated: z.boolean().optional(),
  lastAccessedAt: utcDateTimeSchema.optional(),
});

export type UpdateSessionRequest = z.output<typeof UpdateSessionSchema>;

// Session validation response schema
export const SessionValidationSchema = z.object({
  isValid: z.boolean(),
  session: SessionDataSchema.optional(),
  reason: z.string().optional(),
  errorCode: z
    .enum(['SESSION_NOT_FOUND', 'SESSION_EXPIRED', 'SESSION_INVALID', 'SESSION_OUTDATED', 'USER_NOT_AUTHENTICATED'])
    .optional(),
});

export type SessionValidationResponse = z.output<typeof SessionValidationSchema>;
