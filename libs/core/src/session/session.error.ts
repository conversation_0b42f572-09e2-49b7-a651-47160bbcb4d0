import type { ErrorDomainType } from '@app/common/errors';
import { BaseError } from '@app/common/errors';
import { z } from 'zod/v4';

// Session error types enum
export const SessionErrorEnum = z.enum([
  'SESSION_NOT_FOUND',
  'SESSION_EXPIRED',
  'SESSION_INVALID',
  'SESSION_CREATION_FAILED',
  'SESSION_UPDATE_FAILED',
  'SESSION_DELETION_FAILED',
  'SESSION_VALIDATION_FAILED',
  'SESSION_STORAGE_ERROR',
  'SESSION_CONFIG_INVALID',
  'SESSION_SECRET_INVALID',
  'SESSION_MIDDLEWARE_ERROR',
  'SESSION_REDIS_CONNECTION_ERROR',
  'SESSION_SERIALIZATION_ERROR',
  'SESSION_DESERIALIZATION_ERROR',
  'SESSION_TIMEOUT',
  'SESSION_OUTDATED',
  'SESSION_SECURITY_VIOLATION',
  'SESSION_CONCURRENT_MODIFICATION',
  'SESSION_QUOTA_EXCEEDED',
  'SESSION_CLEANUP_FAILED',
]);

export type SessionErrorEnumType = z.output<typeof SessionErrorEnum>;

// Session error messages mapping
export const SessionErrorMessages: Record<SessionErrorEnumType, string> = {
  SESSION_NOT_FOUND: 'Session not found or has been destroyed',
  SESSION_EXPIRED: 'Session has expired and is no longer valid',
  SESSION_INVALID: 'Session data is invalid or corrupted',
  SESSION_CREATION_FAILED: 'Failed to create new session',
  SESSION_UPDATE_FAILED: 'Failed to update session data',
  SESSION_DELETION_FAILED: 'Failed to delete session',
  SESSION_VALIDATION_FAILED: 'Session validation failed',
  SESSION_STORAGE_ERROR: 'Error accessing session storage',
  SESSION_CONFIG_INVALID: 'Session configuration is invalid',
  SESSION_SECRET_INVALID: 'Session secret is invalid or missing',
  SESSION_MIDDLEWARE_ERROR: 'Session middleware configuration error',
  SESSION_REDIS_CONNECTION_ERROR: 'Failed to connect to Redis session store',
  SESSION_SERIALIZATION_ERROR: 'Failed to serialize session data',
  SESSION_DESERIALIZATION_ERROR: 'Failed to deserialize session data',
  SESSION_TIMEOUT: 'Session operation timed out',
  SESSION_OUTDATED: 'Session is outdated and needs refresh',
  SESSION_SECURITY_VIOLATION: 'Session security validation failed',
  SESSION_CONCURRENT_MODIFICATION: 'Session was modified by another process',
  SESSION_QUOTA_EXCEEDED: 'Session storage quota exceeded',
  SESSION_CLEANUP_FAILED: 'Failed to cleanup expired sessions',
};

// Session error class
export class SessionError extends BaseError<SessionErrorEnumType> {
  public readonly sessionId?: string;
  public readonly userId?: string;
  public readonly context?: Record<string, unknown>;

  constructor(
    name: SessionErrorEnumType,
    domain: ErrorDomainType,
    details?: {
      message?: string;
      cause?: unknown;
      sessionId?: string;
      userId?: string;
      context?: Record<string, unknown>;
    },
  ) {
    super({
      name,
      domain,
      message: details?.message ? details.message : SessionErrorMessages[name],
      cause: details?.cause,
    });

    // Store additional context as instance properties
    this.sessionId = details?.sessionId;
    this.userId = details?.userId;
    this.context = details?.context;

    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

// Session validation error for more specific validation failures
export class SessionValidationError extends SessionError {
  constructor(
    validationErrors: string[],
    sessionId?: string,
    details?: { cause?: unknown; context?: Record<string, unknown> },
  ) {
    super('SESSION_VALIDATION_FAILED', 'SESSION', {
      message: `Session validation failed: ${validationErrors.join(', ')}`,
      cause: details?.cause,
      sessionId,
      context: {
        validationErrors,
        ...details?.context,
      },
    });
  }
}

// Session security error for security-related failures
export class SessionSecurityError extends SessionError {
  constructor(
    securityViolation: string,
    sessionId?: string,
    details?: { cause?: unknown; context?: Record<string, unknown> },
  ) {
    super('SESSION_SECURITY_VIOLATION', 'SESSION', {
      message: `Session security violation: ${securityViolation}`,
      cause: details?.cause,
      sessionId,
      context: {
        securityViolation,
        ...details?.context,
      },
    });
  }
}
