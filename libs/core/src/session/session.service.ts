import { Inject, Injectable, Logger } from '@nestjs/common';
import { DateTimeUtilsService } from '@app/utils';
import { RedisStore } from 'connect-redis';
import session from 'express-session';
import { Redis } from 'ioredis';
import { NestExpressApplication } from '@nestjs/platform-express';

import { REDIS_SESSION_STORE } from './redis-session-store.provider';
import { SessionConfig, SessionConfigSchema, SessionData, SessionDataSchema } from './session.schema';
import { EnvService } from '../env';
import { SessionError } from './session.error';
import { ErrorDomainEnum } from '@app/common/errors';

// Extend express-session types with our validated schema
declare module 'express-session' {
  interface SessionData {
    id: string;
    isAuthenticated: boolean;
    user: {
      id: string;
      email: string;
      orgId: string;
      role: string;
      permissions?: string[];
    };
    isOutdated: boolean;
    accessToken: string;
    orgId: string;
    canRegisterOrg: boolean;
    dbName: string;
    subscription?: {
      id: string;
      plan: string;
      status: 'ACTIVE' | 'SUSPENDED' | 'CANCELLED' | 'EXPIRED';
      expiresAt: string;
      features?: string[];
    };
    createdAt: string;
    updatedAt: string;
    expiresAt: string;
    lastAccessedAt: string;
    ipAddress?: string;
    userAgent?: string;
  }
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private sessionMiddleware: ReturnType<typeof session> | undefined;
  private sessionConfig!: SessionConfig;

  constructor(
    @Inject(REDIS_SESSION_STORE) private redis: Redis,
    private envService: EnvService,
    private dateTimeUtils: DateTimeUtilsService,
  ) {
    // Initialize sessionConfig with default values - will be properly set when needed
    this.sessionConfig = {} as SessionConfig;
  }

  /**
   * Initialize and validate session configuration
   */
  private initializeConfig(): SessionConfig {
    try {
      const config = {
        secret: this.envService.get('SESSION_SECRET_KEY'),
        name: 'sessionId',
        resave: false,
        saveUninitialized: false,
        rolling: false,
        cookie: {
          secure: this.envService.get('NODE_ENV') === 'production',
          httpOnly: true,
          maxAge: 1000 * 60 * 60, // 1 hour in milliseconds
          sameSite: 'lax' as const,
          path: '/',
        },
        store: {
          prefix: 'sess:',
          ttl: 3600, // 1 hour in seconds
        },
      };

      // Validate configuration with Zod
      this.sessionConfig = SessionConfigSchema.parse(config);

      this.logger.log('Session configuration validated successfully');
      return this.sessionConfig;
    } catch (error) {
      throw new SessionError('SESSION_CONFIG_INVALID', ErrorDomainEnum.enum.SESSION, {
        message: 'Failed to validate session configuration',
        cause: error,
      });
    }
  }

  /**
   * Setup express-session middleware with Redis store and Zod validation
   */
  setup(app: NestExpressApplication): void {
    try {
      const config = this.initializeConfig();

      const redisStore = new RedisStore({
        client: this.redis,
        prefix: config.store.prefix,
        ttl: config.store.ttl,
      });

      const sessionConfig = session({
        secret: config.secret,
        name: config.name,
        resave: config.resave,
        saveUninitialized: config.saveUninitialized,
        rolling: config.rolling,
        store: redisStore,
        cookie: {
          secure: config.cookie.secure,
          httpOnly: config.cookie.httpOnly,
          maxAge: config.cookie.maxAge,
          sameSite: config.cookie.sameSite,
          domain: config.cookie.domain,
          path: config.cookie.path,
        },
      });

      this.sessionMiddleware = sessionConfig;
      app.use(sessionConfig);

      this.logger.log('Session middleware configured successfully', {
        cookieMaxAge: config.cookie.maxAge,
        storeTtl: config.store.ttl,
        secure: config.cookie.secure,
      });
    } catch (error) {
      throw new SessionError('SESSION_MIDDLEWARE_ERROR', ErrorDomainEnum.enum.SESSION, {
        message: 'Failed to setup session middleware',
        cause: error,
      });
    }
  }

  /**
   * Get the configured session middleware
   */
  getSessionMiddleWare(): ReturnType<typeof session> {
    if (!this.sessionMiddleware) {
      throw new SessionError('SESSION_MIDDLEWARE_ERROR', ErrorDomainEnum.enum.SESSION, {
        message: 'Session middleware not initialized. Call setup() first.',
      });
    }
    return this.sessionMiddleware;
  }

  /**
   * Create and validate session data
   */
  createSessionData(sessionData: Partial<SessionData>): SessionData {
    try {
      const now = this.dateTimeUtils.getUtcNow();
      const expiresAt = this.dateTimeUtils.toUtcStorage(
        this.dateTimeUtils.getNewDate(this.dateTimeUtils.getTime() + this.sessionConfig.cookie.maxAge),
      );

      const fullSessionData: SessionData = {
        id: sessionData.id || `sess_${this.dateTimeUtils.getTime()}_${Math.random().toString(36).substr(2, 9)}`,
        isAuthenticated: sessionData.isAuthenticated ?? false,
        user: sessionData.user,
        isOutdated: sessionData.isOutdated ?? false,
        accessToken: sessionData.accessToken || '',
        orgId: sessionData.orgId || sessionData.user?.orgId || '',
        canRegisterOrg: sessionData.canRegisterOrg ?? false,
        dbName: sessionData.dbName || `org_${sessionData.user?.orgId || 'default'}`,
        subscription: sessionData.subscription,
        createdAt: sessionData.createdAt || now,
        updatedAt: now,
        expiresAt: sessionData.expiresAt || expiresAt,
        lastAccessedAt: now,
        ipAddress: sessionData.ipAddress,
        userAgent: sessionData.userAgent,
      };

      // Validate with Zod schema
      const validatedSession = SessionDataSchema.parse(fullSessionData);

      this.logger.log('Session data created and validated', {
        sessionId: validatedSession.id,
        userId: validatedSession.user?.id,
        expiresAt: validatedSession.expiresAt,
      });

      return validatedSession;
    } catch (error) {
      throw new SessionError('SESSION_CREATION_FAILED', ErrorDomainEnum.enum.SESSION, {
        message: 'Failed to create valid session data',
        cause: error,
      });
    }
  }

  /**
   * Validate existing session data
   */
  validateSessionData(sessionData: unknown): { isValid: boolean; session?: SessionData; error?: string } {
    try {
      const validatedSession = SessionDataSchema.parse(sessionData);

      // Check if session is expired
      const now = this.dateTimeUtils.getNewDate();
      const expiresAt = this.dateTimeUtils.getNewDate(validatedSession.expiresAt);

      if (now > expiresAt) {
        return {
          isValid: false,
          error: 'Session has expired',
        };
      }

      return {
        isValid: true,
        session: validatedSession,
      };
    } catch (error) {
      this.logger.warn('Session validation failed', { error });
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Session validation failed',
      };
    }
  }

  /**
   * Update session access time
   */
  updateSessionAccess(sessionData: SessionData): SessionData {
    try {
      const updatedSession = {
        ...sessionData,
        lastAccessedAt: this.dateTimeUtils.getUtcNow(),
        updatedAt: this.dateTimeUtils.getUtcNow(),
      };

      return SessionDataSchema.parse(updatedSession);
    } catch (error) {
      throw new SessionError('SESSION_UPDATE_FAILED', ErrorDomainEnum.enum.SESSION, {
        message: 'Failed to update session access time',
        cause: error,
      });
    }
  }

  /**
   * Get session statistics from Redis store
   */
  async getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
  }> {
    try {
      const pattern = `${this.sessionConfig.store.prefix}*`;
      const keys = await this.redis.keys(pattern);

      let activeSessions = 0;

      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl > 0) {
          activeSessions++;
        }
      }

      return {
        totalSessions: keys.length,
        activeSessions,
      };
    } catch (error) {
      this.logger.error('Failed to get session statistics', { error });
      return {
        totalSessions: 0,
        activeSessions: 0,
      };
    }
  }

  /**
   * Clean up expired sessions from Redis
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const pattern = `${this.sessionConfig.store.prefix}*`;
      const keys = await this.redis.keys(pattern);

      let cleanedCount = 0;

      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl === -1 || ttl === -2) {
          // -1: no expiry, -2: key doesn't exist
          await this.redis.del(key);
          cleanedCount++;
        }
      }

      this.logger.log(`Cleaned up ${cleanedCount} expired sessions`);
      return cleanedCount;
    } catch (error) {
      throw new SessionError('SESSION_CLEANUP_FAILED', ErrorDomainEnum.enum.SESSION, {
        message: 'Failed to cleanup expired sessions',
        cause: error,
      });
    }
  }
}
