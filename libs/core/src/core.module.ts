import { Global, Module } from '@nestjs/common';
import { CoreService } from './core.service';
import { EnvironmentEnum, Environment } from '@app/common/constants';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { EnvSchema } from './env/env.schema';
import { CacheModule } from './cache/cache.module';
import { ConfigModule } from './config/config.module';
import { EnvModule } from './env/env.module';

import { HealthCheckLibModule } from './health-check/health-check.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { APP_PIPE } from '@nestjs/core';
import { ZodValidationPipe } from 'nestjs-zod';
import { LoggerModule } from 'nestjs-pino';
import { pinoConfig } from './pino-logger/pino-logger.config';
import { DrizzleModule } from './drizzle/drizzle.module';
import { QuestDBModule } from './questdb/questdb.module';
import { UtilsModule } from '@app/utils';
import { SessionModule } from './session';
import { ClientModule } from './client/client.module';
import { QueueModule } from './queue/queue.module';

const zodValidationProvider = {
  provide: APP_PIPE,
  useClass: ZodValidationPipe,
};

const getEnvFilePath = (nodeEnv: Environment) => {
  // Only return env file path for local environment
  if (nodeEnv === EnvironmentEnum.enum.local) {
    return 'env/.env.local';
  }
  // For all other environments, return undefined to use process.env only
  return undefined;
};

@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      envFilePath: getEnvFilePath((process.env['NODE_ENV'] as Environment) || EnvironmentEnum.enum.local),
      // Ignore env files for all environments except local
      ignoreEnvFile: process.env['NODE_ENV'] !== EnvironmentEnum.enum.local,
      validate: (env) => EnvSchema.parse(env),
      isGlobal: true,
      expandVariables: true,
      cache: true,
    }),
    LoggerModule.forRoot(pinoConfig),
    CacheModule,
    ConfigModule,
    EnvModule,
    HealthCheckLibModule,
    SchedulerModule,
    DrizzleModule,
    QuestDBModule,
    UtilsModule,
    SessionModule,
    ClientModule,
    QueueModule,
  ],
  providers: [CoreService, zodValidationProvider],
  exports: [
    CacheModule,
    ConfigModule,
    EnvModule,
    HealthCheckLibModule,
    SchedulerModule,
    DrizzleModule,
    QuestDBModule,
    SessionModule,
    ClientModule,
    QueueModule,
  ],
})
export class CoreModule {}
