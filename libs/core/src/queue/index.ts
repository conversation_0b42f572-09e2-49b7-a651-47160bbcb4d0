// Queue Module Exports
export * from './queue.module';
export * from './queue.service';
export * from './queue-health.service';
export * from './queue-health-monitor.service';
export * from './bullboard.service';

// Base Classes
export * from './base-queue.service';
export * from './base-worker.service';

// Configuration and Types
export * from './queue.config';
export * from './queue.error';

// Re-export BullMQ types for convenience
export type { Queue, Worker, Job, JobsOptions, WorkerOptions, RepeatOptions, QueueOptions } from 'bullmq';
