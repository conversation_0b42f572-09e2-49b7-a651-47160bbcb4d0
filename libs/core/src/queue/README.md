# Queue System

BullMQ-based background job processing system for PatternTrade API.

## Features

- ✅ **BaseQueueService**: Abstract class for creating queue services
- ✅ **BaseWorkerService**: Abstract class for creating job workers
- ✅ **QueueService**: Central queue management
- ✅ **Health Monitoring**: Automated health checks and monitoring
- ✅ **BullBoard Dashboard**: Web UI for queue monitoring
- ✅ **Error Handling**: Comprehensive error handling with retries
- ✅ **TypeScript Support**: Full type safety with Zod validation
- ✅ **Redis Backend**: Uses Redis (DragonflyDB) for reliability

## Quick Start

### 1. Create a Queue Service

```typescript
import { Injectable } from '@nestjs/common';
import { BaseQueueService, QueueNameEnum } from '@app/core/queue';

@Injectable()
export class MyQueueService extends BaseQueueService<MyJobDataType> {
  constructor(queueService: QueueService, envService: EnvService, dateTimeUtils: DateTimeUtilsService) {
    const connectionConfig = createQueueConnection(envService);
    super(QueueNameEnum.enum.MY_QUEUE, connectionConfig, DEFAULT_QUEUE_OPTIONS, dateTimeUtils);
  }

  async addMyJob(data: MyJobDataType): Promise<string> {
    const job = await this.addJob('my-job', data);
    return job.id || 'unknown';
  }
}
```

### 2. Create a Worker Service

```typescript
import { Injectable } from '@nestjs/common';
import { BaseWorkerService, type JobResult } from '@app/core/queue';

@Injectable()
export class MyWorkerService extends BaseWorkerService<MyJobDataType> {
  constructor(
    queueService: QueueService,
    queueHealthService: QueueHealthService,
    envService: EnvService,
    dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super(QueueNameEnum.enum.MY_QUEUE, connectionConfig, DEFAULT_WORKER_OPTIONS, dateTimeUtils);

    // Register health check
    this.queueHealthService.registerWorkerHealthCheck(MyWorkerService.name, () => this.getHealthStatus());
  }

  protected async processJob(job: Job<MyJobDataType>): Promise<JobResult<MyResultType>> {
    // Your job processing logic here
    const result = await this.doWork(job.data);

    return {
      success: true,
      data: result,
      jobId: job.id || 'unknown',
      queueName: this.queueName,
      processedAt: this.dateTimeUtils.getCurrentUtcDateTime(),
      duration: Date.now() - startTime,
      attempts: job.attemptsMade,
    };
  }

  private async doWork(data: MyJobDataType): Promise<MyResultType> {
    // Implement your business logic
    return { processed: true };
  }
}
```

### 3. Add to Your Module

```typescript
@Module({
  providers: [MyService, MyQueueService, MyWorkerService],
  exports: [MyService, MyQueueService, MyWorkerService],
})
export class MyModule {}
```

### 4. Use in Your Service

```typescript
@Injectable()
export class MyService {
  constructor(private readonly myQueueService: MyQueueService) {}

  async triggerBackgroundJob(data: MyJobDataType): Promise<string> {
    return await this.myQueueService.addMyJob(data);
  }
}
```

## Dashboard

Access the BullBoard dashboard at: `http://localhost:3000/admin/queues`

- Monitor queue status and job progress
- Retry failed jobs
- View job details and logs
- Manage queue operations

## Health Monitoring

The queue system integrates with the main health check system:

```bash
# Check overall health (includes queue status)
curl http://localhost:3000/health

# Response includes queue status
{
  "appStatus": true,
  "queueStatus": true,
  "services": {
    "queues": {
      "status": true,
      "summary": {
        "totalQueues": 3,
        "healthyQueues": 3,
        "totalWorkers": 5,
        "runningWorkers": 5
      }
    }
  }
}
```

## Configuration

### Environment Variables

```bash
# Redis Configuration (required)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=optional
REDIS_PASSWORD=optional
```

### Queue Priorities

```typescript
// Available priority levels
'CRITICAL'; // Priority 1 - Highest
'HIGH'; // Priority 2
'NORMAL'; // Priority 5 - Default
'LOW'; // Priority 7
'BACKGROUND'; // Priority 10 - Lowest

// Usage
await queueService.addJobWithPriority('urgent-job', data, 'HIGH');
```

## Error Handling

```typescript
try {
  await queueService.addJob('my-job', data);
} catch (error) {
  if (error instanceof QueueError) {
    logger.error('Queue operation failed:', {
      errorType: error.name,
      message: error.message,
      context: error.context,
    });
  }
}
```

## Testing

```typescript
describe('MyQueueService', () => {
  let service: MyQueueService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [MyQueueService /* mock dependencies */],
    }).compile();

    service = module.get<MyQueueService>(MyQueueService);
  });

  it('should add job successfully', async () => {
    const jobId = await service.addMyJob({ test: 'data' });
    expect(jobId).toBeDefined();
  });
});
```

## Example Implementation

See `libs/symbol/src/symbol-download.queue.ts` and `libs/symbol/src/symbol-download.worker.ts` for a complete example implementation.

## Documentation

For comprehensive documentation, see [docs/queue-system.md](../../../../docs/queue-system.md).

## Architecture

```
Queue Module (Core)
├── BaseQueueService     # Abstract queue service
├── BaseWorkerService    # Abstract worker service
├── QueueService         # Queue management
├── QueueHealthService   # Health monitoring
├── BullBoardService     # Dashboard UI
└── QueueHealthMonitor   # Automated monitoring

Feature Modules
├── SymbolModule
│   ├── SymbolDownloadQueue   # Extends BaseQueueService
│   └── SymbolDownloadWorker  # Extends BaseWorkerService
└── YourModule
    ├── YourQueue            # Your queue implementation
    └── YourWorker           # Your worker implementation
```

## Best Practices

1. **Keep jobs idempotent** - Jobs should be safe to retry
2. **Use appropriate priorities** - Critical jobs get processed first
3. **Validate job data** - Use Zod schemas for type safety
4. **Monitor health** - Register worker health checks
5. **Handle errors gracefully** - Implement proper error handling
6. **Update progress** - For long-running jobs, update progress
7. **Clean up regularly** - Remove old completed/failed jobs

## Troubleshooting

### Common Issues

- **Redis connection failed**: Check Redis server and configuration
- **Jobs not processing**: Verify worker is running and registered
- **High memory usage**: Implement job cleanup and optimize processing
- **Dashboard not accessible**: Check authentication and admin privileges

### Debug Commands

```typescript
// Check queue health
const health = await queueHealthService.getSystemHealthStatus();

// Get job details
const job = await queueService.getJob(jobId);

// Force health check
await queueHealthMonitorService.forceHealthCheck();
```
