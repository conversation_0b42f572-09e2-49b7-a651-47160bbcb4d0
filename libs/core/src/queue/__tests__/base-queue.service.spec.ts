import { Test, TestingModule } from '@nestjs/testing';
import { BaseQueueService } from '../base-queue.service';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError } from '../queue.error';
import { createQueueConnection, DEFAULT_QUEUE_OPTIONS } from '../queue.config';

// Mock Redis and BullMQ
jest.mock('ioredis');
jest.mock('bullmq');

// Test implementation of BaseQueueService
class TestQueueService extends BaseQueueService<{ testData: string }> {
  constructor(dateTimeUtils: DateTimeUtilsService) {
    const mockConnectionConfig = {
      host: 'localhost',
      port: 6379,
      db: 0,
      keyPrefix: 'test:',
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
    };

    super('test-queue', mockConnectionConfig, DEFAULT_QUEUE_OPTIONS, dateTimeUtils);
  }
}

describe('BaseQueueService', () => {
  let service: TestQueueService;
  let dateTimeUtils: DateTimeUtilsService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        {
          provide: DateTimeUtilsService,
          useValue: {
            getCurrentUtcDateTime: jest.fn().mockReturnValue('2024-01-01T00:00:00.000Z'),
            getNewDate: jest.fn().mockReturnValue(new Date('2024-01-01T00:00:00.000Z')),
          },
        },
      ],
    }).compile();

    dateTimeUtils = module.get<DateTimeUtilsService>(DateTimeUtilsService);
    service = new TestQueueService(dateTimeUtils);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should initialize with correct queue name', () => {
      expect(service['queueName']).toBe('test-queue');
    });
  });

  describe('addJob', () => {
    it('should add a job successfully', async () => {
      const mockJob = {
        id: 'test-job-1',
        name: 'test-job',
        data: { testData: 'test' },
      };

      // Mock the queue.add method
      service['queue'].add = jest.fn().mockResolvedValue(mockJob);

      const result = await service.addJob('test-job', { testData: 'test' });

      expect(result).toEqual(mockJob);
      expect(service['queue'].add).toHaveBeenCalledWith(
        'test-job',
        { testData: 'test' },
        expect.objectContaining(DEFAULT_QUEUE_OPTIONS.defaultJobOptions),
      );
    });

    it('should throw QueueError when job addition fails', async () => {
      const error = new Error('Redis connection failed');
      service['queue'].add = jest.fn().mockRejectedValue(error);

      await expect(service.addJob('test-job', { testData: 'test' })).rejects.toThrow(QueueError);
    });
  });

  describe('addJobWithPriority', () => {
    it('should add a job with correct priority', async () => {
      const mockJob = {
        id: 'test-job-1',
        name: 'test-job',
        data: { testData: 'test' },
      };

      service['queue'].add = jest.fn().mockResolvedValue(mockJob);

      await service.addJobWithPriority('test-job', { testData: 'test' }, 'HIGH');

      expect(service['queue'].add).toHaveBeenCalledWith(
        'test-job',
        { testData: 'test' },
        expect.objectContaining({
          ...DEFAULT_QUEUE_OPTIONS.defaultJobOptions,
          priority: 2, // HIGH priority maps to 2
        }),
      );
    });
  });

  describe('addDelayedJob', () => {
    it('should add a delayed job', async () => {
      const mockJob = {
        id: 'test-job-1',
        name: 'test-job',
        data: { testData: 'test' },
      };

      service['queue'].add = jest.fn().mockResolvedValue(mockJob);

      await service.addDelayedJob('test-job', { testData: 'test' }, 5000);

      expect(service['queue'].add).toHaveBeenCalledWith(
        'test-job',
        { testData: 'test' },
        expect.objectContaining({
          ...DEFAULT_QUEUE_OPTIONS.defaultJobOptions,
          delay: 5000,
        }),
      );
    });
  });

  describe('getJob', () => {
    it('should retrieve a job by ID', async () => {
      const mockJob = {
        id: 'test-job-1',
        name: 'test-job',
        data: { testData: 'test' },
      };

      service['queue'].getJob = jest.fn().mockResolvedValue(mockJob);

      const result = await service.getJob('test-job-1');

      expect(result).toEqual(mockJob);
      expect(service['queue'].getJob).toHaveBeenCalledWith('test-job-1');
    });

    it('should return undefined for non-existent job', async () => {
      service['queue'].getJob = jest.fn().mockResolvedValue(undefined);

      const result = await service.getJob('non-existent');

      expect(result).toBeUndefined();
    });

    it('should throw QueueError when job retrieval fails', async () => {
      const error = new Error('Redis connection failed');
      service['queue'].getJob = jest.fn().mockRejectedValue(error);

      await expect(service.getJob('test-job-1')).rejects.toThrow(QueueError);
    });
  });

  describe('removeJob', () => {
    it('should remove a job successfully', async () => {
      const mockJob = {
        id: 'test-job-1',
        remove: jest.fn().mockResolvedValue(undefined),
      };

      service['queue'].getJob = jest.fn().mockResolvedValue(mockJob);

      await service.removeJob('test-job-1');

      expect(mockJob.remove).toHaveBeenCalled();
    });

    it('should handle removal of non-existent job', async () => {
      service['queue'].getJob = jest.fn().mockResolvedValue(undefined);

      // Should not throw error
      await expect(service.removeJob('non-existent')).resolves.toBeUndefined();
    });
  });

  describe('pauseQueue', () => {
    it('should pause the queue successfully', async () => {
      service['queue'].pause = jest.fn().mockResolvedValue(undefined);

      await service.pauseQueue();

      expect(service['queue'].pause).toHaveBeenCalled();
    });

    it('should throw QueueError when pause fails', async () => {
      const error = new Error('Pause failed');
      service['queue'].pause = jest.fn().mockRejectedValue(error);

      await expect(service.pauseQueue()).rejects.toThrow(QueueError);
    });
  });

  describe('resumeQueue', () => {
    it('should resume the queue successfully', async () => {
      service['queue'].resume = jest.fn().mockResolvedValue(undefined);

      await service.resumeQueue();

      expect(service['queue'].resume).toHaveBeenCalled();
    });
  });

  describe('getHealthStatus', () => {
    it('should return healthy status when queue is operational', async () => {
      // Mock queue methods
      service['queue'].getWaiting = jest.fn().mockResolvedValue([]);
      service['queue'].getActive = jest.fn().mockResolvedValue([]);
      service['queue'].getCompleted = jest.fn().mockResolvedValue([]);
      service['queue'].getFailed = jest.fn().mockResolvedValue([]);
      service['queue'].getDelayed = jest.fn().mockResolvedValue([]);
      service['queue'].isPaused = jest.fn().mockResolvedValue(false);

      const status = await service.getHealthStatus();

      expect(status).toEqual({
        name: 'test-queue',
        isHealthy: true,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
      });
    });

    it('should return unhealthy status when queue operations fail', async () => {
      const error = new Error('Queue operation failed');
      service['queue'].getWaiting = jest.fn().mockRejectedValue(error);

      const status = await service.getHealthStatus();

      expect(status.isHealthy).toBe(false);
      expect(status.lastError).toBe('Queue operation failed');
      expect(status.lastErrorAt).toBe('2024-01-01T00:00:00.000Z');
    });
  });

  describe('cleanQueue', () => {
    it('should clean completed and failed jobs', async () => {
      service['queue'].clean = jest.fn().mockResolvedValue(undefined);

      await service.cleanQueue(86400000); // 24 hours

      expect(service['queue'].clean).toHaveBeenCalledTimes(2);
      expect(service['queue'].clean).toHaveBeenCalledWith(86400000, 100, 'completed');
      expect(service['queue'].clean).toHaveBeenCalledWith(86400000, 50, 'failed');
    });
  });

  describe('onModuleDestroy', () => {
    it('should close queue and connection on destroy', async () => {
      service['queue'].close = jest.fn().mockResolvedValue(undefined);
      service['connection'].disconnect = jest.fn().mockResolvedValue(undefined);

      await service.onModuleDestroy();

      expect(service['queue'].close).toHaveBeenCalled();
      expect(service['connection'].disconnect).toHaveBeenCalled();
    });
  });
});
