import { Test, TestingModule } from '@nestjs/testing';
import { QueueService } from '../queue.service';
import { EnvService } from '../../env/env.service';
import { DateTimeUtilsService } from '@app/utils';
import { QueueNameEnum } from '../queue.config';

// Mock BullMQ
jest.mock('bullmq');

describe('QueueService', () => {
  let service: QueueService;
  let envService: EnvService;
  let dateTimeUtils: DateTimeUtilsService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        QueueService,
        {
          provide: EnvService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              const envVars: Record<string, any> = {
                REDIS_HOST: 'localhost',
                REDIS_PORT: 6379,
                REDIS_USERNAME: undefined,
                REDIS_PASSWORD: undefined,
              };
              return envVars[key];
            }),
          },
        },
        {
          provide: DateTimeUtilsService,
          useValue: {
            getCurrentUtcDateTime: jest.fn().mockReturnValue('2024-01-01T00:00:00.000Z'),
            getNewDate: jest.fn().mockReturnValue(new Date('2024-01-01T00:00:00.000Z')),
          },
        },
      ],
    }).compile();

    service = module.get<QueueService>(QueueService);
    envService = module.get<EnvService>(EnvService);
    dateTimeUtils = module.get<DateTimeUtilsService>(DateTimeUtilsService);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should create connection config from environment', () => {
      const config = service.getConnectionConfig();

      expect(config.host).toBe('localhost');
      expect(config.port).toBe(6379);
      expect(config.db).toBe(0);
      expect(config.keyPrefix).toBe('bull:');
    });
  });

  describe('getQueue', () => {
    it('should create and return a new queue', () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'SYMBOL_DOWNLOAD',
        close: jest.fn().mockResolvedValue(undefined),
      };
      Queue.mockImplementation(() => mockQueue);

      const queue = service.getQueue(QueueNameEnum.enum.SYMBOL_DOWNLOAD);

      expect(queue).toBe(mockQueue);
      expect(Queue).toHaveBeenCalledWith(
        'SYMBOL_DOWNLOAD',
        expect.objectContaining({
          connection: expect.objectContaining({
            host: 'localhost',
            port: 6379,
          }),
        }),
      );
    });

    it('should return existing queue if already created', () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'SYMBOL_DOWNLOAD',
        close: jest.fn().mockResolvedValue(undefined),
      };
      Queue.mockImplementation(() => mockQueue);

      // Create queue first time
      const queue1 = service.getQueue(QueueNameEnum.enum.SYMBOL_DOWNLOAD);

      // Get queue second time
      const queue2 = service.getQueue(QueueNameEnum.enum.SYMBOL_DOWNLOAD);

      expect(queue1).toBe(queue2);
      expect(Queue).toHaveBeenCalledTimes(1); // Should only create once
    });

    it('should create queue with custom name', () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'custom-queue',
        close: jest.fn().mockResolvedValue(undefined),
      };
      Queue.mockImplementation(() => mockQueue);

      const queue = service.getQueue('custom-queue');

      expect(queue).toBe(mockQueue);
      expect(Queue).toHaveBeenCalledWith('custom-queue', expect.any(Object));
    });
  });

  describe('getRegisteredQueueNames', () => {
    it('should return empty array initially', () => {
      const names = service.getRegisteredQueueNames();
      expect(names).toEqual([]);
    });

    it('should return registered queue names', () => {
      const { Queue } = require('bullmq');
      Queue.mockImplementation((name: string) => ({
        name,
        close: jest.fn().mockResolvedValue(undefined),
      }));

      service.getQueue('queue1');
      service.getQueue('queue2');

      const names = service.getRegisteredQueueNames();
      expect(names).toEqual(['queue1', 'queue2']);
    });
  });

  describe('getAllQueues', () => {
    it('should return all registered queue instances', () => {
      const { Queue } = require('bullmq');
      const mockQueue1 = { name: 'queue1', close: jest.fn() };
      const mockQueue2 = { name: 'queue2', close: jest.fn() };

      Queue.mockImplementation((name: string) => (name === 'queue1' ? mockQueue1 : mockQueue2));

      service.getQueue('queue1');
      service.getQueue('queue2');

      const queues = service.getAllQueues();
      expect(queues).toEqual([mockQueue1, mockQueue2]);
    });
  });

  describe('hasQueue', () => {
    it('should return false for non-existent queue', () => {
      expect(service.hasQueue('non-existent')).toBe(false);
    });

    it('should return true for existing queue', () => {
      const { Queue } = require('bullmq');
      Queue.mockImplementation(() => ({
        name: 'test-queue',
        close: jest.fn(),
      }));

      service.getQueue('test-queue');
      expect(service.hasQueue('test-queue')).toBe(true);
    });
  });

  describe('removeQueue', () => {
    it('should remove and close queue', async () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'test-queue',
        close: jest.fn().mockResolvedValue(undefined),
      };
      Queue.mockImplementation(() => mockQueue);

      service.getQueue('test-queue');
      expect(service.hasQueue('test-queue')).toBe(true);

      await service.removeQueue('test-queue');

      expect(service.hasQueue('test-queue')).toBe(false);
      expect(mockQueue.close).toHaveBeenCalled();
    });

    it('should handle removal of non-existent queue', async () => {
      // Should not throw
      await expect(service.removeQueue('non-existent')).resolves.toBeUndefined();
    });
  });

  describe('getAllQueuesHealthStatus', () => {
    it('should return health status for all queues', async () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'test-queue',
        getWaiting: jest.fn().mockResolvedValue([]),
        getActive: jest.fn().mockResolvedValue([]),
        getCompleted: jest.fn().mockResolvedValue([]),
        getFailed: jest.fn().mockResolvedValue([]),
        getDelayed: jest.fn().mockResolvedValue([]),
        isPaused: jest.fn().mockResolvedValue(false),
        close: jest.fn(),
      };
      Queue.mockImplementation(() => mockQueue);

      service.getQueue('test-queue');
      const healthStatuses = await service.getAllQueuesHealthStatus();

      expect(healthStatuses).toHaveLength(1);
      expect(healthStatuses[0]).toEqual({
        name: 'test-queue',
        isHealthy: true,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
      });
    });

    it('should handle queue health check errors', async () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'test-queue',
        getWaiting: jest.fn().mockRejectedValue(new Error('Health check failed')),
        close: jest.fn(),
      };
      Queue.mockImplementation(() => mockQueue);

      service.getQueue('test-queue');
      const healthStatuses = await service.getAllQueuesHealthStatus();

      expect(healthStatuses).toHaveLength(1);
      expect(healthStatuses[0].isHealthy).toBe(false);
      expect(healthStatuses[0].lastError).toBe('Health check failed');
    });
  });

  describe('pauseAllQueues', () => {
    it('should pause all registered queues', async () => {
      const { Queue } = require('bullmq');
      const mockQueue1 = {
        name: 'queue1',
        pause: jest.fn().mockResolvedValue(undefined),
        close: jest.fn(),
      };
      const mockQueue2 = {
        name: 'queue2',
        pause: jest.fn().mockResolvedValue(undefined),
        close: jest.fn(),
      };

      Queue.mockImplementation((name: string) => (name === 'queue1' ? mockQueue1 : mockQueue2));

      service.getQueue('queue1');
      service.getQueue('queue2');

      await service.pauseAllQueues();

      expect(mockQueue1.pause).toHaveBeenCalled();
      expect(mockQueue2.pause).toHaveBeenCalled();
    });
  });

  describe('resumeAllQueues', () => {
    it('should resume all registered queues', async () => {
      const { Queue } = require('bullmq');
      const mockQueue1 = {
        name: 'queue1',
        resume: jest.fn().mockResolvedValue(undefined),
        close: jest.fn(),
      };
      const mockQueue2 = {
        name: 'queue2',
        resume: jest.fn().mockResolvedValue(undefined),
        close: jest.fn(),
      };

      Queue.mockImplementation((name: string) => (name === 'queue1' ? mockQueue1 : mockQueue2));

      service.getQueue('queue1');
      service.getQueue('queue2');

      await service.resumeAllQueues();

      expect(mockQueue1.resume).toHaveBeenCalled();
      expect(mockQueue2.resume).toHaveBeenCalled();
    });
  });

  describe('cleanAllQueues', () => {
    it('should clean all registered queues', async () => {
      const { Queue } = require('bullmq');
      const mockQueue = {
        name: 'test-queue',
        clean: jest.fn().mockResolvedValue(undefined),
        close: jest.fn(),
      };
      Queue.mockImplementation(() => mockQueue);

      service.getQueue('test-queue');

      await service.cleanAllQueues(86400000);

      expect(mockQueue.clean).toHaveBeenCalledTimes(2);
      expect(mockQueue.clean).toHaveBeenCalledWith(86400000, 100, 'completed');
      expect(mockQueue.clean).toHaveBeenCalledWith(86400000, 50, 'failed');
    });
  });

  describe('onModuleDestroy', () => {
    it('should close all queues on module destroy', async () => {
      const { Queue } = require('bullmq');
      const mockQueue1 = {
        name: 'queue1',
        close: jest.fn().mockResolvedValue(undefined),
      };
      const mockQueue2 = {
        name: 'queue2',
        close: jest.fn().mockResolvedValue(undefined),
      };

      Queue.mockImplementation((name: string) => (name === 'queue1' ? mockQueue1 : mockQueue2));

      service.getQueue('queue1');
      service.getQueue('queue2');

      await service.onModuleDestroy();

      expect(mockQueue1.close).toHaveBeenCalled();
      expect(mockQueue2.close).toHaveBeenCalled();
      expect(service.getRegisteredQueueNames()).toEqual([]);
    });
  });
});
