import { z } from 'zod/v4';
import type { EnvService } from '../env/env.service';

/**
 * Queue configuration schemas for BullMQ
 */
export const QueueConnectionSchema = z.object({
  host: z.string().min(1),
  port: z.number().int().min(1).max(65535),
  username: z.string().optional(),
  password: z.string().optional(),
  db: z.number().int().min(0).max(15).default(0),
  maxRetriesPerRequest: z.number().int().min(1).default(3),
  retryDelayOnFailover: z.number().int().min(100).default(100),
  enableReadyCheck: z.boolean().default(true),
  lazyConnect: z.boolean().default(true),
  keepAlive: z.number().int().min(1000).default(30000),
  family: z.number().int().min(4).max(6).default(4),
  keyPrefix: z.string().default('bull:'),
});

export const QueueOptionsSchema = z.object({
  defaultJobOptions: z
    .object({
      removeOnComplete: z.number().int().min(1).default(100),
      removeOnFail: z.number().int().min(1).default(50),
      attempts: z.number().int().min(1).default(3),
      backoff: z
        .object({
          type: z.enum(['fixed', 'exponential']).default('exponential'),
          delay: z.number().int().min(1000).default(2000),
        })
        .default({ type: 'exponential', delay: 2000 }),
      delay: z.number().int().min(0).default(0),
    })
    .default({
      removeOnComplete: 100,
      removeOnFail: 50,
      attempts: 3,
      backoff: { type: 'exponential', delay: 2000 },
      delay: 0,
    }),
  settings: z
    .object({
      stalledInterval: z.number().int().min(1000).default(30000),
      maxStalledCount: z.number().int().min(1).default(1),
      retryProcessDelay: z.number().int().min(1000).default(5000),
    })
    .default({
      stalledInterval: 30000,
      maxStalledCount: 1,
      retryProcessDelay: 5000,
    }),
});

export const WorkerOptionsSchema = z.object({
  concurrency: z.number().int().min(1).default(1),
  limiter: z
    .object({
      max: z.number().int().min(1).default(100),
      duration: z.number().int().min(1000).default(60000),
    })
    .optional(),
  settings: z
    .object({
      stalledInterval: z.number().int().min(1000).default(30000),
      maxStalledCount: z.number().int().min(1).default(1),
    })
    .default({
      stalledInterval: 30000,
      maxStalledCount: 1,
    }),
});

export const JobDataSchema = z.object({
  id: z.string().optional(),
  data: z.record(z.string(), z.unknown()).default({}),
  opts: z
    .object({
      priority: z.number().int().min(1).max(10).default(5),
      delay: z.number().int().min(0).default(0),
      attempts: z.number().int().min(1).default(3),
      removeOnComplete: z.number().int().min(1).default(100),
      removeOnFail: z.number().int().min(1).default(50),
      repeat: z
        .object({
          pattern: z.string().optional(),
          every: z.number().int().min(1000).optional(),
          limit: z.number().int().min(1).optional(),
          endDate: z.date().optional(),
        })
        .optional(),
    })
    .default({
      priority: 5,
      delay: 0,
      attempts: 3,
      removeOnComplete: 100,
      removeOnFail: 50,
    }),
});

// Export types following PatternTrade API standards
export type QueueConnectionType = z.output<typeof QueueConnectionSchema>;
export type QueueOptionsType = z.output<typeof QueueOptionsSchema>;
export type WorkerOptionsType = z.output<typeof WorkerOptionsSchema>;
export type JobDataType = z.output<typeof JobDataSchema>;

/**
 * Create Redis connection configuration for BullMQ from environment
 */
export function createQueueConnection(envService: EnvService): QueueConnectionType {
  const config = {
    host: envService.get('REDIS_HOST'),
    port: envService.get('REDIS_PORT'),
    username: envService.get('REDIS_USERNAME'),
    password: envService.get('REDIS_PASSWORD'),
    db: 0, // Use separate DB for queues
    keyPrefix: 'bull:',
  };

  return QueueConnectionSchema.parse(config);
}

/**
 * Default queue options configuration
 */
export const DEFAULT_QUEUE_OPTIONS: QueueOptionsType = {
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    delay: 0,
  },
  settings: {
    stalledInterval: 30000,
    maxStalledCount: 1,
    retryProcessDelay: 5000,
  },
};

/**
 * Default worker options configuration
 */
export const DEFAULT_WORKER_OPTIONS: WorkerOptionsType = {
  concurrency: 1,
  settings: {
    stalledInterval: 30000,
    maxStalledCount: 1,
  },
};

/**
 * Queue names enum for type safety
 */
export const QueueNameEnum = z.enum([
  'SYMBOL_DOWNLOAD',
  'MARKET_DATA_SYNC',
  'ORDER_PROCESSING',
  'NOTIFICATION',
  'ANALYTICS',
  'CLEANUP',
]);

export type QueueNameType = z.output<typeof QueueNameEnum>;

/**
 * Queue priorities enum
 */
export const QueuePriorityEnum = z.enum([
  'CRITICAL', // 1
  'HIGH', // 2-3
  'NORMAL', // 4-6
  'LOW', // 7-8
  'BACKGROUND', // 9-10
]);

export type QueuePriorityType = z.output<typeof QueuePriorityEnum>;

/**
 * Map queue priority to numeric value
 */
export const QUEUE_PRIORITY_MAP: Record<QueuePriorityType, number> = {
  CRITICAL: 1,
  HIGH: 2,
  NORMAL: 5,
  LOW: 7,
  BACKGROUND: 10,
};
