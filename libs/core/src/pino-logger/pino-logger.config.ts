import { EnvironmentEnum } from '@app/common/constants';
import type { IncomingMessage, ServerResponse } from 'http';

export const pinoConfig = {
  pinoHttp: {
    level: process.env['NODE_ENV'] !== EnvironmentEnum.enum.production ? 'debug' : 'info',
    redact: {
      paths: ['req.headers', 'res.headers', 'req.remoteAddress', 'req.remotePort'],
      remove: true,
    },
    customProps: (_req: IncomingMessage, _res: ServerResponse) => ({
      context: 'HTTP',
    }),
    transport:
      process.env['NODE_ENV'] !== EnvironmentEnum.enum.production
        ? {
            target: 'pino-pretty',
            options: {
              messageFormat: '[{context}] : {msg}',
              singleLine: true,
              colorize: true,
              levelFirst: true,
              // translateTime: 'UTC:dd/mm/yyyy, h:MM:ss TT Z',
              translateTime: 'SYS:standard',
              ignore: 'pid,hostname,context',
            },
          }
        : undefined,
  },
};
