import type { ErrorDomainType } from '@app/common/errors';
import { BaseError } from '@app/common/errors';
import { z } from 'zod/v4';

export const DatabaseErrorEnum = z.enum(['POSTGRES_CONNECTION_NOT_ESTABLISHED', 'POSTGRES_QUERY_FAILED']);

export const DatabaseErrorMessages: Record<DatabaseErrorType, string> = {
  POSTGRES_CONNECTION_NOT_ESTABLISHED: 'Postgres connection not established',
  POSTGRES_QUERY_FAILED: 'Postgres query execution failed',
};

export type DatabaseErrorType = z.output<typeof DatabaseErrorEnum>;

export class DatabaseError extends BaseError<DatabaseErrorType> {
  constructor(name: DatabaseErrorType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : DatabaseErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
