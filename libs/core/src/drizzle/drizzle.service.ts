import { Injectable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { drizzle, PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres, { Sql } from 'postgres';
import { z } from 'zod/v4';
import { EnvService } from '../env/env.service';
import { HealthCheckService } from '../health-check/health-check.service';
import { ConfigTable } from '../config/config.model';
import { DatabaseError, DatabaseErrorEnum } from './database.error';
import { ErrorUtilsService, DateTimeUtilsService } from '@app/utils';

const schema = {
  ...ConfigTable,
};

export type DrizzleDB = PostgresJsDatabase<typeof schema>;

// Connection configuration interface with updated field names
interface ConnectionConfig {
  url?: string;
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  ssl?: boolean;
}

// Zod schema for connection configuration validation
export const ConnectionConfigSchema = z.object({
  url: z.url().optional(),
  host: z.string().min(1).optional(),
  port: z.coerce.number().int().min(1).max(65535).optional(),
  database: z.string().min(1).optional(),
  username: z.string().min(1).optional(),
  password: z.string().min(1).optional(),
  ssl: z.boolean().optional(),
});

// Drizzle connection options type (matching postgres.js)
type DrizzleConnectionOptions = {
  max?: number;
  idle_timeout?: number;
  connect_timeout?: number;
  prepare?: boolean;
  onnotice?: (notice: postgres.Notice) => void;
  onparameter?: (key: string, value: string) => void;
};

// Zod schema for Drizzle connection options
export const DrizzleConnectionOptionsSchema = z.object({
  max: z.number().int().positive().optional(),
  idle_timeout: z.number().int().positive().optional(),
  connect_timeout: z.number().int().positive().optional(),
  prepare: z.boolean().optional(),
}).partial();

// Enhanced query result interface
export interface QueryResult<T = Record<string, unknown>> {
  data: T[];
  rowCount: number;
  executedAt: string; // UTC timestamp
}

@Injectable()
export class DrizzleService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DrizzleService.name);

  private connection: Sql | null = null;
  private database: DrizzleDB | null = null;
  private isConnected = false;
  private connectionAttempts = 0;
  private readonly maxRetryAttempts = 5;
  private readonly retryDelay = 5000; // 5 seconds
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private reconnectionTimeout: NodeJS.Timeout | null = null;

  // Default connection options with resilience settings
  private readonly defaultConnectionOptions: DrizzleConnectionOptions = {
    max: 20, // Maximum connections in pool
    idle_timeout: 30, // 30 seconds idle timeout
    connect_timeout: 10, // 10 seconds connection timeout
    prepare: false, // Disable prepared statements for better compatibility
    onnotice: (notice) => {
      this.logger.warn(`PostgreSQL Notice: ${notice.message}`, {
        severity: notice.severity,
        code: notice.code,
        detail: notice.detail,
        hint: notice.hint,
      });
    },
    onparameter: (key, value) => {
      this.logger.debug(`PostgreSQL Parameter Change: ${key} = ${value}`);
    },
  };

  constructor(
    private readonly envService: EnvService,
    private readonly healthCheckService: HealthCheckService,
    private readonly datetimeUtils: DateTimeUtilsService,
    private readonly errorUtils:ErrorUtilsService
  ) {}

  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing Drizzle service...');
    await this.connectWithRetry();
    this.startHealthCheck();
  }

  async onModuleDestroy(): Promise<void> {
    this.logger.log('Shutting down Drizzle service...');
    await this.disconnect();
  }

  /**
   * Get the Drizzle database instance
   */
  getDb(): DrizzleDB {
    if (!this.database || !this.isConnected) {
      throw new DatabaseError(
        DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
        'DATABASE' as const,
        { message: 'Database not connected. Please ensure the service is initialized.' }
      );
    }
    return this.database;
  }

  /**
   * Execute a query and return structured results with UTC timestamp
   */
  async executeQuery<T = Record<string, unknown>>(query: string, params?: postgres.ParameterOrJSON<unknown>[]): Promise<QueryResult<T>> {
    if (!this.isConnectionHealthy()) {
      throw new DatabaseError(
        DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
        'DATABASE' as const,
        { message: 'Database connection is not healthy' }
      );
    }

    try {
      const startTime = this.datetimeUtils.getTime();
      const result = await this.connection!.unsafe(query, (params as postgres.ParameterOrJSON<never>[]) || []);
      const duration = this.datetimeUtils.getTime() - startTime;
      
      this.logger.debug(`Query executed successfully`, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        duration,
        rowCount: Array.isArray(result) ? result.length : 0,
      });

      return {
        data: Array.isArray(result) ? (result as unknown as T[]) : ([result] as unknown as T[]),
        rowCount: Array.isArray(result) ? result.length : 1,
        executedAt: this.datetimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error(`Query execution failed: ${this.errorUtils.getErrorMessage(error)}`, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        error: this.errorUtils.getErrorStack(error),
      });

      throw new DatabaseError(
        DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
        'DATABASE' as const,
        {
          message: `Query execution failed: ${this.errorUtils.getErrorMessage(error)}`,
          cause: error,
        }
      );
    }
  }

  /**
   * Helper method to create record with timestamps
   */
  createRecordWithTimestamps<T extends Record<string, unknown>>(data: T): T & { created_at: string; updated_at: string } {
    const utcNow = this.datetimeUtils.getUtcNow();
    return {
      ...data,
      created_at: utcNow,
      updated_at: utcNow,
    };
  }

  /**
   * Helper method to update record with timestamp
   */
  updateRecordWithTimestamp<T extends Record<string, unknown>>(data: T): T & { updated_at: string } {
    return {
      ...data,
      updated_at: this.datetimeUtils.getUtcNow(),
    };
  }

  /**
   * Get connection status
   */
  isConnectionHealthy(): boolean {
    return this.isConnected && this.connection !== null && this.database !== null;
  }

  /**
   * Get connection statistics
   */
  getConnectionStats() {
    if (!this.connection) {
      return {
        isConnected: false,
        connectionAttempts: this.connectionAttempts,
        lastError: 'No connection established',
      };
    }

    return {
      isConnected: this.isConnected,
      connectionAttempts: this.connectionAttempts,
      // Note: postgres.js doesn't expose pool stats directly
      // You might need to implement custom tracking if needed
    };
  }

  /**
   * Connect to the database with retry logic
   */
  private async connectWithRetry(): Promise<void> {
    for (let attempt = 1; attempt <= this.maxRetryAttempts; attempt++) {
      try {
        this.connectionAttempts = attempt;
        await this.connect();
        this.logger.log(`Database connection established on attempt ${attempt}`);
        return;
      } catch (error) {
        this.logger.warn(`Connection attempt ${attempt} failed: ${this.errorUtils.getErrorMessage(error)}`);

        if (attempt === this.maxRetryAttempts) {
          this.logger.error('Max connection attempts reached. Database connection failed.');
          throw new DatabaseError(
            DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
            'DATABASE' as const,
            { message: 'Failed to connect to database after maximum retry attempts' }
          );
        }

        // Wait before next attempt with exponential backoff
        const delay = this.retryDelay * Math.pow(2, attempt - 1);
        this.logger.log(`Retrying connection in ${delay}ms...`);
        await this.sleep(delay);
      }
    }
  }

  /**
   * Test database connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      if (!this.database) {
        return false;
      }

      // Simple connectivity test
      await this.database.execute(sql`SELECT 1 as test`);
      return true;
    } catch (error) {
      this.logger.warn(`Connection test failed: ${this.errorUtils.getErrorMessage(error)}`);
      return false;
    }
  }

  /**
   * Disconnect from the database
   */
  private async disconnect(): Promise<void> {
    try {
      // Clear intervals
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      if (this.reconnectionTimeout) {
        clearTimeout(this.reconnectionTimeout);
        this.reconnectionTimeout = null;
      }

      // Close connection
      if (this.connection) {
        await this.connection.end();
        this.connection = null;
      }

      this.database = null;
      this.isConnected = false;
      this.healthCheckService.setPostgresStatus(false);

      this.logger.log('Database connection closed successfully');
    } catch (error) {
      this.logger.error(`Error during disconnect: ${this.errorUtils.getErrorMessage(error)}`);
    }
  }

  /**
   * Establish database connection
   */
  private async connect(): Promise<void> {
    try {
      // Get database configuration
      const config = this.getConnectionConfig();

      // Validate connection configuration with Zod
      const validatedConfig = ConnectionConfigSchema.parse(config);

      this.logger.log('Establishing database connection...', {
        host: validatedConfig.host,
        port: validatedConfig.port,
        database: validatedConfig.database,
        username: validatedConfig.username,
      });

      // Create postgres connection with resilience options
      const connectionOptions = {
        ...this.defaultConnectionOptions,
        ...this.parseConnectionConfig(validatedConfig),
      };

      if (validatedConfig.url) {
        this.connection = postgres(validatedConfig.url, connectionOptions);
      } else {
        this.connection = postgres({
          host: validatedConfig.host!,
          port: validatedConfig.port!,
          database: validatedConfig.database!,
          username: validatedConfig.username!,
          password: validatedConfig.password!,
          ssl: validatedConfig.ssl,
          ...connectionOptions,
        });
      }

      // Create Drizzle instance
      this.database = drizzle(this.connection, { schema });

      // Test the connection
      await this.database.execute(sql`SELECT 1 as connection_test`);

      this.isConnected = true;
      this.healthCheckService.setPostgresStatus(true);

      this.logger.log('Database connection established successfully');
    } catch (error) {
      this.isConnected = false;
      this.healthCheckService.setPostgresStatus(false);

      if (error instanceof z.ZodError) {
        throw new DatabaseError(
          DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
          'DATABASE' as const,
          {
            message: `Database configuration validation failed: ${this.errorUtils.getErrorMessage(error)}`,
            cause: error,
          }
        );
      }

      throw new DatabaseError(
        DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
        'DATABASE' as const,
        {
          message: `Database connection failed: ${this.errorUtils.getErrorMessage(error)}`,
          cause: error,
        }
      );
    }
  }

  /**
   * Get connection configuration from EnvService
   */
  private getConnectionConfig(): ConnectionConfig {
    try {
      // Get all database-related environment variables
      const config: ConnectionConfig = {
        host: this.envService.get('DB_HOST'),
        port: this.envService.get('DB_PORT'),
        database: this.envService.get('DB_NAME'),
        username: this.envService.get('DB_USERNAME'),
        password: this.envService.get('DB_PASSWORD'),
        ssl: this.envService.get('DB_SSL'),
      };

      // Check if DATABASE_URL is available (takes precedence)
      try {
        const databaseUrl = this.envService.getArbitrary('DATABASE_URL');
        if (databaseUrl && typeof databaseUrl === 'string') {
          config.url = databaseUrl;
        }
      } catch {
        // DATABASE_URL is optional, use individual parameters
      }

      return config;
    } catch (error) {
      throw new DatabaseError(
        DatabaseErrorEnum.enum.POSTGRES_CONNECTION_NOT_ESTABLISHED,
        'DATABASE' as const,
        {
          message: `Failed to get database configuration: ${this.errorUtils.getErrorMessage(error)}`,
          cause: error,
        }
      );
    }
  }

  /**
   * Parse connection config for postgres.js options
   */
  private parseConnectionConfig(_config: ConnectionConfig): Partial<DrizzleConnectionOptions> {
    const options: Partial<DrizzleConnectionOptions> = {};

    // Add any custom parsing logic here if needed
    // For now, we use the defaults

    return options;
  }

  /**
   * Start periodic health checks
   */
  private startHealthCheck(): void {
    // Health check every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck().catch((error) => {
        this.logger.error(`Health check error: ${this.errorUtils.getErrorMessage(error)}`);
      });
    }, 30000); // 30 seconds
  }

  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const isHealthy = await this.testConnection();

      if (!isHealthy && this.isConnected) {
        this.logger.warn('Health check failed, marking connection as unhealthy');
        this.handleConnectionError(new Error('Health check failed'));
      } else if (isHealthy && !this.isConnected) {
        this.logger.log('Health check passed, connection restored');
        this.isConnected = true;
        this.healthCheckService.setPostgresStatus(true);
      }
    } catch (error) {
      this.logger.error(`Health check error: ${this.errorUtils.getErrorMessage(error)}`);
      this.handleConnectionError(this.errorUtils.toError(error));
    }
  }

  /**
   * Perform reconnection attempt
   */
  private async performReconnection(): Promise<void> {
    this.logger.log('Attempting database reconnection...');

    try {
      await this.disconnect();
      await this.connect();
      this.logger.log('Database reconnection successful');
    } catch (error) {
      this.logger.error(`Reconnection failed: ${this.errorUtils.getErrorMessage(error)}`);
      // Schedule another reconnection attempt
      this.scheduleReconnection();
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnection(): void {
    if (this.reconnectionTimeout) {
      return; // Already scheduled
    }

    this.reconnectionTimeout = setTimeout(() => {
      this.performReconnection()
        .catch((error) => {
          this.logger.error(`Reconnection failed: ${this.errorUtils.getErrorMessage(error)}`);
        })
        .finally(() => {
          this.reconnectionTimeout = null;
        });
    }, this.retryDelay);
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(error: Error): void {
    this.logger.error(`Connection error detected: ${this.errorUtils.getErrorMessage(error)}`, { error: this.errorUtils.getErrorStack(error) });

    this.isConnected = false;
    this.healthCheckService.setPostgresStatus(false);

    // Schedule reconnection if not already scheduled
    this.scheduleReconnection();
  }

  /**
   * Check if error is connection-related
   */
  private isConnectionError(error: Error): boolean {
    const connectionErrorCodes = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNRESET',
      'EPIPE',
      'connection terminated unexpectedly',
      'server closed the connection unexpectedly',
    ];

    const errorMessage = this.errorUtils.getErrorMessage(error).toLowerCase();
    const errorCode = (error as { code?: string }).code?.toUpperCase() || '';

    return connectionErrorCodes.some((code) => errorMessage.includes(code.toLowerCase()) || errorCode === code);
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
