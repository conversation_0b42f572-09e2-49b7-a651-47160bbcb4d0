import type { CacheModuleAsyncOptions } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-store';
import { EnvService } from '../env/env.service';
import { z } from 'zod/v4';
import { EnvError } from '../env/env.error';

// Redis configuration schemas
export const RedisConfigSchema = z.object({
  host: z.string().min(1),
  port: z.coerce.number().int().min(1).max(65535),
  username: z.string().optional(),
  password: z.string().optional(),
  db: z.coerce.number().int().min(0).max(15).default(0),
  connectTimeout: z.coerce.number().int().positive().default(10000),
  lazyConnect: z.boolean().default(true),
  retryDelayOnFailover: z.coerce.number().int().positive().default(100),
  maxRetriesPerRequest: z.coerce.number().int().positive().default(3),
  keepAlive: z.coerce.number().int().positive().default(30000),
});

export const RedisUrlConfigSchema = z.object({
  url: z.url(),
  connectTimeout: z.coerce.number().int().positive().default(10000),
  lazyConnect: z.boolean().default(true),
  retryDelayOnFailover: z.coerce.number().int().positive().default(100),
  maxRetriesPerRequest: z.coerce.number().int().positive().default(3),
  keepAlive: z.coerce.number().int().positive().default(30000),
});

export const CacheOptionsSchema = z.object({
  ttl: z.coerce.number().int().positive().default(86400000), // 1 day
  max: z.coerce.number().int().positive().default(100),
  refreshThreshold: z.coerce.number().int().positive().default(3000),
});

export type RedisConfigType = z.output<typeof RedisConfigSchema>;
export type RedisUrlConfigType = z.output<typeof RedisUrlConfigSchema>;
export type CacheOptionsType = z.output<typeof CacheOptionsSchema>;

/**
 * Validate and create Redis configuration from environment variables
 */
function createRedisConfig(envService: EnvService): RedisConfigType | RedisUrlConfigType {
  const redisUrl = envService.get('REDIS_URL');

  if (redisUrl) {
    // Use URL-based configuration
    try {
      return RedisUrlConfigSchema.parse({
        url: redisUrl,
      });
    } catch (error) {
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Invalid Redis URL configuration',
        cause: error,
      });
    }
  } else {
    // Use individual Redis parameters
    try {
      return RedisConfigSchema.parse({
        host: envService.get('REDIS_HOST'),
        port: envService.get('REDIS_PORT'),
        username: envService.get('REDIS_USERNAME'),
        password: envService.get('REDIS_PASSWORD'),
        db: process.env.REDIS_DB || 0,
      });
    } catch (error) {
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Invalid Redis configuration parameters',
        cause: error,
      });
    }
  }
}

/**
 * Create cache options from environment or defaults
 */
function createCacheOptions(): CacheOptionsType {
  try {
    return CacheOptionsSchema.parse({
      ttl: process.env.CACHE_TTL || 86400000,
      max: process.env.CACHE_MAX_ITEMS || 100,
      refreshThreshold: process.env.CACHE_REFRESH_THRESHOLD || 3000,
    });
  } catch (error) {
    throw new EnvError('VALIDATION_FAILED', {
      message: 'Invalid cache options configuration',
      cause: error,
    });
  }
}

/**
 * Cache Redis Options for NestJS Cache Module
 * Uses EnvService instead of FirestoreService for better decoupling
 */
export const CacheRedisOptions: CacheModuleAsyncOptions = {
  isGlobal: true,
  inject: [EnvService],
  useFactory: async (envService: EnvService) => {
    try {
      // Get and validate Redis configuration
      const redisConfig = createRedisConfig(envService);

      // Get and validate cache options
      const cacheOptions = createCacheOptions();

      // Create Redis store
      const store = await redisStore(redisConfig);

      return {
        store: () => store,
        ttl: cacheOptions.ttl,
        max: cacheOptions.max,
        refreshThreshold: cacheOptions.refreshThreshold,
      };
    } catch (error) {
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Failed to create Redis cache configuration',
        cause: error,
      });
    }
  },
};
