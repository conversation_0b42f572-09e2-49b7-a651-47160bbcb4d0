import { BaseError } from '@app/common/errors';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { z } from 'zod/v4';
import { DateTimeUtilsService } from '@app/utils';

// Cache operation schemas
export const CacheKeySchema = z.string().min(1).max(250);
export const CacheTtlSchema = z.number().int().positive().optional();
export const CacheValueSchema = z.unknown();

// Cache Error Types
export const CacheErrorEnum = z.enum([
  'OPERATION_FAILED',
  'INVALID_KEY',
  'INVALID_TTL',
  'SERIALIZATION_FAILED',
  'DESERIALIZATION_FAILED',
  'CONNECTION_FAILED',
]);

export type CacheErrorType = z.output<typeof CacheErrorEnum>;

export const CacheErrorMessages: Record<CacheErrorType, string> = {
  OPERATION_FAILED: 'Cache operation failed',
  INVALID_KEY: 'Invalid cache key format',
  INVALID_TTL: 'Invalid TTL value',
  SERIALIZATION_FAILED: 'Failed to serialize cache value',
  DESERIALIZATION_FAILED: 'Failed to deserialize cache value',
  CONNECTION_FAILED: 'Cache connection failed',
};

export class CacheError extends BaseError<CacheErrorType> {
  constructor(name: CacheErrorType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain: 'REDIS' as const,
      message: details?.message || CacheErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

// Cache operation result interfaces
export interface CacheOperationResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: CacheError;
  operation: string;
  key: string;
  timestamp: string;
  ttl?: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  errors: number;
  operations: number;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    errors: 0,
    operations: 0,
  };

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Get value from cache with validation and error handling
   */
  async get<T>(key: string): Promise<T | undefined> {
    const result = await this.safeOperation('get', key, async () => {
      // Validate key
      const validatedKey = this.validateKey(key);

      // Get from cache
      const value = await this.cacheManager.get<T>(validatedKey);

      if (value !== undefined) {
        this.stats.hits++;
        this.logger.debug(`Cache hit for key: ${validatedKey}`, {
          key: validatedKey,
          operation: 'get',
          hit: true,
        });
        return value;
      } else {
        this.stats.misses++;
        this.logger.debug(`Cache miss for key: ${validatedKey}`, {
          key: validatedKey,
          operation: 'get',
          hit: false,
        });
        return undefined;
      }
    });

    return result.success ? result.data : undefined;
  }

  /**
   * Set value in cache with validation and error handling
   */
  async set(key: string, value: unknown, ttl?: number): Promise<boolean> {
    const result = await this.safeOperation(
      'set',
      key,
      async () => {
        // Validate inputs
        const validatedKey = this.validateKey(key);
        const validatedTtl = ttl ? this.validateTtl(ttl) : undefined;

        // Validate value can be serialized
        try {
          JSON.stringify(value);
        } catch (error) {
          throw new CacheError('SERIALIZATION_FAILED', {
            message: `Failed to serialize value for key: ${validatedKey}`,
            cause: error,
          });
        }

        // Set in cache
        await this.cacheManager.set(validatedKey, value, validatedTtl);

        this.logger.debug(`Cache set for key: ${validatedKey}`, {
          key: validatedKey,
          operation: 'set',
          ttl: validatedTtl,
          valueType: typeof value,
        });

        return true;
      },
      ttl,
    );

    return result.success;
  }

  /**
   * Delete value from cache with validation and error handling
   */
  async del(key: string): Promise<boolean> {
    const result = await this.safeOperation('del', key, async () => {
      // Validate key
      const validatedKey = this.validateKey(key);

      // Delete from cache
      await this.cacheManager.del(validatedKey);

      this.logger.debug(`Cache deleted for key: ${validatedKey}`, {
        key: validatedKey,
        operation: 'del',
      });

      return true;
    });

    return result.success;
  }

  /**
   * Clear entire cache (not implemented - depends on cache store)
   */
  clear(): boolean {
    this.logger.warn('Cache clear operation not implemented for current cache manager');
    return false;
  }

  /**
   * Check if key exists in cache
   */
  async has(key: string): Promise<boolean> {
    const result = await this.safeOperation('has', key, async () => {
      // Validate key
      const validatedKey = this.validateKey(key);

      // Check existence
      const value = await this.cacheManager.get(validatedKey);
      const exists = value !== undefined;

      this.logger.debug(`Cache existence check for key: ${validatedKey}`, {
        key: validatedKey,
        operation: 'has',
        exists,
      });

      return exists;
    });

    return result.success ? (result.data ?? false) : false;
  }

  /**
   * Get multiple values from cache
   */
  async mget<T>(keys: string[]): Promise<Record<string, T | undefined>> {
    const results: Record<string, T | undefined> = {};

    // Process all keys in parallel
    const promises = keys.map(async (key) => {
      const value = await this.get<T>(key);
      return { key, value };
    });

    const keyResults = await Promise.all(promises);

    for (const { key, value } of keyResults) {
      results[key] = value;
    }

    this.logger.debug(`Multiple cache get completed`, {
      operation: 'mget',
      keyCount: keys.length,
      hitCount: Object.values(results).filter((v) => v !== undefined).length,
    });

    return results;
  }

  /**
   * Set multiple values in cache
   */
  async mset(values: Record<string, unknown>, ttl?: number): Promise<boolean> {
    const keys = Object.keys(values);

    // Process all keys in parallel
    const promises = keys.map(async (key) => {
      return this.set(key, values[key], ttl);
    });

    const results = await Promise.all(promises);
    const success = results.every((result) => result);

    this.logger.debug(`Multiple cache set completed`, {
      operation: 'mset',
      keyCount: keys.length,
      successCount: results.filter((r) => r).length,
      ttl,
    });

    return success;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      operations: 0,
    };
    this.logger.log('Cache statistics reset');
  }

  /**
   * Validate cache key format
   */
  private validateKey(key: string): string {
    try {
      return CacheKeySchema.parse(key);
    } catch (error) {
      throw new CacheError('INVALID_KEY', {
        message: `Invalid cache key: ${key}`,
        cause: error,
      });
    }
  }

  /**
   * Validate TTL value
   */
  private validateTtl(ttl: number): number {
    try {
      return CacheTtlSchema.parse(ttl)!;
    } catch (error) {
      throw new CacheError('INVALID_TTL', {
        message: `Invalid TTL value: ${ttl}`,
        cause: error,
      });
    }
  }

  /**
   * Safe operation wrapper with error handling and logging
   */
  private async safeOperation<T>(
    operation: string,
    key: string,
    fn: () => Promise<T>,
    ttl?: number,
  ): Promise<CacheOperationResult<T>> {
    this.stats.operations++;
    const startTime = this.dateTimeUtils.getTime();

    try {
      const data = await fn();
      const duration = this.dateTimeUtils.getTime() - startTime;

      this.logger.debug(`Cache operation completed successfully`, {
        operation,
        key,
        duration,
        success: true,
      });

      return {
        success: true,
        data,
        operation,
        key,
        timestamp: this.dateTimeUtils.getUtcNow(),
        ttl,
      };
    } catch (error) {
      this.stats.errors++;
      const duration = this.dateTimeUtils.getTime() - startTime;

      const cacheError =
        error instanceof CacheError
          ? error
          : new CacheError('OPERATION_FAILED', {
              message: `Cache operation '${operation}' failed for key: ${key}`,
              cause: error,
            });

      this.logger.error(`Cache operation failed`, {
        operation,
        key,
        duration,
        error: cacheError.message,
        success: false,
      });

      return {
        success: false,
        error: cacheError,
        operation,
        key,
        timestamp: this.dateTimeUtils.getUtcNow(),
        ttl,
      };
    }
  }
}
