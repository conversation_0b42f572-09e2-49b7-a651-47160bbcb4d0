import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { QuestDBService } from '../questdb/questdb.service';
import {
  SymbolMasterQuestDBTable,
  SYMBOL_MASTER_TABLE,
  CREATE_SYMBOL_MASTER_TABLE_SQL,
  CREATE_SYMBOL_MASTER_INDEXES_SQL,
  SymbolMasterQueries,
} from './symbol-master.questdb';

describe('SymbolMasterQuestDBTable', () => {
  let table: SymbolMasterQuestDBTable;
  let questdbService: jest.Mocked<QuestDBService>;

  const mockQuestDBService = {
    executeQuery: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: QuestDBService,
          useValue: mockQuestDBService,
        },
      ],
    }).compile();

    questdbService = module.get(QuestDBService);
    table = new SymbolMasterQuestDBTable(questdbService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialize', () => {
    it('should create table and indexes successfully', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        data: [],
        rowCount: 0,
        executedAt: new Date().toISOString(),
        executionTimeMs: 100,
      });

      await table.initialize();

      // Should call executeQuery for table creation
      expect(mockQuestDBService.executeQuery).toHaveBeenCalledWith(CREATE_SYMBOL_MASTER_TABLE_SQL);

      // Should call executeQuery for each index
      CREATE_SYMBOL_MASTER_INDEXES_SQL.forEach((indexSql) => {
        expect(mockQuestDBService.executeQuery).toHaveBeenCalledWith(indexSql);
      });

      expect(mockQuestDBService.executeQuery).toHaveBeenCalledTimes(1 + CREATE_SYMBOL_MASTER_INDEXES_SQL.length);
    });

    it('should handle index creation errors gracefully', async () => {
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({
          data: [],
          rowCount: 0,
          executedAt: new Date().toISOString(),
          executionTimeMs: 100,
        })
        .mockRejectedValueOnce(new Error('Index already exists'))
        .mockResolvedValue({
          data: [],
          rowCount: 0,
          executedAt: new Date().toISOString(),
          executionTimeMs: 100,
        });

      // Should not throw error even if index creation fails
      await expect(table.initialize()).resolves.not.toThrow();
    });

    it('should throw error if table creation fails', async () => {
      mockQuestDBService.executeQuery.mockRejectedValue(new Error('Table creation failed'));

      await expect(table.initialize()).rejects.toThrow('Table creation failed');
    });
  });

  describe('verifyTableStructure', () => {
    it('should return table exists and column count', async () => {
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({
          data: [{ table_name: SYMBOL_MASTER_TABLE }],
          rowCount: 1,
          executedAt: new Date().toISOString(),
          executionTimeMs: 50,
        })
        .mockResolvedValueOnce({
          data: Array(16).fill({ column_name: 'test', data_type: 'STRING' }),
          rowCount: 16,
          executedAt: new Date().toISOString(),
          executionTimeMs: 30,
        });

      const result = await table.verifyTableStructure();

      expect(result.tableExists).toBe(true);
      expect(result.columnCount).toBe(16);
      expect(result.indexCount).toBe(CREATE_SYMBOL_MASTER_INDEXES_SQL.length);
      expect(result.issues).toEqual([]);
    });

    it('should return false if table does not exist', async () => {
      mockQuestDBService.executeQuery.mockResolvedValueOnce({
        data: [],
        rowCount: 0,
        executedAt: new Date().toISOString(),
        executionTimeMs: 50,
      });

      const result = await table.verifyTableStructure();

      expect(result.tableExists).toBe(false);
      expect(result.columnCount).toBe(0);
      expect(result.indexCount).toBe(0);
      expect(result.issues).toContain('Table does not exist');
    });

    it('should detect column count mismatch', async () => {
      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({
          data: [{ table_name: SYMBOL_MASTER_TABLE }],
          rowCount: 1,
          executedAt: new Date().toISOString(),
          executionTimeMs: 50,
        })
        .mockResolvedValueOnce({
          data: Array(10).fill({ column_name: 'test', data_type: 'STRING' }),
          rowCount: 10,
          executedAt: new Date().toISOString(),
          executionTimeMs: 30,
        });

      const result = await table.verifyTableStructure();

      expect(result.tableExists).toBe(true);
      expect(result.columnCount).toBe(10);
      expect(result.issues).toContain('Expected 16 columns, found 10');
    });
  });

  describe('getTableStats', () => {
    it('should return comprehensive table statistics', async () => {
      const mockCountData = {
        total_records: '1000',
        active_records: '950',
        last_update: '2024-01-01T12:00:00Z',
      };

      const mockExchangeData = [
        { exchange: 'NSE', count: '500' },
        { exchange: 'BSE', count: '450' },
      ];

      const mockSegmentData = [
        { segment: 'EQ', count: '600' },
        { segment: 'FO', count: '350' },
      ];

      mockQuestDBService.executeQuery
        .mockResolvedValueOnce({
          data: [mockCountData],
          rowCount: 1,
          executedAt: new Date().toISOString(),
          executionTimeMs: 100,
        })
        .mockResolvedValueOnce({
          data: mockExchangeData,
          rowCount: 2,
          executedAt: new Date().toISOString(),
          executionTimeMs: 80,
        })
        .mockResolvedValueOnce({
          data: mockSegmentData,
          rowCount: 2,
          executedAt: new Date().toISOString(),
          executionTimeMs: 70,
        });

      const result = await table.getTableStats();

      expect(result.totalRecords).toBe(1000);
      expect(result.activeRecords).toBe(950);
      expect(result.lastUpdate).toEqual(new Date('2024-01-01T12:00:00Z'));
      expect(result.exchangeBreakdown).toEqual({
        NSE: 500,
        BSE: 450,
      });
      expect(result.segmentBreakdown).toEqual({
        EQ: 600,
        FO: 350,
      });
    });

    it('should handle empty results gracefully', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        data: [],
        rowCount: 0,
        executedAt: new Date().toISOString(),
        executionTimeMs: 50,
      });

      const result = await table.getTableStats();

      expect(result.totalRecords).toBe(0);
      expect(result.activeRecords).toBe(0);
      expect(result.lastUpdate).toBeNull();
      expect(result.exchangeBreakdown).toEqual({});
      expect(result.segmentBreakdown).toEqual({});
    });
  });

  describe('cleanupOldData', () => {
    it('should delete old records and return count', async () => {
      mockQuestDBService.executeQuery.mockResolvedValue({
        data: [],
        rowCount: 150,
        executedAt: new Date().toISOString(),
        executionTimeMs: 200,
      });

      const result = await table.cleanupOldData(30);

      expect(result.deletedRecords).toBe(150);
      expect(result.cutoffDate).toBeInstanceOf(Date);

      // Verify the delete query was called
      expect(mockQuestDBService.executeQuery).toHaveBeenCalledWith(
        expect.stringContaining(`DELETE FROM ${SYMBOL_MASTER_TABLE}`),
      );
    });

    it('should handle cleanup errors', async () => {
      mockQuestDBService.executeQuery.mockRejectedValue(new Error('Cleanup failed'));

      await expect(table.cleanupOldData(30)).rejects.toThrow('Cleanup failed');
    });
  });
});

describe('SymbolMasterQueries', () => {
  it('should contain all required query templates', () => {
    expect(SymbolMasterQueries.UPSERT_SYMBOL).toBeDefined();
    expect(SymbolMasterQueries.GET_SYMBOLS_BY_FILTERS).toBeDefined();
    expect(SymbolMasterQueries.GET_SYMBOL_BY_TOKEN).toBeDefined();
    expect(SymbolMasterQueries.GET_ACTIVE_SYMBOLS_COUNT).toBeDefined();
    expect(SymbolMasterQueries.GET_SYMBOLS_BY_EXCHANGE).toBeDefined();
    expect(SymbolMasterQueries.GET_SYMBOLS_BY_SEGMENT).toBeDefined();
    expect(SymbolMasterQueries.SEARCH_SYMBOLS).toBeDefined();
    expect(SymbolMasterQueries.DEACTIVATE_MISSING_SYMBOLS).toBeDefined();
  });

  it('should use correct table name in queries', () => {
    Object.values(SymbolMasterQueries).forEach((query) => {
      expect(query).toContain(SYMBOL_MASTER_TABLE);
    });
  });
});
