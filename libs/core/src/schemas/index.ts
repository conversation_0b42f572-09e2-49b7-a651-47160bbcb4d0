/**
 * QuestDB Schema Exports
 *
 * Centralized exports for all QuestDB table schemas and management classes.
 * This provides a unified interface for QuestDB schema operations across
 * the PatternTrade API ecosystem.
 *
 * Features:
 * - Centralized schema management
 * - Table initialization and management
 * - Query templates and utilities
 * - Schema validation and verification
 */

// Symbol Master Schema
export {
  SYMBOL_MASTER_TABLE,
  CREATE_SYMBOL_MASTER_TABLE_SQL,
  CREATE_SYMBOL_MASTER_INDEXES_SQL,
  SymbolMasterQuestDBTable,
  SymbolMasterQueries,
} from './symbol-master.questdb';

// Schema Management Types
export interface QuestDBTableSchema {
  tableName: string;
  createTableSQL: string;
  createIndexesSQL: string[];
  queries: Record<string, string>;
}

export interface QuestDBSchemaManager {
  initialize(): Promise<void>;
  verifyTableStructure(): Promise<{
    tableExists: boolean;
    columnCount: number;
    indexCount: number;
    issues: string[];
  }>;
  getTableStats(): Promise<Record<string, unknown>>;
  cleanupOldData?(retentionDays: number): Promise<{
    deletedRecords: number;
    cutoffDate: Date;
  }>;
}

/**
 * Registry of all QuestDB schemas
 */
export const QUESTDB_SCHEMAS = {
  SYMBOL_MASTER: {
    tableName: SYMBOL_MASTER_TABLE,
    createTableSQL: CREATE_SYMBOL_MASTER_TABLE_SQL,
    createIndexesSQL: CREATE_SYMBOL_MASTER_INDEXES_SQL,
    queries: SymbolMasterQueries,
  },
} as const;

/**
 * Schema initialization utility
 */
export async function initializeAllQuestDBSchemas(questdbService: any): Promise<void> {
  const symbolMasterTable = new SymbolMasterQuestDBTable(questdbService);
  await symbolMasterTable.initialize();
}

/**
 * Schema verification utility
 */
export async function verifyAllQuestDBSchemas(questdbService: any): Promise<{
  [key: string]: {
    tableExists: boolean;
    columnCount: number;
    indexCount: number;
    issues: string[];
  };
}> {
  const results: Record<string, any> = {};
  
  const symbolMasterTable = new SymbolMasterQuestDBTable(questdbService);
  results.symbolMaster = await symbolMasterTable.verifyTableStructure();
  
  return results;
}
