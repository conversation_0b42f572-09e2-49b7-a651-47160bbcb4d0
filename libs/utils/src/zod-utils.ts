import { z } from 'zod/v4';
import type { ZodValidationDetails } from '../../common/src/errors/zod.error';
import { ZodError, ZodErrorEnum } from '../../common/src/errors/zod.error';
import { ErrorDomainEnum } from '../../common/src/errors/domain';

/**
 * Safe parse with enhanced error handling
 */
export function safeParse<T extends z.ZodTypeAny>(
  schema: T,
  data: unknown,
  options?: {
    domain?: keyof typeof ErrorDomainEnum.enum;
    context?: string;
  },
): { success: true; data: z.output<T> } | { success: false; error: ZodError } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  }

  const validationDetails = convertZodIssuesToValidationDetails(result.error.issues);
  const domain = options?.domain || 'ZOD';
  const context = options?.context ? `${options.context}: ` : '';

  const zodError = new ZodError(ZodErrorEnum.enum.VALIDATION_FAILED, ErrorDomainEnum.enum[domain], {
    message: `${context}Schema validation failed`,
    validationDetails,
    zodIssues: result.error.issues,
    cause: result.error,
  });

  return { success: false, error: zodError };
}

/**
 * Parse with error throwing
 */
export function parseOrThrow<T extends z.ZodTypeAny>(
  schema: T,
  data: unknown,
  options?: {
    domain?: keyof typeof ErrorDomainEnum.enum;
    context?: string;
  },
): z.output<T> {
  const result = safeParse(schema, data, options);

  if (!result.success) {
    throw result.error;
  }

  return result.data;
}

/**
 * Convert Zod issues to our validation details format
 */
export function convertZodIssuesToValidationDetails(issues: z.ZodIssue[]): ZodValidationDetails[] {
  return issues.map((issue) => {
    const field = issue.path.length > 0 ? issue.path.join('.') : 'root';
    const validationDetail: ZodValidationDetails = {
      field,
      value: 'received' in issue ? issue.received : undefined,
      customMessage: issue.message,
    };

    // Add type-specific information based on Zod issue types
    // Only override message if it's a generic/default message
    if (issue.code === z.ZodIssueCode.invalid_type && 'expected' in issue) {
      validationDetail.expectedType = issue.expected;
    } else if (issue.code === z.ZodIssueCode.too_small && 'minimum' in issue) {
      // Only use generic message if the issue message is the default Zod message
      if (
        issue.message === 'String must contain at least 1 character(s)' ||
        issue.message === 'Number must be greater than or equal to 0'
      ) {
        validationDetail.customMessage = `Value must be at least ${issue.minimum}`;
      }
    } else if (issue.code === z.ZodIssueCode.too_big && 'maximum' in issue) {
      // Only use generic message if the issue message is the default Zod message
      if (issue.message.includes('must contain at most') || issue.message.includes('must be less than')) {
        validationDetail.customMessage = `Value must be at most ${issue.maximum}`;
      }
    } else if (issue.code === z.ZodIssueCode.invalid_format) {
      // Only use generic message if the issue message is the default Zod message
      if (issue.message === 'Invalid') {
        validationDetail.customMessage = 'Invalid format';
      }
    }

    return validationDetail;
  });
}

/**
 * Create a validation middleware for NestJS controllers
 */
export function createZodValidationPipe<T extends z.ZodTypeAny>(
  schema: T,
  options?: {
    domain?: keyof typeof ErrorDomainEnum.enum;
    context?: string;
  },
) {
  return {
    transform: (value: unknown): z.output<T> => {
      return parseOrThrow(schema, value, options);
    },
  };
}

/**
 * Validate environment variables with enhanced error reporting
 */
export function validateEnv<T extends z.ZodTypeAny>(
  schema: T,
  env: Record<string, unknown> = process.env,
): z.output<T> {
  const result = safeParse(schema, env, {
    domain: 'ZOD',
    context: 'Environment variables validation',
  });

  if (!result.success) {
    // For environment validation, we want to be more explicit about the error
    const formattedError = result.error.getFormattedValidationErrors();
    console.error('❌ Environment validation failed:');
    console.error(formattedError);

    if (result.error.validationDetails) {
      console.error('\nDetailed validation errors:');
      result.error.validationDetails.forEach((detail, index) => {
        console.error(`${index + 1}. ${detail.field}: ${detail.customMessage || 'Validation failed'}`);
      });
    }

    process.exit(1);
  }

  return result.data;
}

/**
 * Validate API request body with domain-specific error handling
 */
export function validateApiRequest<T extends z.ZodTypeAny>(schema: T, data: unknown, apiContext?: string): z.output<T> {
  return parseOrThrow(schema, data, {
    domain: 'ZOD',
    context: apiContext || 'API request validation',
  });
}

/**
 * Validate database input/output with enhanced error context
 */
export function validateDatabaseData<T extends z.ZodTypeAny>(
  schema: T,
  data: unknown,
  operation?: 'INSERT' | 'UPDATE' | 'SELECT',
): z.output<T> {
  return parseOrThrow(schema, data, {
    domain: 'DATABASE',
    context: operation ? `Database ${operation.toLowerCase()} validation` : 'Database validation',
  });
}

/**
 * Create a type-safe configuration validator
 */
export function createConfigValidator<T extends z.ZodTypeAny>(schema: T, configName: string) {
  return (config: unknown): z.output<T> => {
    return parseOrThrow(schema, config, {
      domain: 'ZOD',
      context: `${configName} configuration validation`,
    });
  };
}

/**
 * Utility to check if an error is a ZodError
 */
export function isZodError(error: unknown): error is ZodError {
  return error instanceof ZodError;
}

/**
 * Extract validation summary from ZodError
 */
export function getValidationSummary(error: ZodError): {
  errorCount: number;
  fields: string[];
  firstError: string;
} {
  const details = error.validationDetails || [];
  return {
    errorCount: details.length,
    fields: details.map((d) => d.field),
    firstError: details[0]?.customMessage || error.message,
  };
}
