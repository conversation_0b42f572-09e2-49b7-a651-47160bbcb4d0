import { Injectable } from '@nestjs/common';

/**
 * NestJS service for error handling and type safety utilities
 */
@Injectable()
export class ErrorUtilsService {
  /**
   * Type guard to check if an unknown value is an Error instance
   * @param error - Unknown error value
   * @returns True if the value is an Error instance
   */
  isError(error: unknown): error is Error {
    return error instanceof Error;
  }

  /**
   * Safely extracts error message from unknown error value
   * @param error - Unknown error value
   * @returns Error message string
   */
  getErrorMessage(error: unknown): string {
    if (this.isError(error)) {
      return error.message;
    }

    if (typeof error === 'string') {
      return error;
    }

    if (error && typeof error === 'object' && 'message' in error) {
      return String(error.message);
    }

    return 'Unknown error occurred';
  }

  /**
   * Safely extracts error stack from unknown error value
   * @param error - Unknown error value
   * @returns Error stack string or undefined
   */
  getErrorStack(error: unknown): string | undefined {
    if (this.isError(error)) {
      return error.stack;
    }

    if (error && typeof error === 'object' && 'stack' in error) {
      return String(error.stack);
    }

    return undefined;
  }

  /**
   * Converts unknown error to Error instance
   * @param error - Unknown error value
   * @returns Error instance
   */
  toError(error: unknown): Error {
    if (this.isError(error)) {
      return error;
    }

    if (typeof error === 'string') {
      return new Error(error);
    }

    if (error && typeof error === 'object' && 'message' in error) {
      return new Error(String(error.message));
    }

    return new Error('Unknown error occurred');
  }

  /**
   * Safely checks if error has a specific property
   * @param error - Unknown error value
   * @param property - Property name to check
   * @returns True if error has the property
   */
  hasErrorProperty(error: unknown, property: string): boolean {
    return Boolean(error && typeof error === 'object' && property in error);
  }

  /**
   * Safely gets a property value from an error object
   * @param error - Unknown error value
   * @param property - Property name to get
   * @returns Property value or undefined
   */
  getErrorProperty(error: unknown, property: string): unknown {
    if (this.hasErrorProperty(error, property)) {
      return (error as Record<string, unknown>)[property];
    }
    return undefined;
  }
}
