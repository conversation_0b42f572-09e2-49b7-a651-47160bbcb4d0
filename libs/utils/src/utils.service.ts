import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class UtilsService {
  constructor(private readonly clsService: ClsService) {}

  isObjectEmpty = (objectName: object) => {
    return objectName && Object.keys(objectName).length === 0 && objectName.constructor === Object;
  };

  addHeaders(data: Record<string, unknown>) {
    const session = this.clsService.get('session');

    return {
      ...data,
      headers: {
        session,
      },
    };
  }

  getCurrentUserId() {
    return this.clsService.get('session').user?.id || 'system';
  }
}
