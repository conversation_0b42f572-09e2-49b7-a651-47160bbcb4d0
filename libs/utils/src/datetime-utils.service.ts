import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';

import { constant } from '@app/common/constants';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class DateTimeUtilsService {
  /**
   * Get new Date
   * @returns current date
   */
  getNewDate(date?: string | number | Date): Date {
    return date ? new Date(date) : new Date();
  }

  /**
   * Get current UTC timestamp in storage format
   * @returns ISO string with UTC timezone
   */
  getUtcNow(): string {
    return dayjs.utc().format(constant.UTC_TIME_PATTERN);
  }

  /**
   * Get current timestamp
   * @returns current time
   */
  getTime(): number {
    return dayjs().valueOf();
  }

  /**
   * Format date
   * @param date - Date string, Date object, or dayjs instance
   * @param format - The format pattern
   * @returns Formatted date string
   */
  formatDate(date: string | Date | dayjs.Dayjs, format = constant.UTC_TIME_PATTERN): string {
    return dayjs(date).format(format);
  }

  /**
   * Get start of day in UTC
   * @param date - Date string, Date object, or dayjs instance
   * @returns UTC formatted string for the start of the day
   */
  getStartOfDay(date: string | Date | dayjs.Dayjs): string {
    return dayjs(date).startOf('day').utc().format(constant.UTC_TIME_PATTERN);
  }

  /**
   * Get end of day in UTC
   * @param date - Date string, Date object, or dayjs instance
   * @returns UTC formatted string for the end of the day
   */
  getEndOfDay(date: string | Date | dayjs.Dayjs): string {
    return dayjs(date).endOf('day').utc().format(constant.UTC_TIME_PATTERN);
  }

  /**
   * Convert any datetime to UTC storage format
   * @param date - Date string, Date object, or dayjs instance
   * @returns UTC formatted string for database storage
   */
  toUtcStorage(date?: string | Date | dayjs.Dayjs): string {
    if (!date) {
      return this.getUtcNow();
    }
    return dayjs(date).utc().format(constant.UTC_TIME_PATTERN);
  }

  /**
   * Convert UTC database value to IST for display
   * @param utcDate - UTC date string from database
   * @returns IST formatted string for display
   */
  toIstDisplay(utcDate: string): string {
    return dayjs.utc(utcDate, constant.UTC_TIME_PATTERN).tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss IST');
  }

  /**
   * Convert UTC database value to IST dayjs instance
   * @param utcDate - UTC date string from database
   * @returns dayjs instance in IST timezone
   */
  toIstDayjs(utcDate: string): dayjs.Dayjs {
    return dayjs.utc(utcDate, constant.UTC_TIME_PATTERN).tz('Asia/Kolkata');
  }

  /**
   * Get current IST time
   * @returns dayjs instance in IST timezone
   */
  getIstNow(): dayjs.Dayjs {
    return dayjs().tz('Asia/Kolkata');
  }

  /**
   * Add hours to a UTC datetime and return in storage format
   * @param utcDateTime - UTC datetime string (optional, defaults to current UTC time)
   * @param hours - Number of hours to add
   * @returns UTC formatted string for database storage
   */
  addHours(utcDateTime?: string, hours = 0): string {
    const baseTime = utcDateTime ? dayjs.utc(utcDateTime, constant.UTC_TIME_PATTERN) : dayjs.utc();
    return baseTime.add(hours, 'hour').format(constant.UTC_TIME_PATTERN);
  }

  /**
   * Add minutes to a UTC datetime and return in storage format
   * @param utcDateTime - UTC datetime string (optional, defaults to current UTC time)
   * @param minutes - Number of minutes to add
   * @returns UTC formatted string for database storage
   */
  addMinutes(utcDateTime?: string, minutes = 0): string {
    const baseTime = utcDateTime ? dayjs.utc(utcDateTime, constant.UTC_TIME_PATTERN) : dayjs.utc();
    return baseTime.add(minutes, 'minute').format(constant.UTC_TIME_PATTERN);
  }

  /**
   * Add days to a UTC datetime and return in storage format
   * @param utcDateTime - UTC datetime string (optional, defaults to current UTC time)
   * @param days - Number of days to add
   * @returns UTC formatted string for database storage
   */
  addDays(utcDateTime?: string, days = 0): string {
    const baseTime = utcDateTime ? dayjs.utc(utcDateTime, constant.UTC_TIME_PATTERN) : dayjs.utc();
    return baseTime.add(days, 'day').format(constant.UTC_TIME_PATTERN);
  }

  // ==================== MARKET HOURS UTILITIES ====================

  /**
   * Check if current IST time is within market hours for specified segment
   * @param segment - Market segment ('EQUITY', 'COMMODITY', 'CURRENCY'). Defaults to 'EQUITY'
   * @returns boolean indicating if market is open
   */
  isMarketOpen(segment: 'EQUITY' | 'COMMODITY' | 'CURRENCY' = 'EQUITY'): boolean {
    const now = this.getIstNow();
    const marketHours = constant.MARKET_HOURS[segment];

    // Check if today is a trading day (Monday to Friday)
    const dayOfWeek = now.day();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return false; // Sunday or Saturday
    }

    // Check against market open/close times
    const marketOpen = now.set('hour', marketHours.OPEN.hour).set('minute', marketHours.OPEN.minute).set('second', 0);
    const marketClose = now
      .set('hour', marketHours.CLOSE.hour)
      .set('minute', marketHours.CLOSE.minute)
      .set('second', 0);

    return now.isAfter(marketOpen) && now.isBefore(marketClose);
  }

  /**
   * Check if a specific IST datetime is within market hours for specified segment
   * @param istDateTime - IST datetime to check
   * @param segment - Market segment ('EQUITY', 'COMMODITY', 'CURRENCY'). Defaults to 'EQUITY'
   * @returns boolean indicating if the time is within market hours
   */
  isWithinMarketHours(istDateTime: dayjs.Dayjs, segment: 'EQUITY' | 'COMMODITY' | 'CURRENCY' = 'EQUITY'): boolean {
    const marketHours = constant.MARKET_HOURS[segment];

    const dayOfWeek = istDateTime.day();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return false;
    }

    const marketOpen = istDateTime
      .set('hour', marketHours.OPEN.hour)
      .set('minute', marketHours.OPEN.minute)
      .set('second', 0);
    const marketClose = istDateTime
      .set('hour', marketHours.CLOSE.hour)
      .set('minute', marketHours.CLOSE.minute)
      .set('second', 0);

    return istDateTime.isAfter(marketOpen) && istDateTime.isBefore(marketClose);
  }

  /**
   * Get next market opening time in IST for specified segment
   * @param segment - Market segment ('EQUITY', 'COMMODITY', 'CURRENCY'). Defaults to 'EQUITY'
   * @returns dayjs instance of next market opening
   */
  getNextMarketOpen(segment: 'EQUITY' | 'COMMODITY' | 'CURRENCY' = 'EQUITY'): dayjs.Dayjs {
    let nextOpen = this.getIstNow();
    const marketHours = constant.MARKET_HOURS[segment];
    const marketOpenTime = { hour: marketHours.OPEN.hour, minute: marketHours.OPEN.minute };

    // If it's currently after market close, or it's a weekend, move to the next day
    if (
      nextOpen.hour() > marketHours.CLOSE.hour ||
      (nextOpen.hour() === marketHours.CLOSE.hour && nextOpen.minute() >= marketHours.CLOSE.minute) ||
      nextOpen.day() === 0 ||
      nextOpen.day() === 6
    ) {
      nextOpen = nextOpen.add(1, 'day');
    }

    // If the next day is a weekend, skip to Monday
    if (nextOpen.day() === 6) {
      // Saturday
      nextOpen = nextOpen.add(2, 'day');
    } else if (nextOpen.day() === 0) {
      // Sunday
      nextOpen = nextOpen.add(1, 'day');
    }

    return nextOpen
      .set('hour', marketOpenTime.hour)
      .set('minute', marketOpenTime.minute)
      .set('second', 0)
      .set('millisecond', 0);
  }

  /**
   * Get time until next market open in minutes for specified segment
   * @param segment - Market segment ('EQUITY', 'COMMODITY', 'CURRENCY'). Defaults to 'EQUITY'
   * @returns number of minutes until market opens
   */
  getTimeToMarketOpen(segment: 'EQUITY' | 'COMMODITY' | 'CURRENCY' = 'EQUITY'): number {
    if (this.isMarketOpen(segment)) {
      return 0;
    }

    const now = this.getIstNow();
    const nextOpen = this.getNextMarketOpen(segment);

    return nextOpen.diff(now, 'minute');
  }

  /**
   * Get the most recent market session's open and close times
   * @param segment - Market segment ('EQUITY', 'COMMODITY', 'CURRENCY'). Defaults to 'EQUITY'
   * @returns object with `open` and `close` dayjs instances
   */
  getLatestMarketSession(segment: 'EQUITY' | 'COMMODITY' | 'CURRENCY' = 'EQUITY'): {
    open: dayjs.Dayjs;
    close: dayjs.Dayjs;
  } {
    let sessionDate = this.getIstNow();
    const marketHours = constant.MARKET_HOURS[segment];

    // If it's before market open today, get yesterday's session
    if (
      sessionDate.hour() < marketHours.OPEN.hour ||
      (sessionDate.hour() === marketHours.OPEN.hour && sessionDate.minute() < marketHours.OPEN.minute)
    ) {
      sessionDate = sessionDate.subtract(1, 'day');
    }

    // Adjust for weekends
    if (sessionDate.day() === 0) {
      // Sunday -> Friday
      sessionDate = sessionDate.subtract(2, 'day');
    } else if (sessionDate.day() === 6) {
      // Saturday -> Friday
      sessionDate = sessionDate.subtract(1, 'day');
    }

    const open = sessionDate
      .set('hour', marketHours.OPEN.hour)
      .set('minute', marketHours.OPEN.minute)
      .set('second', 0)
      .set('millisecond', 0);
    const close = sessionDate
      .set('hour', marketHours.CLOSE.hour)
      .set('minute', marketHours.CLOSE.minute)
      .set('second', 0)
      .set('millisecond', 0);

    return { open, close };
  }

  // ==================== UTILITY HELPERS ====================

  /**
   * Format duration in human readable format
   * @param minutes - Duration in minutes
   * @returns Formatted string (e.g., "2h 30m", "45m", "1d 3h")
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}m`;
    } else if (minutes < 1440) {
      // Less than 24 hours
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    } else {
      const days = Math.floor(minutes / 1440);
      const hours = Math.floor((minutes % 1440) / 60);
      const mins = minutes % 60;
      let result = `${days}d`;
      if (hours > 0) result += ` ${hours}h`;
      if (mins > 0) result += ` ${mins}m`;
      return result;
    }
  }
}
