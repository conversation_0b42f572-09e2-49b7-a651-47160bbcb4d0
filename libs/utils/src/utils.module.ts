import { Module } from '@nestjs/common';
import { UtilsService } from './utils.service';
import { ErrorUtilsService } from './error-utils.service';
import { DateTimeUtilsService } from './datetime-utils.service';
import { DbUtilsService } from './db-utils.service';

@Module({
  providers: [UtilsService, ErrorUtilsService, DateTimeUtilsService, DbUtilsService],
  exports: [UtilsService, ErrorUtilsService, DateTimeUtilsService, DbUtilsService],
})
export class UtilsModule {}
