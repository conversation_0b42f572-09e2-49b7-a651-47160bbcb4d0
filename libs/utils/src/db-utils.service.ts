import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { DateTimeUtilsService } from './datetime-utils.service';

// Define base audit fields interface
interface BaseSchemaType {
  id?: number;
  createdAt?: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
  deletedAt?: string;
  deletedBy?: string;
}

@Injectable()
export class DbUtilsService {
  constructor(
    private readonly clsService: ClsService,
    private readonly dateTimeUtilsService: DateTimeUtilsService,
  ) {}

  setCreatedAtAndCreatedBy<T extends Partial<BaseSchemaType>>(entity: T): T {
    const createdAt = this.dateTimeUtilsService.getUtcNow();
    const userId = this.getCurrentUserId();
    entity.createdAt = createdAt;
    entity.createdBy = userId;
    entity.updatedAt = createdAt;
    entity.updatedBy = userId;
    return entity;
  }

  setUpdatedAtAndUpdatedBy<T extends Partial<BaseSchemaType>>(entity: T): T {
    const updatedAt = this.dateTimeUtilsService.getUtcNow();
    const userId = this.getCurrentUserId();
    entity.updatedAt = updatedAt;
    entity.updatedBy = userId;
    return entity;
  }

  setDeletedAtAndDeletedBy<T extends Partial<BaseSchemaType>>(entity: T): T {
    const deletedAt = this.dateTimeUtilsService.getUtcNow();
    const userId = this.getCurrentUserId();
    entity.updatedAt = deletedAt;
    entity.updatedBy = userId;
    entity.deletedAt = deletedAt;
    entity.deletedBy = userId;
    return entity;
  }

  getCurrentUserId() {
    return this.clsService.get('session').user?.id || 'system';
  }
}
