import { z } from 'zod/v4';
import { SortOrderEnum, DayOfWeekEnum } from '../constants/enums';

// ==================== COMMON VALIDATORS ====================

/**
 * UTC datetime string validator - follows cursor rule #1 (dayjs with UTC storage)
 * Validates format: YYYY-MM-DDTHH:mm:ss.SSS with proper range validation
 */
export const utcDateTimeSchema = z
  .string()
  .regex(
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}$/,
    'Must be a valid UTC datetime string in format YYYY-MM-DDTHH:mm:ss.SSS',
  )
  .refine((value) => {
    // Extract date parts for validation
    const [datePart, timePart] = value.split('T');
    const [year, month, day] = datePart.split('-').map(Number);
    const [hour, minute, second] = timePart
      .split(':')
      .map((part, i) => (i === 2 ? Number(part.split('.')[0]) : Number(part)));

    // Validate ranges
    if (year < 1000 || year > 9999) return false;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;
    if (hour < 0 || hour > 23) return false;
    if (minute < 0 || minute > 59) return false;
    if (second < 0 || second > 59) return false;

    // Basic month/day validation
    if (month === 2 && day > 29) return false;
    if ([4, 6, 9, 11].includes(month) && day > 30) return false;

    return true;
  }, 'Must be a valid UTC datetime with proper date/time ranges')
  .describe('UTC datetime string for database storage');

/**
 * Optional UTC datetime string validator
 */
export const optionalUtcDateTimeSchema = utcDateTimeSchema.optional();

/**
 * User ID string validator
 */
export const userIdSchema = z
  .string()
  .trim()
  .min(1, 'User ID cannot be empty')
  .describe('User identifier for audit trail');

/**
 * Optional user ID string validator
 */
export const optionalUserIdSchema = userIdSchema.optional();

// ==================== AUDIT FIELD SCHEMAS ====================

/**
 * Created audit fields schema - follows cursor rule #1 (dayjs UTC storage)
 */
export const createdAuditSchema = z.object({
  createdAt: utcDateTimeSchema.describe('UTC timestamp when entity was created'),
  createdBy: userIdSchema.describe('ID of user who created the entity'),
});

/**
 * Updated audit fields schema - follows cursor rule #1 (dayjs UTC storage)
 */
export const updatedAuditSchema = z.object({
  updatedAt: utcDateTimeSchema.describe('UTC timestamp when entity was last updated'),
  updatedBy: userIdSchema.describe('ID of user who last updated the entity'),
});

/**
 * Deleted audit fields schema - follows cursor rule #1 (dayjs UTC storage)
 */
export const deletedAuditSchema = z.object({
  deletedAt: optionalUtcDateTimeSchema.describe('UTC timestamp when entity was soft deleted'),
  deletedBy: optionalUserIdSchema.describe('ID of user who deleted the entity'),
});

/**
 * Complete audit fields schema combining all audit information
 * Uses Zod 4's improved object composition
 */
export const fullAuditSchema = z.object({
  ...createdAuditSchema.shape,
  ...updatedAuditSchema.shape,
  ...deletedAuditSchema.shape,
});

// ==================== BASE ENTITY SCHEMAS ====================

/**
 * Base entity schema with ID and created audit fields
 * Uses Zod 4's improved object composition
 */
export const baseEntitySchema = z.object({
  id: z.number().int().positive().describe('Unique identifier for the entity'),
  ...createdAuditSchema.shape,
});

/**
 * Base updatable entity schema with ID and full audit trail
 * Uses Zod 4's improved object composition
 */
export const baseUpdatableEntitySchema = z.object({
  ...baseEntitySchema.shape,
  ...updatedAuditSchema.shape,
  ...deletedAuditSchema.shape,
});

/**
 * Base create input schema (without ID and system-generated fields)
 */
export const baseCreateInputSchema = z.object({
  // ID, timestamps, and user IDs are system-generated
});

/**
 * Base update input schema (without ID and created fields)
 */
export const baseUpdateInputSchema = z.object({
  // ID and created fields cannot be updated
});

// ==================== COMMON FIELD SCHEMAS ====================

/**
 * Name field schema with proper validation
 */
export const nameSchema = z
  .string()
  .trim()
  .min(1, 'Name cannot be empty')
  .max(255, 'Name cannot exceed 255 characters')
  .describe('Human-readable name');

/**
 * Description field schema
 */
export const descriptionSchema = z
  .string()
  .max(1000, 'Description cannot exceed 1000 characters')
  .trim()
  .describe('Descriptive text');

/**
 * Optional description field schema
 */
export const optionalDescriptionSchema = descriptionSchema.optional();

/**
 * Email address schema
 */
export const emailSchema = z.email('Must be a valid email address').toLowerCase().describe('Email address');

/**
 * Phone number schema (Indian format)
 */
export const phoneNumberSchema = z
  .string()
  .regex(/^(\+91|91)?[6-9]\d{9}$/, 'Must be a valid Indian phone number')
  .describe('Indian phone number');

/**
 * Positive integer schema
 */
export const positiveIntSchema = z.number().int().positive('Must be a positive integer');

/**
 * Non-negative integer schema
 */
export const nonNegativeIntSchema = z.number().int().min(0, 'Must be non-negative');

/**
 * Positive decimal schema
 */
export const positiveDecimalSchema = z.number().positive('Must be a positive number');

/**
 * Non-negative decimal schema
 */
export const nonNegativeDecimalSchema = z.number().min(0, 'Must be non-negative');

// ==================== RESPONSE SCHEMAS ====================

/**
 * Standard API success response schema
 */
export const successResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().describe('Success message'),
  timestamp: utcDateTimeSchema.describe('Response timestamp in UTC'),
});

/**
 * Standard API error response schema
 */
export const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string().describe('Error message'),
  timestamp: utcDateTimeSchema.describe('Error timestamp in UTC'),
  statusCode: z.number().int().describe('HTTP status code'),
});

/**
 * Paginated response metadata schema
 */
export const paginationMetaSchema = z.object({
  page: positiveIntSchema.describe('Current page number'),
  limit: positiveIntSchema.describe('Items per page'),
  total: nonNegativeIntSchema.describe('Total number of items'),
  totalPages: positiveIntSchema.describe('Total number of pages'),
  hasNext: z.boolean().describe('Whether there are more pages'),
  hasPrev: z.boolean().describe('Whether there are previous pages'),
});

/**
 * Generic paginated response schema
 */
export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  successResponseSchema.extend({
    data: z.array(itemSchema).describe('Array of items'),
    meta: paginationMetaSchema.describe('Pagination metadata'),
  });

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types from Zod schemas using z.output

export type UtcDateTime = z.output<typeof utcDateTimeSchema>;
export type UserId = z.output<typeof userIdSchema>;
export type CreatedAudit = z.output<typeof createdAuditSchema>;
export type UpdatedAudit = z.output<typeof updatedAuditSchema>;
export type DeletedAudit = z.output<typeof deletedAuditSchema>;
export type FullAudit = z.output<typeof fullAuditSchema>;
export type BaseEntity = z.output<typeof baseEntitySchema>;
export type BaseUpdatableEntity = z.output<typeof baseUpdatableEntitySchema>;
export type BaseCreateInput = z.output<typeof baseCreateInputSchema>;
export type BaseUpdateInput = z.output<typeof baseUpdateInputSchema>;
export type Name = z.output<typeof nameSchema>;
export type Description = z.output<typeof descriptionSchema>;
export type Email = z.output<typeof emailSchema>;
export type PhoneNumber = z.output<typeof phoneNumberSchema>;
export type SuccessResponse = z.output<typeof successResponseSchema>;
export type ErrorResponse = z.output<typeof errorResponseSchema>;
export type PaginationMeta = z.output<typeof paginationMetaSchema>;

// ==================== UTILITY HELPERS ====================

/**
 * Create a schema with full audit trail
 * Uses Zod 4's improved object composition
 */
export const withFullAudit = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) =>
  z.object({
    ...schema.shape,
    ...fullAuditSchema.shape,
  });

/**
 * Create a schema with base entity fields (ID + created audit)
 * Uses Zod 4's improved object composition
 */
export const withBaseEntity = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) =>
  z.object({
    ...schema.shape,
    ...baseEntitySchema.shape,
  });

/**
 * Create a schema with updatable entity fields (ID + full audit)
 * Uses Zod 4's improved object composition
 */
export const withUpdatableEntity = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) =>
  z.object({
    ...schema.shape,
    ...baseUpdatableEntitySchema.shape,
  });

/**
 * Create an input schema for creation (removes system fields)
 * Use this helper only with schemas that extend baseUpdatableEntitySchema
 */
export const createInputSchema = <T extends z.ZodObject<z.ZodRawShape>>(schema: T) => {
  // Extract only the custom fields by omitting base entity fields
  const entityFields = ['id', 'createdAt', 'createdBy', 'updatedAt', 'updatedBy', 'deletedAt', 'deletedBy'] as const;

  // Create a new schema with only the custom fields
  const customFields = Object.keys(schema.shape).reduce(
    (acc, key) => {
      if (!entityFields.includes(key as (typeof entityFields)[number])) {
        const field = schema.shape[key as keyof typeof schema.shape];
        if (field) {
          acc[key] = field as z.ZodTypeAny;
        }
      }
      return acc;
    },
    {} as Record<string, z.ZodTypeAny>,
  );

  return z.object(customFields).strict();
};

/**
 * Create an input schema for updates (removes immutable fields)
 * Use this helper only with schemas that extend baseUpdatableEntitySchema
 */
export const updateInputSchema = <T extends z.ZodObject<z.ZodRawShape>>(schema: T) => {
  // Extract fields that can be updated (exclude id, createdAt, createdBy)
  const immutableFields = ['id', 'createdAt', 'createdBy'] as const;

  // Create a new schema with updatable fields made optional
  const updatableFields = Object.keys(schema.shape).reduce(
    (acc, key) => {
      if (!immutableFields.includes(key as (typeof immutableFields)[number])) {
        const field = schema.shape[key as keyof typeof schema.shape];
        if (field && 'optional' in field && typeof field.optional === 'function') {
          acc[key] = field.optional();
        }
      }
      return acc;
    },
    {} as Record<string, z.ZodTypeAny>,
  );

  return z.object(updatableFields).strict();
};

/**
 * Create a schema with conditional validation based on other fields
 * Uses Zod 4's improved validation patterns
 */
export const createConditionalSchema = <T extends z.ZodRawShape>(
  baseSchema: z.ZodObject<T>,
  conditions: Array<{
    when: (data: z.output<z.ZodObject<T>>) => boolean;
    then: z.ZodObject<z.ZodRawShape>;
    message?: string;
  }>,
) => {
  return baseSchema.refine(
    (data) => {
      for (const condition of conditions) {
        if (condition.when(data)) {
          const result = condition.then.safeParse(data);
          if (!result.success) {
            return false;
          }
        }
      }
      return true;
    },
    {
      message: 'Conditional validation failed',
    },
  );
};

/**
 * Create a schema that validates array uniqueness by a specific field
 * Uses Zod 4's improved validation patterns
 */
export const createUniqueArraySchema = <T extends z.ZodTypeAny>(
  itemSchema: T,
  uniqueBy: keyof z.output<T> | ((item: z.output<T>) => unknown),
  message?: string,
) => {
  return z.array(itemSchema).refine(
    (items) => {
      const seen = new Set();
      const getValue = typeof uniqueBy === 'function' ? uniqueBy : (item: z.output<T>) => item[uniqueBy];

      for (const item of items) {
        const value = getValue(item);
        if (seen.has(value)) {
          return false;
        }
        seen.add(value);
      }
      return true;
    },
    {
      message: message || 'Array must contain unique items',
    },
  );
};

/**
 * Create a search/filter schema with common pagination and sorting
 */
export const createSearchSchema = <T extends z.ZodRawShape>(
  filterSchema: z.ZodObject<T> = z.object({}) as z.ZodObject<T>,
  sortableFields: readonly string[] = [],
) => {
  return z.object({
    // Pagination
    page: positiveIntSchema.default(1).describe('Page number (1-based)'),
    limit: z
      .number()
      .int()
      .min(1, 'Limit must be at least 1')
      .max(100, 'Limit cannot exceed 100')
      .default(20)
      .describe('Items per page (max 100)'),

    // Search
    search: z.string().optional().describe('Search query'),

    // Sorting
    sortBy:
      sortableFields.length > 0
        ? z
            .enum(sortableFields as [string, ...string[]])
            .optional()
            .describe('Field to sort by')
        : z.string().optional().describe('Field to sort by'),
    sortOrder: SortOrderEnum.default('asc').describe('Sort direction'),

    // Filters
    ...filterSchema.shape,
  });
};

/**
 * Create a bulk operation schema for arrays with validation
 */
export const createBulkOperationSchema = <T extends z.ZodTypeAny>(
  itemSchema: T,
  options: {
    maxItems?: number;
    minItems?: number;
    operation: 'create' | 'update' | 'delete';
  },
) => {
  const { maxItems = 100, minItems = 1, operation } = options;

  const baseArraySchema = z
    .array(itemSchema)
    .min(minItems, `Must have at least ${minItems} item(s) for ${operation}`)
    .max(maxItems, `Cannot ${operation} more than ${maxItems} items at once`);

  return z.object({
    operation: z.literal(operation).describe(`Bulk ${operation} operation`),
    items: baseArraySchema.describe(`Array of items to ${operation}`),
    validateOnly: z.boolean().default(false).describe('Only validate without executing'),
    continueOnError: z.boolean().default(false).describe('Continue processing if individual items fail'),
  });
};

/**
 * Create a schema for API responses with optional data
 */
export const createApiResponseSchema = <T extends z.ZodTypeAny>(
  dataSchema: T,
  options: {
    includeMetadata?: boolean;
    includePagination?: boolean;
  } = {},
) => {
  const baseResponse = successResponseSchema.extend({
    data: dataSchema.describe('Response data'),
  });

  if (options.includePagination) {
    return baseResponse.extend({
      meta: paginationMetaSchema.describe('Pagination metadata'),
    });
  }

  if (options.includeMetadata) {
    return baseResponse.extend({
      meta: z.record(z.string(), z.unknown()).optional().describe('Additional metadata'),
    });
  }

  return baseResponse;
};

/**
 * Create a schema with default values that respect environment
 * Uses Zod 4's improved type inference
 */
export const createEnvironmentAwareSchema = <T extends z.ZodRawShape>(
  schema: z.ZodObject<T>,
  environmentDefaults: {
    development?: Partial<z.output<z.ZodObject<T>>>;
    staging?: Partial<z.output<z.ZodObject<T>>>;
    production?: Partial<z.output<z.ZodObject<T>>>;
  },
) => {
  // Make all fields optional to allow defaults to be applied
  const partialSchema = schema.partial();

  return partialSchema.transform((data) => {
    const env = (process.env['NODE_ENV'] as 'development' | 'staging' | 'production') || 'development';
    const defaults = environmentDefaults[env] || {};

    const result = {
      ...defaults,
      ...data,
    };

    return result;
  });
};

/**
 * Create a schema that validates business hours
 */
export const createBusinessHoursSchema = (timezone: string = 'Asia/Kolkata') => {
  return z
    .object({
      startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
      endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
      timezone: z.string().default(timezone),
      daysOfWeek: z.array(DayOfWeekEnum).min(1, 'At least one day must be selected').describe('Operating days'),
    })
    .refine((data) => {
      // Validate that end time is after start time
      const [startHour, startMinute] = data.startTime.split(':').map(Number);
      const [endHour, endMinute] = data.endTime.split(':').map(Number);

      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;

      return endMinutes > startMinutes;
    }, 'End time must be after start time');
};

/**
 * Create a schema that validates financial amounts with currency
 */
export const createFinancialAmountSchema = (
  options: {
    allowNegative?: boolean;
    maxAmount?: number;
    requiredCurrency?: string[];
  } = {},
) => {
  const { allowNegative = false, maxAmount, requiredCurrency } = options;

  return z.object({
    amount: allowNegative
      ? z.number().max(maxAmount || Number.MAX_SAFE_INTEGER, 'Amount exceeds maximum allowed value')
      : positiveDecimalSchema.max(maxAmount || Number.MAX_SAFE_INTEGER, 'Amount exceeds maximum allowed value'),
    currency: requiredCurrency
      ? z.enum(requiredCurrency as [string, ...string[]])
      : z.string().length(3, 'Currency must be a 3-letter code').toUpperCase(),
  });
};

/**
 * Create a partial schema where all fields are optional
 */
export const createPartialSchema = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) => {
  return schema.partial();
};

/**
 * Create a schema that validates array with minimum/maximum constraints
 * Uses Zod 4's improved type inference
 */
export const createConstrainedArraySchema = <T extends z.ZodTypeAny>(
  itemSchema: T,
  constraints: {
    min?: number;
    max?: number;
    unique?: boolean;
    sortBy?: keyof z.output<T>;
  },
) => {
  let arraySchema = z.array(itemSchema);

  if (constraints.min !== undefined) {
    arraySchema = arraySchema.min(constraints.min, `Array must have at least ${constraints.min} items`);
  }

  if (constraints.max !== undefined) {
    arraySchema = arraySchema.max(constraints.max, `Array cannot have more than ${constraints.max} items`);
  }

  if (constraints.unique || constraints.sortBy) {
    arraySchema = arraySchema.refine((items) => {
      if (constraints.unique) {
        const seen = new Set(items);
        return seen.size === items.length;
      }
      return true;
    }, 'Array must contain unique items');
  }

  return arraySchema;
};
