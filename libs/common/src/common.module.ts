import { Modu<PERSON> } from '@nestjs/common';
import { CoreModule } from '@app/core';
import { SecurityService } from './security/security.service';
import { IPWhitelistService } from './security/ip-whitelist.service';
import { AuditLoggingService } from './security/audit-logging.service';
import { IPWhitelistGuard } from './security/ip-whitelist.guard';
import { EncryptionModule } from './encryption/encryption.module';

@Module({
  imports: [CoreModule, EncryptionModule],
  providers: [SecurityService, IPWhitelistService, AuditLoggingService, IPWhitelistGuard],
  exports: [SecurityService, IPWhitelistService, AuditLoggingService, IPWhitelistGuard, EncryptionModule],
})
export class CommonModule {}
