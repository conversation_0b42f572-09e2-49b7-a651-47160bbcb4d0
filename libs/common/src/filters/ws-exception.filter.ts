import { Catch, ArgumentsHost, Logger } from '@nestjs/common';
import { BaseWsExceptionFilter } from '@nestjs/websockets';
import { AllExceptionsFilter } from './exceptions.filter';

@Catch()
export class WsExceptionFilter extends BaseWsExceptionFilter {
  private logger = new Logger(WsExceptionFilter.name);
  constructor(private readonly allExceptionsFilter: AllExceptionsFilter) {
    super();
  }
  override catch(exception: unknown, host: ArgumentsHost) {
    this.logger.error(exception);
    this.allExceptionsFilter.catch(exception, host);
  }
}
