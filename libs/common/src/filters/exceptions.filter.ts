import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import { constant } from '../constants/constant';
import { BadRequestError, NotFoundError, ZodError } from '../errors';
import { AuthError } from '@app/auth';
import { UserError } from '@app/user';
import { BrokerError } from '@app/broker';
import { EncryptionError } from '../encryption/encryption.error';
import { DateTimeUtilsService } from '@app/utils';

// Configure dayjs plugins
dayjs.extend(utc);
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private logger = new Logger(AllExceptionsFilter.name);
  constructor(private readonly dateTimeUtils: DateTimeUtilsService) {}
  catch(exception: unknown, host: ArgumentsHost) {
    this.logger.error(exception);

    const contextType = host.getType();

    if (contextType === 'http') {
      this.handleHttpException(exception, host);
    } else if (contextType === 'rpc') {
      this.handleRpcException(exception, host);
    }
  }

  private handleHttpException(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    let status;
    let error;
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      error = JSON.stringify(exception.getResponse());
    } else if (exception instanceof Error) {
      const result = this.errorMapper(exception);
      status = result.status;
      error = result.error;
    }

    response.status(status).json({
      statusCode: status,
      path: ctx.getRequest().url,
      timestamp: dayjs.utc().format(constant.UTC_TIME_PATTERN),
      error,
    });
  }

  private handleRpcException(exception: unknown, _host: ArgumentsHost) {
    let error;
    if (exception instanceof RpcException) {
      error = exception.getError();
    } else if (exception instanceof Error) {
      error = this.errorMapper(exception).error;
    }

    return { status: 'error', timestamp: dayjs.utc().format(constant.UTC_TIME_PATTERN), error };
  }

  private errorMapper(error: Error): { status: number; error: string | object } {
    if (error instanceof AuthError) {
      return { status: HttpStatus.UNAUTHORIZED, error: error.message };
    } else if (error instanceof UserError) {
      // Map UserError to appropriate HTTP status codes
      return this.mapUserErrorToHttpStatus(error);
    } else if (error instanceof BrokerError) {
      // Map BrokerError to appropriate HTTP status codes
      return this.mapBrokerErrorToHttpStatus(error);
    } else if (error instanceof EncryptionError) {
      // Map EncryptionError to appropriate HTTP status codes
      return this.mapEncryptionErrorToHttpStatus(error);
    } else if (error instanceof ZodError) {
      // Use enhanced ZodError formatting
      return {
        status: HttpStatus.BAD_REQUEST,
        error: error.toApiResponse(this.dateTimeUtils),
      };
    } else if (error instanceof BadRequestError) {
      return { status: HttpStatus.BAD_REQUEST, error: error.message };
    } else if (error instanceof NotFoundError) {
      return { status: HttpStatus.NOT_FOUND, error: error.message };
    } else {
      return { status: HttpStatus.INTERNAL_SERVER_ERROR, error: error.message };
    }
  }

  /**
   * Map UserError types to appropriate HTTP status codes
   */
  private mapUserErrorToHttpStatus(error: UserError): { status: number; error: string | object } {
    const errorResponse = {
      error: error.name,
      message: error.message,
      domain: error.domain,
      timestamp: dayjs.utc().format(constant.UTC_TIME_PATTERN),
    };

    switch (error.name) {
      case 'USER_NOT_FOUND':
        return { status: HttpStatus.NOT_FOUND, error: errorResponse };

      case 'USER_ALREADY_EXISTS':
      case 'EMAIL_ALREADY_EXISTS':
        return { status: HttpStatus.CONFLICT, error: errorResponse };

      case 'USER_INACTIVE':
        return { status: HttpStatus.FORBIDDEN, error: errorResponse };

      case 'INVALID_USER_DATA':
      case 'INVALID_PASSWORD':
      case 'INVALID_USER_ROLE':
        return { status: HttpStatus.BAD_REQUEST, error: errorResponse };

      case 'USER_CREATION_FAILED':
      case 'USER_UPDATE_FAILED':
      case 'USER_DELETION_FAILED':
      case 'PASSWORD_UPDATE_FAILED':
      default:
        return { status: HttpStatus.INTERNAL_SERVER_ERROR, error: errorResponse };
    }
  }

  /**
   * Map BrokerError types to appropriate HTTP status codes
   */
  private mapBrokerErrorToHttpStatus(error: BrokerError): { status: number; error: string | object } {
    // Use the error's toApiResponse method for consistent formatting
    const errorResponse = error.toApiResponse();

    switch (error.name) {
      // Authentication and authorization errors - 401/403
      case 'AUTHENTICATION_FAILED':
      case 'INVALID_CREDENTIALS':
      case 'SESSION_EXPIRED':
      case 'TOKEN_EXPIRED':
      case 'OAUTH_FAILED':
        return { status: HttpStatus.UNAUTHORIZED, error: errorResponse };

      case 'INSUFFICIENT_PERMISSIONS':
        return { status: HttpStatus.FORBIDDEN, error: errorResponse };

      // Not found errors - 404
      case 'ACCOUNT_NOT_FOUND':
      case 'SYMBOL_NOT_FOUND':
        return { status: HttpStatus.NOT_FOUND, error: errorResponse };

      // Conflict errors - 409
      case 'DUPLICATE_ACCOUNT':
        return { status: HttpStatus.CONFLICT, error: errorResponse };

      // Bad request errors - 400
      case 'INVALID_BROKER_RESPONSE':
      case 'INVALID_SYMBOL_FORMAT':
      case 'INVALID_OAUTH_STATE':
      case 'INVALID_ENCRYPTED_DATA':
      case 'UNSUPPORTED_OPERATION':
        return { status: HttpStatus.BAD_REQUEST, error: errorResponse };

      // Rate limiting - 429
      case 'API_RATE_LIMIT_EXCEEDED':
        return { status: HttpStatus.TOO_MANY_REQUESTS, error: errorResponse };

      // Service unavailable - 503
      case 'BROKER_SERVICE_UNAVAILABLE':
      case 'BROKER_MAINTENANCE':
      case 'CIRCUIT_BREAKER_OPEN':
        return { status: HttpStatus.SERVICE_UNAVAILABLE, error: errorResponse };

      // Request timeout - 408
      case 'TIMEOUT_ERROR':
      case 'OPERATION_TIMEOUT':
        return { status: HttpStatus.REQUEST_TIMEOUT, error: errorResponse };

      // Internal server errors - 500
      case 'ENCRYPTION_FAILED':
      case 'DECRYPTION_FAILED':
      case 'KEY_ROTATION_FAILED':
      case 'DATABASE_ERROR':
      case 'SYSTEM_ERROR':
      case 'ACCOUNT_CREATION_FAILED':
      case 'ACCOUNT_UPDATE_FAILED':
      case 'ACCOUNT_DELETION_FAILED':
      case 'TOKEN_REFRESH_FAILED':
      case 'OAUTH_CALLBACK_FAILED':
      case 'MAX_RETRIES_EXCEEDED':
      case 'RETRY_EXHAUSTED':
      default:
        return { status: HttpStatus.INTERNAL_SERVER_ERROR, error: errorResponse };

      // Connection errors - 502 (Bad Gateway)
      case 'CONNECTION_FAILED':
      case 'NETWORK_ERROR':
      case 'WEBSOCKET_CONNECTION_FAILED':
      case 'WEBSOCKET_DISCONNECTED':
      case 'SUBSCRIPTION_FAILED':
      case 'UNSUBSCRIPTION_FAILED':
      case 'UNKNOWN_BROKER_ERROR':
        return { status: HttpStatus.BAD_GATEWAY, error: errorResponse };
    }
  }

  /**
   * Map EncryptionError types to appropriate HTTP status codes
   */
  private mapEncryptionErrorToHttpStatus(error: EncryptionError): { status: number; error: string | object } {
    // Use the error's toApiResponse method for consistent formatting
    const errorResponse = error.toApiResponse(this.dateTimeUtils);

    switch (error.name) {
      // Bad request errors - 400
      case 'INVALID_ENCRYPTED_DATA':
        return { status: HttpStatus.BAD_REQUEST, error: errorResponse };

      // Internal server errors - 500
      case 'ENCRYPTION_FAILED':
      case 'DECRYPTION_FAILED':
      case 'KEY_ROTATION_FAILED':
      case 'ENCRYPTION_SERVICE_INITIALIZATION_FAILED':
      default:
        return { status: HttpStatus.INTERNAL_SERVER_ERROR, error: errorResponse };
    }
  }
}
