import { z } from 'zod/v4';
import type { DateTimeUtilsService } from '@app/utils/datetime-utils.service';

/**
 * Encryption error types enum
 */
export const EncryptionErrorEnum = z.enum([
  'ENCRYPTION_FAILED',
  'DECRYPTION_FAILED',
  'KEY_ROTATION_FAILED',
  'INVALID_ENCRYPTED_DATA',
  'ENCRYPTION_SERVICE_INITIALIZATION_FAILED',
]);

export type EncryptionErrorType = z.infer<typeof EncryptionErrorEnum>;

/**
 * Generic encryption error class for the common encryption service
 *
 * This error class is used by the encryption service to provide consistent
 * error handling across the application without creating circular dependencies.
 *
 * Requirements:
 * - 4.3: Proper error handling for encryption operations
 * - 4.4: Clean separation of concerns without circular dependencies
 */
export class EncryptionError extends Error {
  public readonly name: EncryptionErrorType;
  public readonly module: string;
  public readonly context: {
    message: string;
    cause?: unknown;
    correlationId?: string;
    operation?: string;
    metadata?: Record<string, unknown>;
  };
  public readonly isRetryable: boolean;
  public readonly severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

  constructor(
    name: EncryptionErrorType,
    module: string,
    context: {
      message: string;
      cause?: unknown;
      correlationId?: string;
      operation?: string;
      metadata?: Record<string, unknown>;
    },
    isRetryable: boolean = false,
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'HIGH',
  ) {
    super(context.message);
    this.name = name;
    this.module = module;
    this.context = context;
    this.isRetryable = isRetryable;
    this.severity = severity;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, EncryptionError);
    }
  }

  /**
   * Convert error to API response format
   */
  toApiResponse(dateTimeUtils: DateTimeUtilsService) {
    return {
      error: this.name,
      message: this.context.message,
      correlationId: this.context.correlationId,
      timestamp: dateTimeUtils.getUtcNow(),
      retryable: this.isRetryable,
      severity: this.severity,
    };
  }

  /**
   * Convert error to log format (sanitized)
   */
  toLogFormat() {
    return {
      name: this.name,
      module: this.module,
      message: this.context.message,
      correlationId: this.context.correlationId,
      operation: this.context.operation,
      isRetryable: this.isRetryable,
      severity: this.severity,
      metadata: this.context.metadata,
      // Don't log the actual cause as it might contain sensitive data
      hasCause: !!this.context.cause,
    };
  }
}
