/**
 * Encrypted object structure for storing encrypted data with metadata
 */
export interface EncryptedObject {
  data: string;
  iv: string;
  authTag: string;
  algorithm: string;
  keyVersion: string;
}

/**
 * Interface for encryption service operations
 * Defines the contract for encryption/decryption functionality
 */
export interface IEncryptionService {
  /**
   * Encrypt a string using AES-256-GCM
   * @param data - Plain text data to encrypt
   * @returns Base64 encoded encrypted data with metadata
   */
  encrypt(data: string): string;

  /**
   * Decrypt a string using AES-256-GCM
   * @param encryptedData - Base64 encoded encrypted data with metadata
   * @returns Decrypted plain text data
   */
  decrypt(encryptedData: string): string;

  /**
   * Encrypt an object by converting it to JSON first
   * @param obj - Object to encrypt
   * @returns Encrypted object with metadata
   */
  encryptObject<T>(obj: T): EncryptedObject;

  /**
   * Decrypt an object by decrypting and parsing JSON
   * @param encryptedObj - Encrypted object with metadata
   * @returns Decrypted object
   */
  decryptObject<T>(encryptedObj: EncryptedObject): T;

  /**
   * Validate encryption by encrypting and decrypting test data
   * @param data - Test data to validate
   * @param encrypted - Encrypted version to validate against
   * @returns True if validation passes
   */
  validateEncryption(data: string, encrypted: string): boolean;

  /**
   * Rotate encryption keys
   * @returns void
   */
  rotateKeys(): void;
}
