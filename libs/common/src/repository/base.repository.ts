import { Injectable, Logger } from '@nestjs/common';
import { eq, and, desc, asc, count, type SQL } from 'drizzle-orm';
import type { PgTable } from 'drizzle-orm/pg-core';
import { DbUtilsService, UtilsService } from '@app/utils';
import { IBaseRepository, IPaginatedRepository, PaginationOptions, PaginatedResult } from './base-repository.interface';
import { RepositoryError, RepositoryErrorCodeEnum, EntityNotFoundError, RepositoryContext } from './repository.types';
import { DrizzleService } from '@app/core/drizzle';

/**
 * Base repository class providing common CRUD operations and error handling
 *
 * This abstract class provides:
 * - Standard CRUD operations (create, read, update, delete)
 * - Consistent error handling across all repositories
 * - Audit field management (createdAt, updatedAt, etc.)
 * - Transaction support
 * - Pagination utilities
 * - Query building helpers
 */
@Injectable()
export abstract class BaseRepository<T, CreateData, UpdateData, ID = number>
  implements IBaseRepository<T, CreateData, UpdateData, ID>, IPaginatedRepository<T>
{
  protected readonly logger: Logger;

  constructor(
    protected readonly drizzleService: DrizzleService,
    protected readonly utilsService: UtilsService,
    protected readonly dbUtils: DbUtilsService,
    loggerContext: string,
  ) {
    this.logger = new Logger(loggerContext);
  }

  // ==================== ABSTRACT METHODS ====================

  /**
   * Get the database table/schema for this repository
   * Must be implemented by concrete repositories
   */
  protected abstract getTable(): PgTable;

  /**
   * Get the table name for logging purposes
   * Must be implemented by concrete repositories
   */
  protected abstract getTableName(): string;

  // ==================== CRUD OPERATIONS ====================

  /**
   * Create a new entity
   * @param data - Entity creation data
   * @returns Promise<T> - Created entity
   */
  async create(data: CreateData): Promise<T> {
    return this.executeWithErrorHandling('create', async () => {
      this.logOperation('create', { data });

      const auditedData = this.addCreateAuditFields(data as Record<string, unknown>);
      const table = this.getTable();

      const [created] = await this.getDb().insert(table).values(auditedData).returning();

      this.logger.log(`Created ${this.getTableName()} with ID: ${String((created as Record<string, unknown>).id)}`);
      return created as T;
    });
  }

  /**
   * Find entity by ID
   * @param id - Entity ID
   * @returns Promise<T | null> - Found entity or null
   */
  async findById(id: ID): Promise<T | null> {
    return this.executeWithErrorHandling('findById', async () => {
      this.logOperation('findById', { id });

      const table = this.getTable();
      const results = await this.getDb()
        .select()
        .from(table)
        .where(eq((table as unknown as Record<string, never>).id, id))
        .limit(1);

      return (results[0] as T) || null;
    });
  }

  /**
   * Update entity by ID
   * @param id - Entity ID
   * @param updates - Partial entity data for updates
   * @returns Promise<T> - Updated entity
   */
  async update(id: ID, updates: UpdateData): Promise<T> {
    return this.executeWithErrorHandling('update', async () => {
      this.logOperation('update', { id, updates });

      // Validate entity exists
      await this.validateEntityExists(id);

      const auditedUpdates = this.addUpdateAuditFields(updates as Record<string, unknown>);
      const table = this.getTable();

      const [updated] = await this.getDb()
        .update(table)
        .set(auditedUpdates)
        .where(eq((table as unknown as Record<string, never>).id, id))
        .returning();

      this.logger.log(`Updated ${this.getTableName()} with ID: ${String(id)}`);
      return updated as T;
    });
  }

  /**
   * Delete entity by ID
   * @param id - Entity ID
   * @returns Promise<void>
   */
  async delete(id: ID): Promise<void> {
    return this.executeWithErrorHandling('delete', async () => {
      this.logOperation('delete', { id });

      // Validate entity exists
      await this.validateEntityExists(id);

      const table = this.getTable();
      await this.getDb()
        .delete(table)
        .where(eq((table as unknown as Record<string, never>).id, id));

      this.logger.log(`Deleted ${this.getTableName()} with ID: ${String(id)}`);
    });
  }

  /**
   * Find all entities with optional filtering
   * @param filters - Optional filter criteria
   * @returns Promise<T[]> - Array of entities
   */
  async findAll(filters?: Record<string, unknown>): Promise<T[]> {
    return this.executeWithErrorHandling('findAll', async () => {
      this.logOperation('findAll', { filters });

      const table = this.getTable();

      if (filters && Object.keys(filters).length > 0) {
        // Build WHERE conditions manually for better type safety
        const conditions: SQL[] = [];
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null && (table as unknown as Record<string, never>)[key]) {
            conditions.push(eq((table as unknown as Record<string, never>)[key], value));
          }
        }

        if (conditions.length > 0) {
          const whereClause = conditions.length === 1 ? conditions[0] : and(...conditions);
          const results = await this.getDb()
            .select()
            .from(table)
            .where(whereClause)
            .orderBy(desc((table as unknown as Record<string, never>).createdAt));
          return results as T[];
        }
      }

      const results = await this.getDb()
        .select()
        .from(table)
        .orderBy(desc((table as unknown as Record<string, never>).createdAt));
      return results as T[];
    });
  }

  /**
   * Find entities with pagination
   * @param options - Pagination options
   * @returns Promise<PaginatedResult<T>> - Paginated results
   */
  async findPaginated(options: PaginationOptions): Promise<PaginatedResult<T>> {
    return this.executeWithErrorHandling('findPaginated', async () => {
      const { limit = 10, offset = 0, sortBy = 'createdAt', sortOrder = 'DESC', filters } = options;

      this.logOperation('findPaginated', { limit, offset, sortBy, sortOrder, filters });

      const table = this.getTable();

      // Build WHERE conditions
      let whereClause: SQL | undefined;
      if (filters && Object.keys(filters).length > 0) {
        const conditions: SQL[] = [];
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null && (table as unknown as Record<string, never>)[key]) {
            conditions.push(eq((table as unknown as Record<string, never>)[key], value));
          }
        }
        if (conditions.length > 0) {
          whereClause = conditions.length === 1 ? conditions[0] : and(...conditions);
        }
      }

      // Build ORDER BY clause
      const sortColumn =
        (table as unknown as Record<string, never>)[sortBy] || (table as unknown as Record<string, never>).createdAt;
      const orderByClause = sortOrder === 'ASC' ? asc(sortColumn) : desc(sortColumn);

      // Execute data query
      let data;
      if (whereClause) {
        data = await this.getDb()
          .select()
          .from(table)
          .where(whereClause)
          .orderBy(orderByClause)
          .limit(limit)
          .offset(offset);
      } else {
        data = await this.getDb().select().from(table).orderBy(orderByClause).limit(limit).offset(offset);
      }

      // Execute count query
      let totalResult;
      if (whereClause) {
        totalResult = await this.getDb().select({ count: count() }).from(table).where(whereClause);
      } else {
        totalResult = await this.getDb().select({ count: count() }).from(table);
      }

      const total = totalResult[0]?.count || 0;
      const hasMore = offset + limit < total;

      return {
        data: data as T[],
        total,
        hasMore,
        limit,
        offset,
      };
    });
  }

  // ==================== HELPER METHODS ====================

  /**
   * Add audit fields for creation
   * @param data - Data object to add audit fields to
   * @returns Data with audit fields added
   */
  protected addCreateAuditFields<D extends Record<string, unknown>>(data: D): D {
    return this.dbUtils.setCreatedAtAndCreatedBy(data);
  }

  /**
   * Add audit fields for updates
   * @param data - Data object to add audit fields to
   * @returns Data with audit fields added
   */
  protected addUpdateAuditFields<D extends Record<string, unknown>>(data: D): D {
    return this.dbUtils.setUpdatedAtAndUpdatedBy(data);
  }

  /**
   * Build WHERE conditions from filter object
   * @param filters - Filter criteria
   * @returns SQL WHERE conditions
   */
  protected buildWhereConditions(filters: Record<string, unknown>): SQL | undefined {
    const table = this.getTable();
    const conditions: SQL[] = [];

    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && value !== null && (table as unknown as Record<string, never>)[key]) {
        conditions.push(eq((table as unknown as Record<string, never>)[key], value));
      }
    }

    return conditions.length > 0 ? and(...conditions) : undefined;
  }

  /**
   * Build ORDER BY clause
   * @param sortBy - Field to sort by
   * @param sortOrder - Sort order
   * @returns SQL ORDER BY clause
   */
  protected buildOrderByClause(sortBy: string, sortOrder: 'ASC' | 'DESC'): SQL | undefined {
    const table = this.getTable();

    if (!(table as unknown as Record<string, never>)[sortBy]) {
      return undefined;
    }

    return sortOrder === 'ASC'
      ? asc((table as unknown as Record<string, never>)[sortBy])
      : desc((table as unknown as Record<string, never>)[sortBy]);
  }

  /**
   * Execute operation with consistent error handling
   * @param operation - Operation name
   * @param fn - Function to execute
   * @returns Promise<R> - Operation result
   */
  protected async executeWithErrorHandling<R>(
    operation: string,
    fn: () => Promise<R>,
    context?: RepositoryContext,
  ): Promise<R> {
    try {
      return await fn();
    } catch (error) {
      this.handleError(error, operation, context);
    }
  }

  /**
   * Handle repository errors consistently
   * @param error - Original error
   * @param operation - Operation that failed
   * @param context - Additional context
   * @throws RepositoryError
   */
  protected handleError(error: unknown, operation: string, context?: RepositoryContext): never {
    this.logger.error(`${operation} failed for ${this.getTableName()}:`, error);

    // If it's already a repository error, re-throw it
    if (error instanceof EntityNotFoundError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    const repositoryError: RepositoryError = {
      code: this.getErrorCode(error),
      message: `${operation} operation failed: ${errorMessage}`,
      context: {
        table: this.getTableName(),
        operation,
        originalError: errorMessage,
        ...context,
      },
    };

    throw new Error(JSON.stringify(repositoryError));
  }

  /**
   * Get error code from error object
   * @param error - Error object
   * @returns Error code
   */
  protected getErrorCode(error: unknown): string {
    if (error instanceof EntityNotFoundError) {
      return error.code;
    }

    if (error instanceof Error) {
      // Map common database errors
      if (error.message.includes('duplicate key')) {
        return RepositoryErrorCodeEnum.enum.DUPLICATE_ENTITY;
      }
      if (error.message.includes('foreign key')) {
        return RepositoryErrorCodeEnum.enum.FOREIGN_KEY_CONSTRAINT;
      }
      if (error.message.includes('not found')) {
        return RepositoryErrorCodeEnum.enum.ENTITY_NOT_FOUND;
      }
    }

    return RepositoryErrorCodeEnum.enum.UNKNOWN_ERROR;
  }

  /**
   * Log repository operation
   * @param operation - Operation name
   * @param context - Operation context
   */
  protected logOperation(operation: string, context?: Record<string, unknown>): void {
    this.logger.debug(`${operation} operation on ${this.getTableName()}`, context);
  }

  /**
   * Validate entity exists
   * @param id - Entity ID
   * @param entityName - Entity name for error messages
   * @returns Promise<void>
   * @throws EntityNotFoundError if entity doesn't exist
   */
  protected async validateEntityExists(id: ID, entityName?: string): Promise<void> {
    const entity = await this.findById(id);
    if (!entity) {
      const name = entityName || this.getTableName();
      throw new EntityNotFoundError(name, String(id));
    }
  }

  /**
   * Get database connection
   * @returns Database connection
   */
  protected getDb() {
    return this.drizzleService.getDb();
  }
}
