/**
 * Common repository types and error definitions for PatternTrade API
 */

/**
 * Repository error interface
 */
export interface RepositoryError {
  /** Error code for categorization */
  code: string;
  /** Human-readable error message */
  message: string;
  /** Additional context information */
  context?: Record<string, unknown>;
}

/**
 * Repository error codes enum
 */
import { z } from 'zod';

export const RepositoryErrorCodeEnum = z.enum([
  // Generic errors
  'UNKNOWN_ERROR',
  'VALIDATION_ERROR',

  // Database errors
  'CONNECTION_ERROR',
  'QUERY_ERROR',
  'TRANSACTION_ERROR',

  // Entity errors
  'ENTITY_NOT_FOUND',
  'DUPLICATE_ENTITY',
  'ENTITY_VALIDATION_FAILED',

  // Constraint errors
  'FOREIGN_KEY_CONSTRAINT',
  'UNIQUE_CONSTRAINT',
  'CHECK_CONSTRAINT',

  // Permission errors
  'ACCESS_DENIED',
  'INSUFFICIENT_PERMISSIONS',
]);

export type RepositoryErrorCode = z.infer<typeof RepositoryErrorCodeEnum>;

/**
 * Entity not found error
 */
export class EntityNotFoundError extends Error {
  public readonly code = RepositoryErrorCodeEnum.enum.ENTITY_NOT_FOUND;
  public readonly context: Record<string, unknown>;

  constructor(entityName: string, identifier: string | number, additionalContext?: Record<string, unknown>) {
    super(`${entityName} with identifier '${identifier}' not found`);
    this.name = 'EntityNotFoundError';
    this.context = {
      entityName,
      identifier,
      ...additionalContext,
    };
  }
}

/**
 * Duplicate entity error
 */
export class DuplicateEntityError extends Error {
  public readonly code = RepositoryErrorCodeEnum.enum.DUPLICATE_ENTITY;
  public readonly context: Record<string, unknown>;

  constructor(entityName: string, field: string, value: string | number, additionalContext?: Record<string, unknown>) {
    super(`${entityName} with ${field} '${value}' already exists`);
    this.name = 'DuplicateEntityError';
    this.context = {
      entityName,
      field,
      value,
      ...additionalContext,
    };
  }
}

/**
 * Repository validation error
 */
export class RepositoryValidationError extends Error {
  public readonly code = RepositoryErrorCodeEnum.enum.ENTITY_VALIDATION_FAILED;
  public readonly context: Record<string, unknown>;

  constructor(entityName: string, validationErrors: string[], additionalContext?: Record<string, unknown>) {
    super(`Validation failed for ${entityName}: ${validationErrors.join(', ')}`);
    this.name = 'RepositoryValidationError';
    this.context = {
      entityName,
      validationErrors,
      ...additionalContext,
    };
  }
}

/**
 * Repository access denied error
 */
export class RepositoryAccessDeniedError extends Error {
  public readonly code = RepositoryErrorCodeEnum.enum.ACCESS_DENIED;
  public readonly context: Record<string, unknown>;

  constructor(operation: string, entityName: string, userId?: string, additionalContext?: Record<string, unknown>) {
    super(`Access denied for ${operation} operation on ${entityName}`);
    this.name = 'RepositoryAccessDeniedError';
    this.context = {
      operation,
      entityName,
      userId,
      ...additionalContext,
    };
  }
}

/**
 * Transaction options for repository operations
 */
export interface TransactionOptions {
  /** Transaction isolation level */
  isolationLevel?: 'READ_UNCOMMITTED' | 'READ_COMMITTED' | 'REPEATABLE_READ' | 'SERIALIZABLE';
  /** Transaction timeout in milliseconds */
  timeout?: number;
  /** Whether to automatically rollback on error */
  autoRollback?: boolean;
}

/**
 * Audit information for entity operations
 */
export interface AuditInfo {
  /** User ID who performed the operation */
  userId: string;
  /** UTC timestamp of the operation */
  timestamp: string;
  /** Optional reason for the operation */
  reason?: string;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult<T> {
  /** Successfully processed entities */
  successful: T[];
  /** Failed operations with error details */
  failed: Array<{
    entity: Partial<T>;
    error: string;
  }>;
  /** Total number of entities processed */
  totalProcessed: number;
  /** Number of successful operations */
  successCount: number;
  /** Number of failed operations */
  failureCount: number;
}

/**
 * Query builder options for complex queries
 */
export interface QueryBuilderOptions {
  /** Fields to select */
  select?: string[];
  /** Join conditions */
  joins?: Array<{
    table: string;
    condition: string;
    type?: 'INNER' | 'LEFT' | 'RIGHT' | 'FULL';
  }>;
  /** Where conditions */
  where?: Record<string, unknown>;
  /** Order by clauses */
  orderBy?: Array<{
    field: string;
    direction: 'ASC' | 'DESC';
  }>;
  /** Group by fields */
  groupBy?: string[];
  /** Having conditions */
  having?: Record<string, unknown>;
  /** Limit number of results */
  limit?: number;
  /** Offset for pagination */
  offset?: number;
}

/**
 * Repository operation context
 */
export interface RepositoryContext {
  /** Current user ID */
  userId?: string;
  /** Request ID for tracing */
  requestId?: string;
  /** Transaction ID if within a transaction */
  transactionId?: string;
  /** Additional context metadata */
  metadata?: Record<string, unknown>;
}
