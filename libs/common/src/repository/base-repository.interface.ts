/**
 * Base repository interfaces for PatternTrade API
 *
 * Provides core repository contracts that all domain repositories should implement.
 * These interfaces ensure consistent data access patterns across the entire application.
 */

/**
 * Core repository interface providing standard CRUD operations
 *
 * @template T - Entity type
 * @template CreateData - Data type for entity creation
 * @template UpdateData - Data type for entity updates
 * @template ID - ID type (number for serial IDs, string for UUIDs)
 */
export interface IBaseRepository<T, CreateData, UpdateData, ID = number> {
  /**
   * Create a new entity
   * @param data - Entity creation data
   * @returns Promise<T> - Created entity
   */
  create(data: CreateData): Promise<T>;

  /**
   * Find entity by ID
   * @param id - Entity ID
   * @returns Promise<T | null> - Found entity or null
   */
  findById(id: ID): Promise<T | null>;

  /**
   * Update entity by ID
   * @param id - Entity ID
   * @param updates - Partial entity data for updates
   * @returns Promise<T> - Updated entity
   */
  update(id: ID, updates: UpdateData): Promise<T>;

  /**
   * Delete entity by ID
   * @param id - Entity ID
   * @returns Promise<void>
   */
  delete(id: ID): Promise<void>;

  /**
   * Find all entities with optional filtering
   * @param filters - Optional filter criteria
   * @returns Promise<T[]> - Array of entities
   */
  findAll(filters?: Record<string, unknown>): Promise<T[]>;
}

/**
 * Repository interface for entities that belong to users
 *
 * @template T - Entity type
 * @template CreateData - Data type for entity creation
 * @template UpdateData - Data type for entity updates
 * @template ID - ID type (number for serial IDs, string for UUIDs)
 */
export interface IRepositoryWithUser<T, CreateData, UpdateData, ID = number>
  extends IBaseRepository<T, CreateData, UpdateData, ID> {
  /**
   * Find entities by user ID
   * @param userId - User ID
   * @returns Promise<T[]> - Array of user's entities
   */
  findByUserId(userId: string): Promise<T[]>;

  /**
   * Delete all entities for a user
   * @param userId - User ID
   * @returns Promise<void>
   */
  deleteByUserId(userId: string): Promise<void>;
}

/**
 * Repository interface for paginated queries
 *
 * @template T - Entity type
 */
export interface IPaginatedRepository<T> {
  /**
   * Find entities with pagination
   * @param options - Pagination options
   * @returns Promise<PaginatedResult<T>> - Paginated results
   */
  findPaginated(options: PaginationOptions): Promise<PaginatedResult<T>>;
}

/**
 * Pagination options for repository queries
 */
export interface PaginationOptions {
  /** Maximum number of results to return */
  limit?: number;
  /** Number of results to skip */
  offset?: number;
  /** Field to sort by */
  sortBy?: string;
  /** Sort order */
  sortOrder?: 'ASC' | 'DESC';
  /** Filter criteria */
  filters?: Record<string, unknown>;
}

/**
 * Paginated query result
 *
 * @template T - Entity type
 */
export interface PaginatedResult<T> {
  /** Array of entities */
  data: T[];
  /** Total number of entities matching the query */
  total: number;
  /** Whether there are more results available */
  hasMore: boolean;
  /** Number of results per page */
  limit: number;
  /** Number of results skipped */
  offset: number;
}

/**
 * Repository interface for entities with soft delete capability
 *
 * @template T - Entity type
 * @template CreateData - Data type for entity creation
 * @template UpdateData - Data type for entity updates
 * @template ID - ID type (number for serial IDs, string for UUIDs)
 */
export interface ISoftDeleteRepository<T, CreateData, UpdateData, ID = number>
  extends IBaseRepository<T, CreateData, UpdateData, ID> {
  /**
   * Soft delete entity by ID
   * @param id - Entity ID
   * @param deletedBy - User ID who performed the deletion
   * @returns Promise<void>
   */
  softDelete(id: ID, deletedBy: string): Promise<void>;

  /**
   * Restore soft deleted entity
   * @param id - Entity ID
   * @param restoredBy - User ID who performed the restoration
   * @returns Promise<T> - Restored entity
   */
  restore(id: ID, restoredBy: string): Promise<T>;

  /**
   * Find entities including soft deleted ones
   * @param filters - Optional filter criteria
   * @returns Promise<T[]> - Array of entities including deleted
   */
  findAllIncludingDeleted(filters?: Record<string, unknown>): Promise<T[]>;

  /**
   * Find only soft deleted entities
   * @param filters - Optional filter criteria
   * @returns Promise<T[]> - Array of deleted entities
   */
  findDeleted(filters?: Record<string, unknown>): Promise<T[]>;
}
