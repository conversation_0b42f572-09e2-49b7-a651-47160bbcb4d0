import { z } from 'zod/v4';

// ==================== SYSTEM ENUMS ====================

/**
 * Environment enum for application deployment contexts
 */
export const EnvironmentEnum = z
  .enum(['local', 'docker', 'development', 'staging', 'test', 'preproduction', 'development_senthil', 'production'])
  .describe('Application deployment environments');

/**
 * Log level enum for application logging
 */
export const LogLevelEnum = z
  .enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal'])
  .describe('Logging severity levels');

/**
 * API response status enum
 */
export const ApiStatusEnum = z.enum(['success', 'error', 'pending']).describe('API response status indicators');

/**
 * Sort order enum for pagination and listing
 */
export const SortOrderEnum = z.enum(['asc', 'desc']).describe('Sort direction for queries');

/**
 * Day of week enum for scheduling and business hours
 */
export const DayOfWeekEnum = z
  .enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
  .describe('Days of the week for scheduling');

/**
 * Application type enum for configuration scoping
 */
export const ApplicationEnum = z
  .enum(['global', 'api', 'oms', 'ticker', 'analyser', 'simulator'])
  .describe('Application types for configuration scoping');

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types from Zod schemas using z.output

export type Environment = z.output<typeof EnvironmentEnum>;
export type LogLevel = z.output<typeof LogLevelEnum>;
export type ApiStatus = z.output<typeof ApiStatusEnum>;
export type SortOrder = z.output<typeof SortOrderEnum>;
export type DayOfWeek = z.output<typeof DayOfWeekEnum>;

export type Application = z.output<typeof ApplicationEnum>;

// ==================== UTILITY FUNCTIONS ====================

/**
 * Get all values from a Zod enum as an array
 * Uses Zod 4's improved type inference
 */
export const getEnumValues = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T): Array<z.output<T>> => {
  return enumSchema.options as Array<z.output<T>>;
};

/**
 * Check if a value is valid for a given enum
 * Uses Zod 4's improved type inference
 */
export const isValidEnumValue = <T extends z.ZodEnum<Record<string, string>>>(
  enumSchema: T,
  value: unknown,
): value is z.output<T> => {
  return enumSchema.safeParse(value).success;
};

/**
 * Get enum description
 * Compatible with Zod 4's improved metadata handling
 */
export const getEnumDescription = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T): string | undefined => {
  return (enumSchema as { description?: string }).description;
};

/**
 * Safely parse enum value with fallback
 * Uses Zod 4's improved type inference
 */
export const parseEnumValue = <T extends z.ZodEnum<Record<string, string>>>(
  enumSchema: T,
  value: unknown,
  fallback: z.output<T>,
): z.output<T> => {
  const result = enumSchema.safeParse(value);
  return result.success ? result.data : fallback;
};

/**
 * Get enum key-value pairs as an object
 * Uses Zod 4's improved type inference
 */
export const getEnumKeyValuePairs = <T extends z.ZodEnum<Record<string, string>>>(
  enumSchema: T,
): Record<z.output<T>, z.output<T>> => {
  const values = enumSchema.options as Array<z.output<T>>;
  return values.reduce(
    (acc, value) => {
      acc[value] = value;
      return acc;
    },
    {} as Record<z.output<T>, z.output<T>>,
  );
};

/**
 * Create a validation function for a specific enum
 * Uses Zod 4's improved type inference
 */
export const createEnumValidator = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T) => {
  return (value: unknown): value is z.output<T> => isValidEnumValue(enumSchema, value);
};

/**
 * Transform enum values using a mapping function
 * Uses Zod 4's improved type inference
 */
export const transformEnumValues = <T extends z.ZodEnum<Record<string, string>>, R>(
  enumSchema: T,
  transformer: (value: z.output<T>) => R,
): R[] => {
  return enumSchema.options.map((value: string) => transformer(value as z.output<T>));
};

/**
 * Get random enum value (useful for testing)
 * Uses Zod 4's improved type inference
 */
export const getRandomEnumValue = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T): z.output<T> => {
  const values = enumSchema.options;
  return values[Math.floor(Math.random() * values.length)] as z.output<T>;
};

// ==================== ENUM COLLECTIONS ====================

/**
 * System-related enums collection
 */
export const SystemEnums = {
  Environment: EnvironmentEnum,
  LogLevel: LogLevelEnum,
  ApiStatus: ApiStatusEnum,
} as const;
