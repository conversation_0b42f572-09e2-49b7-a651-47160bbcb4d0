import { serial, varchar } from 'drizzle-orm/pg-core';

export const baseModel = {
  id: serial('id').primary<PERSON>ey(),
  createdAt: varchar('created_at', { length: 32 }).notNull(),
  createdBy: varchar('created_by', { length: 255 }).notNull(),
  updatedAt: varchar('updated_at', { length: 32 }).notNull(),
  updatedBy: varchar('updated_by', { length: 255 }).notNull(),
  deletedAt: varchar('deleted_at', { length: 32 }),
  deletedBy: varchar('deleted_by', { length: 255 }),
};
