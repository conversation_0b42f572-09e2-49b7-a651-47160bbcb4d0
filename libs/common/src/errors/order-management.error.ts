import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';

export const OrderManagementErrorEnum = z.enum([
  'INVALID_ORDER_DATA',
  'MISSING_ORDER_FIELDS',
  'INVALID_ORDER_TYPE',
  'INVALID_QUANTITY',
  'INVALID_PRICE',
  'INVALID_SYMBOL',
  'INVALID_EXCHANGE',
  'INVALID_PRODUCT_TYPE',
  'INVALID_VALIDITY_TYPE',
  'INVALID_TRANSACTION_TYPE',
  'ORDER_PLACEMENT_FAILED',
  'ORDER_MODIFICATION_FAILED',
  'ORDER_CANCELLATION_FAILED',
  'ORDER_NOT_FOUND',
  'ORDER_ALREADY_EXECUTED',
  'ORDER_ALREADY_CANCELLED',
  'INSUFFICIENT_FUNDS',
  'INSUFFICIENT_MARGIN',
  'MARKET_CLOSED_FOR_TRADING',
  'SYMBOL_NOT_TRADEABLE',
  'POSITION_LIMIT_EXCEEDED',
  'ORDER_SIZE_LIMIT_EXCEEDED',
  'PRICE_BAND_VIOLATION',
  'CIRCUIT_LIMIT_HIT',
  'TRADING_HALTED',
  'RISK_MANAGEMENT_REJECTION',
  'COMPLIANCE_VIOLATION',
  'DUPLICATE_ORDER',
  'STALE_ORDER',
  'UNKNOWN_ORDER_ERROR',
]);

export const OrderManagementErrorMessages: Record<OrderManagementErrorEnumType, string> = {
  INVALID_ORDER_DATA: 'Invalid order data provided',
  MISSING_ORDER_FIELDS: 'Required order fields are missing',
  INVALID_ORDER_TYPE: 'Invalid order type specified',
  INVALID_QUANTITY: 'Invalid order quantity specified',
  INVALID_PRICE: 'Invalid order price specified',
  INVALID_SYMBOL: 'Invalid symbol specified for order',
  INVALID_EXCHANGE: 'Invalid exchange specified for order',
  INVALID_PRODUCT_TYPE: 'Invalid product type specified',
  INVALID_VALIDITY_TYPE: 'Invalid validity type specified',
  INVALID_TRANSACTION_TYPE: 'Invalid transaction type specified',
  ORDER_PLACEMENT_FAILED: 'Order placement failed',
  ORDER_MODIFICATION_FAILED: 'Order modification failed',
  ORDER_CANCELLATION_FAILED: 'Order cancellation failed',
  ORDER_NOT_FOUND: 'Order not found',
  ORDER_ALREADY_EXECUTED: 'Order has already been executed',
  ORDER_ALREADY_CANCELLED: 'Order has already been cancelled',
  INSUFFICIENT_FUNDS: 'Insufficient funds to place order',
  INSUFFICIENT_MARGIN: 'Insufficient margin to place order',
  MARKET_CLOSED_FOR_TRADING: 'Market is closed for trading',
  SYMBOL_NOT_TRADEABLE: 'Symbol is not tradeable',
  POSITION_LIMIT_EXCEEDED: 'Position limit exceeded',
  ORDER_SIZE_LIMIT_EXCEEDED: 'Order size limit exceeded',
  PRICE_BAND_VIOLATION: 'Price band violation detected',
  CIRCUIT_LIMIT_HIT: 'Circuit limit hit for symbol',
  TRADING_HALTED: 'Trading halted for symbol',
  RISK_MANAGEMENT_REJECTION: 'Order rejected by risk management',
  COMPLIANCE_VIOLATION: 'Order violates compliance rules',
  DUPLICATE_ORDER: 'Duplicate order detected',
  STALE_ORDER: 'Stale order detected',
  UNKNOWN_ORDER_ERROR: 'Unknown order management error occurred',
};

export type OrderManagementErrorEnumType = z.output<typeof OrderManagementErrorEnum>;

export class OrderManagementError extends BaseError<OrderManagementErrorEnumType> {
  constructor(
    name: OrderManagementErrorEnumType,
    domain: ErrorDomainType,
    details?: { message?: string; cause?: unknown },
  ) {
    super({
      name,
      domain,
      message: details?.message ? details.message : OrderManagementErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
