import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';

export const BadRequestErrorEnum = z.enum([
  'GENERATE_SESSION_ERROR',
  'GET_PROFILE_ERROR',
  'LOGIN_ERROR',
  'DISCONNECT_ERROR',
  'KITE_CONNECT_NOT_INITIALIZED',
]);

export const BadRequestErrorMessages: Record<BadRequestErrorEnumType, string> = {
  GENERATE_SESSION_ERROR: 'Failed to generate session',
  GET_PROFILE_ERROR: 'Failed to get profile',
  LOGIN_ERROR: 'Failed to login',
  DISCONNECT_ERROR: 'Failed to disconnect',
  KITE_CONNECT_NOT_INITIALIZED: 'KiteConnect OR AuthConfig not initialized',
};

export type BadRequestErrorEnumType = z.output<typeof BadRequestErrorEnum>;

export class BadRequestError extends BaseError<BadRequestErrorEnumType> {
  constructor(name: BadRequestErrorEnumType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : BadRequestErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
