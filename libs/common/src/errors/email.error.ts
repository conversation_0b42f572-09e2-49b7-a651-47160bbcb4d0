import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';

export const EmailErrorEnum = z.enum(['SEND_EMAIL_ERROR']);

export const EmailErrorMessages: Record<EmailErrorEnumType, string> = {
  SEND_EMAIL_ERROR: 'Error sending Email',
};

export type EmailErrorEnumType = z.output<typeof EmailErrorEnum>;

export class EmailError extends BaseError<EmailErrorEnumType> {
  constructor(name: EmailErrorEnumType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : EmailErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
