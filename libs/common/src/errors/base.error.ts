import { DateTimeUtilsService } from '@app/utils';

export class BaseError<T extends string> extends Error {
  override name: T;
  override message: string;
  override cause: unknown;
  domain: string;
  protected readonly dateTimeUtils = new DateTimeUtilsService();

  constructor({ name, message, cause, domain }: { name: T; message: string; cause?: unknown; domain: string }) {
    super();
    this.name = name;
    this.message = message;
    this.cause = cause;
    this.domain = domain;
  }
}
