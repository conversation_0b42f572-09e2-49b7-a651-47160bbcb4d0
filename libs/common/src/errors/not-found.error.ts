import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';

export const NotFoundErrorEnum = z.enum(['BROKER_NOT_FOUND']);

export const NotFoundErrorMessages: Record<NotFoundErrorType, string> = {
  [NotFoundErrorEnum.enum.BROKER_NOT_FOUND]: 'Broker not found',
};

export type NotFoundErrorType = z.output<typeof NotFoundErrorEnum>;

export class NotFoundError extends BaseError<NotFoundErrorType> {
  constructor(name: NotFoundErrorType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : NotFoundErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
