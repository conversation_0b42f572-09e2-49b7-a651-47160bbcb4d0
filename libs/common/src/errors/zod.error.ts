import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';
import type { DateTimeUtilsService } from '@app/utils/datetime-utils.service';

export const ZodErrorEnum = z.enum([
  'PARSE_ERROR',
  'VALIDATION_FAILED',
  'REQUIRED_FIELD_MISSING',
  'INVALID_TYPE',
  'INVALID_FORMAT',
  'VALUE_OUT_OF_RANGE',
  'ENUM_VALUE_INVALID',
  'CUSTOM_VALIDATION_FAILED',
]);

export const ZodErrorMessages: Record<ZodErrorEnumType, string> = {
  PARSE_ERROR: 'Error parsing Zod schema',
  VALIDATION_FAILED: 'Schema validation failed',
  REQUIRED_FIELD_MISSING: 'Required field is missing',
  INVALID_TYPE: 'Invalid data type provided',
  INVALID_FORMAT: 'Invalid format for field',
  VALUE_OUT_OF_RANGE: 'Value is outside acceptable range',
  ENUM_VALUE_INVALID: 'Value is not a valid enum option',
  CUSTOM_VALIDATION_FAILED: 'Custom validation rule failed',
};

export type ZodErrorEnumType = z.output<typeof ZodErrorEnum>;

/**
 * Enhanced Zod validation error with detailed field information
 */
export interface ZodValidationDetails {
  field: string;
  value: unknown;
  expectedType?: string;
  validOptions?: unknown[];
  customMessage?: string;
}

export class ZodError extends BaseError<ZodErrorEnumType> {
  public readonly validationDetails?: ZodValidationDetails[];
  public readonly zodIssues?: z.ZodIssue[];

  constructor(
    name: ZodErrorEnumType,
    domain: ErrorDomainType,
    details?: {
      message?: string;
      cause?: unknown;
      validationDetails?: ZodValidationDetails[];
      zodIssues?: z.ZodIssue[];
    },
  ) {
    super({
      name,
      domain,
      message: details?.message ? details.message : ZodErrorMessages[name],
      cause: details?.cause,
    });

    this.validationDetails = details?.validationDetails;
    this.zodIssues = details?.zodIssues;

    // Maintain proper prototype chain
    Object.setPrototypeOf(this, new.target.prototype);

    // Capture stack trace with proper prototype chain
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }

    // If cause is an Error, preserve its stack
    if (details?.cause instanceof Error) {
      this.stack = `${this.stack}\nCaused by: ${details?.cause?.stack}`;
    }
  }

  /**
   * Get formatted validation error details
   */
  getFormattedValidationErrors(): string {
    if (!this.validationDetails || this.validationDetails.length === 0) {
      return this.message;
    }

    const errorMessages = this.validationDetails.map((detail) => {
      let message = `Field '${detail.field}': `;

      if (detail.customMessage) {
        message += detail.customMessage;
      } else if (detail.expectedType) {
        message += `expected ${detail.expectedType}, received ${typeof detail.value}`;
      } else if (detail.validOptions) {
        message += `must be one of [${detail.validOptions.join(', ')}]`;
      } else {
        message += 'validation failed';
      }

      return message;
    });

    return `${this.message}: ${errorMessages.join('; ')}`;
  }

  /**
   * Convert to API-friendly error response
   */
  toApiResponse(dateTimeUtils: DateTimeUtilsService) {
    return {
      error: this.name,
      message: this.getFormattedValidationErrors(),
      domain: this.domain,
      ...(this.validationDetails && { validationErrors: this.validationDetails }),
      timestamp: dateTimeUtils.getUtcNow(),
    };
  }
}
