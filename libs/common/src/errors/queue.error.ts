import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';

export const QueueErrorEnum = z.enum([
  'ADD_JOB_ERROR',
  'GET_JOB_ERROR',
  'REMOVE_JOB_ERROR',
  'ADD_REPEATABLE_JOB_ERROR',
  'REMOVE_REPEATABLE_JOB_ERROR',
  'GET_REPEATABLE_JOB_ERROR',
  'ADD_DELAYED_JOB_ERROR',
  'REMOVE_DELAYED_JOB_ERROR',
  'GET_DELAYED_JOB_ERROR',
  'PRICING_QUEUE_WORKER',
  'ADD_FLOW_JOB_ERROR',
]);

export const QueueErrorMessages: Record<QueueErrorEnumType, string> = {
  ADD_JOB_ERROR: 'Error adding job to queue',
  GET_JOB_ERROR: 'Error getting job from queue',
  REMOVE_JOB_ERROR: 'Error removing job from queue',
  ADD_REPEATABLE_JOB_ERROR: 'Error adding repeatable job to queue',
  REMOVE_REPEATABLE_JOB_ERROR: 'Error removing repeatable job from queue',
  GET_REPEATABLE_JOB_ERROR: 'Error getting repeatable job from queue',
  ADD_DELAYED_JOB_ERROR: 'Error adding delayed job to queue',
  REMOVE_DELAYED_JOB_ERROR: 'Error removing delayed job from queue',
  GET_DELAYED_JOB_ERROR: 'Error getting delayed job from queue',
  PRICING_QUEUE_WORKER: 'Error processing pricing queue worker',
  ADD_FLOW_JOB_ERROR: 'Error adding flow job to queue',
};

export type QueueErrorEnumType = z.output<typeof QueueErrorEnum>;

export class QueueError extends BaseError<QueueErrorEnumType> {
  constructor(name: QueueErrorEnumType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : QueueErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
