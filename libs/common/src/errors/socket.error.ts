import { z } from 'zod/v4';
import { BaseError } from './base.error';
import type { ErrorDomainType } from './domain';

export const SocketErrorEnum = z.enum(['SOCKET_ERROR', 'SOCKET_NOT_FOUND_ERROR']);

export const SocketErrorMessages: Record<SocketErrorEnumType, string> = {
  SOCKET_ERROR: 'Error getting Socket',
  SOCKET_NOT_FOUND_ERROR: 'Socket not found',
};

export type SocketErrorEnumType = z.output<typeof SocketErrorEnum>;

export class SocketError extends BaseError<SocketErrorEnumType> {
  constructor(name: SocketErrorEnumType, domain: ErrorDomainType, details?: { message?: string; cause?: unknown }) {
    super({
      name,
      domain,
      message: details?.message ? details.message : SocketErrorMessages[name],
      cause: details?.cause,
    });
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}
