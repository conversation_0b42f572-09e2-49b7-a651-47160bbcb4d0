# Test Environment Variables
NODE_ENV=test
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_NAME=test_db
DB_USERNAME=test_user
DB_PASSWORD=test_password
DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_db

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-purposes-only

# Session Configuration
SESSION_SECRET_KEY=test-session-secret-key-for-testing-purposes-only

# Broker Configuration
BROKER_ENCRYPTION_KEY=test-32-character-encryption-key-here-change-this-for-production
BROKER_ENCRYPTION_SALT=test-16-char-salt
BROKER_KEY_ROTATION_INTERVAL=86400000

# API Configuration
API_BASE_URL=http://localhost:3000