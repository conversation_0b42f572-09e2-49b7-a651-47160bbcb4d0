# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/patterntrade

# Redis Configuration
REDIS_URL=redis://localhost:6379

# API Configuration
API_BASE_URL=http://localhost:3000
PORT=3000

# Broker Management (Required for encryption)
BROKER_ENCRYPTION_KEY=your-32-character-encryption-key-here-change-this
BROKER_ENCRYPTION_SALT=your-16-char-salt
BROKER_KEY_ROTATION_INTERVAL=86400000

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here

# Environment
NODE_ENV=development