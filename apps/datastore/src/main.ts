declare const module: {
  hot?: {
    accept: () => void;
    dispose: (callback: () => Promise<void>) => void;
  };
};

import { NestFactory } from '@nestjs/core';
import { DatastoreModule } from './datastore.module';
import { LoggerErrorInterceptor, Logger as PinoLogger } from 'nestjs-pino';
import type { MicroserviceOptions } from '@nestjs/microservices';
import { Logger } from '@nestjs/common';
import { EnvService } from '@app/core/env';
import { getRedisTransportOption, getGrpcTransportOption } from '@app/core/transport';

async function bootstrap() {
  const app = await NestFactory.create(DatastoreModule, {
    bufferLogs: true,
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  const envService = app.get(EnvService);
  app.useLogger(app.get(PinoLogger));
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  // Connect microservice with dual transport (Redis + gRPC) for comprehensive communication
  app.connectMicroservice<MicroserviceOptions>(getRedisTransportOption(envService), {
    inheritAppConfig: true,
  });

  app.connectMicroservice<MicroserviceOptions>(getGrpcTransportOption(envService, 'datastore'), {
    inheritAppConfig: true,
  });

  await app.startAllMicroservices();

  Logger.log({
    context: 'Bootstrap',
    msg: '🚀 Datastore Microservice is running (Redis + gRPC transports)',
  });

  // Gracefully shutdown the server
  app.enableShutdownHooks();

  if (module.hot) {
    module.hot.accept();
    module.hot.dispose(() => app.close());
  }
}

bootstrap();
