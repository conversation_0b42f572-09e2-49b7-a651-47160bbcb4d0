import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { SymbolMicroservice } from './symbol.microservice';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

/**
 * Datastore Service
 *
 * Main service for the datastore application that coordinates all data storage
 * and retrieval operations. Acts as the primary entry point for datastore
 * functionality and manages service initialization and health monitoring.
 *
 * Features:
 * - Service initialization and startup coordination
 * - Health monitoring and status reporting
 * - Service discovery and registration
 * - Cross-service communication coordination
 */
@Injectable()
export class DatastoreService implements OnModuleInit {
  private readonly logger = new Logger(DatastoreService.name);

  constructor(
    private readonly symbolMicroservice: SymbolMicroservice,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Initialize the datastore service
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('Initializing Datastore Service');

      // Perform any necessary startup tasks
      await this.initializeServices();

      this.logger.log('Datastore Service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Datastore Service', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get basic service information
   */
  getHello(): string {
    return 'PatternTrade Datastore Service - Symbol Master Data Management';
  }

  /**
   * Get service information
   */
  async getServiceInfo(): Promise<{
    name: string;
    version: string;
    description: string;
    status: string;
    uptime: number;
    timestamp: Date;
    features: string[];
  }> {
    try {
      const symbolHealth = await this.symbolMicroservice.getHealthStatus();

      return {
        name: 'PatternTrade Datastore Service',
        version: process.env.npm_package_version || '1.0.0',
        description: 'Symbol master data management and storage service',
        status: symbolHealth.isHealthy ? 'healthy' : 'unhealthy',
        uptime: process.uptime(),
        timestamp: this.dateTimeUtils.getUtcNow(),
        features: [
          'Symbol Master Data Storage',
          'QuestDB Time-Series Integration',
          'PostgreSQL Audit Logging',
          'Redis Microservice Communication',
          'Background Job Processing',
          'Health Monitoring',
          'Real-time Data Synchronization',
        ],
      };
    } catch (error) {
      this.logger.error('Failed to get service info', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        name: 'PatternTrade Datastore Service',
        version: process.env.npm_package_version || '1.0.0',
        description: 'Symbol master data management and storage service',
        status: 'unhealthy',
        uptime: process.uptime(),
        timestamp: this.dateTimeUtils.getUtcNow(),
        features: [],
      };
    }
  }

  /**
   * Get comprehensive service status
   */
  async getServiceStatus(): Promise<{
    isHealthy: boolean;
    services: any;
    statistics: any;
    lastCheck: Date;
  }> {
    try {
      const [healthStatus, statistics] = await Promise.all([
        this.symbolMicroservice.getHealthStatus(),
        this.symbolMicroservice.getServiceStatistics(),
      ]);

      return {
        isHealthy: healthStatus.isHealthy,
        services: healthStatus.services,
        statistics,
        lastCheck: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get service status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        services: null,
        statistics: null,
        lastCheck: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  // ==================== PRIVATE METHODS ====================

  /**
   * Initialize all services
   */
  private async initializeServices(): Promise<void> {
    try {
      this.logger.log('Initializing datastore services');

      // Services are initialized through their respective modules
      // This method can be used for any additional startup logic

      this.logger.log('All datastore services initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize services', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }
}
