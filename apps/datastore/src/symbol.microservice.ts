import { Injectable, Logger } from '@nestjs/common';
import {
  SymbolService,
  SymbolMasterRepository,
  SymbolDownloadQueueService,
  SymbolAuditService,
  SymbolMasterQueryFilters,
  SymbolDownloadRequest,
  SymbolMaster,
} from '@app/symbol';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

/**
 * Symbol Microservice
 *
 * Provides high-level business logic for symbol operations in the datastore application.
 * Acts as a facade for symbol-related services and handles complex operations that
 * involve multiple services.
 *
 * Features:
 * - Orchestrates symbol download operations
 * - Provides unified symbol data access
 * - Handles complex queries and aggregations
 * - Manages service health and monitoring
 * - Coordinates between repository, queue, and audit services
 */
@Injectable()
export class SymbolMicroservice {
  private readonly logger = new Logger(SymbolMicroservice.name);

  constructor(
    private readonly symbolService: SymbolService,
    private readonly symbolRepository: SymbolMasterRepository,
    private readonly symbolQueueService: SymbolDownloadQueueService,
    private readonly symbolAuditService: SymbolAuditService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== SYMBOL DATA OPERATIONS ====================

  /**
   * Get symbols with enhanced filtering and caching
   */
  async getSymbols(filters: SymbolMasterQueryFilters): Promise<{
    data: SymbolMaster[];
    meta: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
      cacheStatus?: string;
    };
  }> {
    try {
      this.logger.log('Getting symbols with enhanced filtering', filters);

      const result = await this.symbolRepository.findSymbols(filters);

      return {
        data: result.data,
        meta: {
          total: result.total,
          limit: filters.limit || 100,
          offset: filters.offset || 0,
          hasMore: result.hasMore,
          cacheStatus: 'miss', // Could implement caching later
        },
      };
    } catch (error) {
      this.logger.error('Failed to get symbols', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get symbol by instrument token with caching
   */
  async getSymbolByToken(instrumentToken: string): Promise<SymbolMaster | null> {
    try {
      this.logger.log('Getting symbol by token', { instrumentToken });

      const symbol = await this.symbolRepository.findByInstrumentToken(instrumentToken);

      if (symbol) {
        this.logger.debug('Symbol found', {
          instrumentToken,
          tradingSymbol: symbol.tradingSymbol,
          exchange: symbol.exchange,
        });
      } else {
        this.logger.debug('Symbol not found', { instrumentToken });
      }

      return symbol;
    } catch (error) {
      this.logger.error('Failed to get symbol by token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Search symbols with enhanced matching
   */
  async searchSymbols(searchTerm: string, limit: number = 50, offset: number = 0): Promise<SymbolMaster[]> {
    try {
      this.logger.log('Searching symbols', { searchTerm, limit, offset });

      const symbols = await this.symbolRepository.searchSymbols(searchTerm, limit, offset);

      this.logger.debug('Symbol search completed', {
        searchTerm,
        resultCount: symbols.length,
        limit,
        offset,
      });

      return symbols;
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get symbols by exchange and segment
   */
  async getSymbolsByExchangeSegment(
    exchange: string,
    segment: string,
    limit: number = 1000,
    offset: number = 0,
  ): Promise<SymbolMaster[]> {
    try {
      this.logger.log('Getting symbols by exchange and segment', {
        exchange,
        segment,
        limit,
        offset,
      });

      const symbols = await this.symbolRepository.getSymbolsByExchangeSegment(exchange, segment, limit, offset);

      return symbols;
    } catch (error) {
      this.logger.error('Failed to get symbols by exchange and segment', {
        exchange,
        segment,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== DOWNLOAD OPERATIONS ====================

  /**
   * Trigger symbol download with comprehensive tracking
   */
  async triggerSymbolDownload(request: SymbolDownloadRequest): Promise<{
    jobId: string;
    requestId: string;
    scheduledAt: Date;
    estimatedCompletion?: Date;
  }> {
    try {
      this.logger.log('Triggering symbol download', request);

      const result = await this.symbolQueueService.addSymbolDownloadJob(request.exchange, request.segment, {
        forceRefresh: request.forceRefresh,
        batchSize: request.batchSize,
        requestId: request.requestId,
        priority: request.priority || 5,
      });

      // Estimate completion time based on historical data
      const estimatedCompletion = this.estimateCompletionTime(request);

      this.logger.log('Symbol download job scheduled', {
        jobId: result.jobId,
        requestId: result.requestId,
        exchange: request.exchange,
        segment: request.segment,
        estimatedCompletion,
      });

      return {
        jobId: result.jobId,
        requestId: result.requestId,
        scheduledAt: this.dateTimeUtils.getUtcNow(),
        estimatedCompletion,
      };
    } catch (error) {
      this.logger.error('Failed to trigger symbol download', {
        request,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get comprehensive download status
   */
  async getDownloadStatus(jobId: string): Promise<any> {
    try {
      this.logger.log('Getting comprehensive download status', { jobId });

      const status = await this.symbolQueueService.getJobStatus(jobId);

      if (!status) {
        return null;
      }

      // Enhance status with additional information
      const enhancedStatus = {
        ...status,
        estimatedTimeRemaining: this.estimateTimeRemaining(status),
        healthStatus: status.state === 'failed' ? 'unhealthy' : 'healthy',
      };

      return enhancedStatus;
    } catch (error) {
      this.logger.error('Failed to get download status', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Trigger immediate download for all exchanges
   */
  async triggerImmediateDownload(): Promise<{
    jobId: string;
    requestId: string;
    scheduledAt: Date;
  }> {
    try {
      this.logger.log('Triggering immediate download for all exchanges');

      const result = await this.symbolQueueService.triggerImmediateDownload({
        forceRefresh: true,
        priority: 1, // Highest priority
      });

      return {
        jobId: result.jobId,
        requestId: result.requestId,
        scheduledAt: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger immediate download', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== STATISTICS AND MONITORING ====================

  /**
   * Get comprehensive service statistics
   */
  async getServiceStatistics(): Promise<{
    repository: any;
    queue: any;
    audit: any;
    summary: {
      totalSymbols: number;
      activeSymbols: number;
      lastUpdate: Date | null;
      queueHealth: string;
      overallHealth: string;
    };
  }> {
    try {
      this.logger.log('Getting comprehensive service statistics');

      const [repositoryStats, queueStats, auditHealth] = await Promise.all([
        this.symbolRepository.getStats(),
        this.symbolQueueService.getQueueStats(),
        this.symbolAuditService.getHealthStatus(),
      ]);

      const summary = {
        totalSymbols: repositoryStats.totalRecords,
        activeSymbols: repositoryStats.activeRecords,
        lastUpdate: repositoryStats.lastUpdate,
        queueHealth: queueStats.cronJobStatus === 'active' ? 'healthy' : 'unhealthy',
        overallHealth: this.calculateOverallHealth(repositoryStats, queueStats, auditHealth),
      };

      return {
        repository: repositoryStats,
        queue: queueStats,
        audit: auditHealth,
        summary,
      };
    } catch (error) {
      this.logger.error('Failed to get service statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean;
    services: {
      repository: any;
      queue: any;
      audit: any;
    };
    issues: string[];
    lastCheck: Date;
  }> {
    try {
      this.logger.log('Getting service health status');

      const [repositoryHealth, queueHealth, auditHealth] = await Promise.all([
        this.symbolRepository.getHealthStatus(),
        this.symbolQueueService.getComprehensiveHealthStatus(),
        this.symbolAuditService.getHealthStatus(),
      ]);

      const issues: string[] = [];

      if (!repositoryHealth.isHealthy) {
        issues.push('Repository is unhealthy');
      }

      if (!queueHealth.isHealthy) {
        issues.push('Queue system is unhealthy');
        issues.push(...queueHealth.issues);
      }

      if (!auditHealth.isHealthy) {
        issues.push('Audit system is unhealthy');
        issues.push(...auditHealth.issues);
      }

      const isHealthy = issues.length === 0;

      return {
        isHealthy,
        services: {
          repository: repositoryHealth,
          queue: queueHealth,
          audit: auditHealth,
        },
        issues,
        lastCheck: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get health status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        services: {
          repository: null,
          queue: null,
          audit: null,
        },
        issues: ['Failed to get health status'],
        lastCheck: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Estimate completion time for download operations
   */
  private estimateCompletionTime(request: SymbolDownloadRequest): Date | undefined {
    // Simple estimation based on exchange and segment
    const baseTime = 30000; // 30 seconds base time
    let multiplier = 1;

    if (request.exchange === 'ALL') {
      multiplier = 8; // All exchanges take longer
    }

    if (request.segment === 'ALL') {
      multiplier *= 2; // All segments take longer
    }

    const estimatedMs = baseTime * multiplier;
    const completion = new Date();
    completion.setTime(completion.getTime() + estimatedMs);

    return completion;
  }

  /**
   * Estimate time remaining for a job
   */
  private estimateTimeRemaining(status: any): number | undefined {
    if (status.state === 'completed' || status.state === 'failed') {
      return 0;
    }

    if (status.progress && status.progress > 0) {
      // Simple estimation based on progress
      const elapsed = status.processedAt ? Date.now() - new Date(status.processedAt).getTime() : 0;

      if (elapsed > 0) {
        const totalEstimated = (elapsed / status.progress) * 100;
        return Math.max(0, totalEstimated - elapsed);
      }
    }

    return undefined;
  }

  /**
   * Calculate overall health status
   */
  private calculateOverallHealth(repositoryStats: any, queueStats: any, auditHealth: any): string {
    if (!repositoryStats || !queueStats || !auditHealth.isHealthy) {
      return 'unhealthy';
    }

    if (queueStats.cronJobStatus !== 'active') {
      return 'degraded';
    }

    if (repositoryStats.totalRecords === 0) {
      return 'warning';
    }

    return 'healthy';
  }
}
