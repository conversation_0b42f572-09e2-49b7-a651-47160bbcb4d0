import type { TestingModule } from '@nestjs/testing';
import { Test } from '@nestjs/testing';
import { ScreenerController } from './screener.controller';
import { ScreenerService } from './screener.service';

describe('ScreenerController', () => {
  let screenerController: ScreenerController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [ScreenerController],
      providers: [ScreenerService],
    }).compile();

    screenerController = app.get<ScreenerController>(ScreenerController);
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(screenerController.getHello()).toBe('Hello World!');
    });
  });
});
