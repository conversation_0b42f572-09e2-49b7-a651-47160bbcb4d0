import { ExecutionContext, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ClsModule, ClsService } from 'nestjs-cls';
import { CoreModule } from '@app/core';

export const clsSetupHelper = (cls: ClsService, context: ExecutionContext) => {
  try {
    let session: Record<string, unknown> | null = null;

    if (context.getType() === 'rpc') {
      const rpcData = context.switchToRpc().getData();
      session = rpcData.headers?.session || null;
    }
    cls.set('session', session);
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error('Unknown error occurred');
    throw new Error(error.message);
  }
};

@Module({
  imports: [
    ClsModule.forRoot({
      global: true,
      guard: {
        mount: true,
        setup: clsSetupHelper,
      },
    }),
    CoreModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
