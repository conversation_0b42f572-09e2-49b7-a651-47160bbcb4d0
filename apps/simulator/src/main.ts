declare const module: {
  hot?: {
    accept: () => void;
    dispose: (callback: () => Promise<void>) => void;
  };
};

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { LoggerErrorInterceptor, Logger as PinoLogger } from 'nestjs-pino';
import type { MicroserviceOptions } from '@nestjs/microservices';
import { Logger } from '@nestjs/common';
import { EnvService } from '@app/core/env';
import { getRedisTransportOption } from '@app/core/transport';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { bufferLogs: true });
  app.useLogger(app.get(PinoLogger));
  app.useGlobalInterceptors(new LoggerErrorInterceptor());
  const envService = app.get(EnvService);

  app.connectMicroservice<MicroserviceOptions>(getRedisTransportOption(envService), { inheritAppConfig: true });

  const port = envService.get('PORT') || 3005;

  await app.startAllMicroservices();
  await app.listen(port);
  Logger.log({
    context: 'Bootstrap',
    msg: `🚀 Application is running on: http://localhost:${port}`,
  });
  // Gracefully shutdown the server.
  app.enableShutdownHooks();

  if (module.hot) {
    module.hot.accept();
    module.hot.dispose(() => app.close());
  }
}

process.on('exit', exitHandler.bind(null, { cleanup: true }));

//catches ctrl+c event
process.on('SIGINT', exitHandler.bind(null, { exit: true }));

// catches "kill pid" (for example: nodemon restart)
process.on('SIGUSR1', exitHandler.bind(null, { exit: true }));
process.on('SIGUSR2', exitHandler.bind(null, { exit: true }));

//catches uncaught exceptions
process.on('uncaughtException', exitHandler.bind(null, { exit: true }));
process.on('unhandledRejection', exitHandler.bind(null, { exit: true }));

function exitHandler(options: { cleanup?: boolean; exit?: boolean }, exitCode: Error | number) {
  if (options.cleanup) {
    Logger.log('exit: clean');
  }
  if (exitCode || exitCode === 0) {
    if (exitCode !== 0) {
      Logger.error(exitCode, (exitCode as Error).stack);
    } else {
      Logger.log(`exit: code - ${exitCode}`);
    }
  }
  if (options.exit) {
    process.exit();
  }
}

try {
  bootstrap().catch((err: Error) => {
    Logger.error(err, err.stack);
  });
} catch (err: unknown) {
  Logger.error(err, (err as Error).stack);
}
