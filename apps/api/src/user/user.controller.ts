import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { UserService, User } from '@app/user';
import { AuthGuard } from '@app/auth/guards/auth.guard';
import { RolesGuard } from '@app/auth/guards/roles.guard';
import { Roles } from '@app/auth/decorators/roles.decorator';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { CreateUserSchema, UpdateUserSchema, UserSearchSchema } from '@app/user';
import { createZodDto } from 'nestjs-zod';

// Create DTO classes using nestjs-zod createZodDto
class CreateUserDto extends createZodDto(CreateUserSchema) {}
class UpdateUserDto extends createZodDto(UpdateUserSchema) {}
class UserSearchDto extends createZodDto(UserSearchSchema) {}

/**
 * User controller providing CRUD operations for user management
 * Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 7.4 - Admin-only user management with validation
 */
@Controller('users')
@UseGuards(AuthGuard, RolesGuard)
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly userService: UserService) {}

  /**
   * Create a new user
   * Requirements: 1.1 - Admin can create new user accounts
   */
  @Post()
  @Roles('admin')
  async createUser(@Body() createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating new user with email: ${createUserDto.email}`);

    const user = await this.userService.createUser(createUserDto);
    this.logger.log(`Successfully created user with ID: ${user.id}`);
    return user;
  }

  /**
   * Get all users with optional filtering and pagination
   * Requirements: 1.2 - Admin can retrieve list of all users
   */
  @Get()
  @Roles('admin')
  async findAllUsers(
    @Query() searchFilters: UserSearchDto,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResult<User>> {
    this.logger.log('Retrieving users with filters and pagination');

    const paginationOptions: PaginationOptions = {
      limit: limit || 10,
      offset: offset || 0,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'DESC',
      filters: searchFilters as Record<string, unknown>,
    };

    const result = await this.userService.findUsersPaginated(paginationOptions);
    this.logger.log(`Retrieved ${result.data.length} users out of ${result.total} total`);
    return result;
  }

  /**
   * Get user by ID
   * Requirements: 1.3 - Admin can retrieve detailed user information
   */
  @Get(':id')
  @Roles('admin')
  async findUserById(@Param('id', ParseIntPipe) id: number): Promise<User> {
    this.logger.log(`Retrieving user with ID: ${id}`);

    const user = await this.userService.findUserById(id);

    if (!user) {
      this.logger.warn(`User with ID ${id} not found`);
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    this.logger.log(`Successfully retrieved user: ${user.email}`);
    return user;
  }

  /**
   * Update user information
   * Requirements: 1.4 - Admin can update user information
   */
  @Patch(':id')
  @Roles('admin')
  async updateUser(@Param('id', ParseIntPipe) id: number, @Body() updateUserDto: UpdateUserDto): Promise<User> {
    this.logger.log(`Updating user with ID: ${id}`);

    const updatedUser = await this.userService.updateUser(id, updateUserDto);
    this.logger.log(`Successfully updated user: ${updatedUser.email}`);
    return updatedUser;
  }

  /**
   * Delete user (soft delete by deactivating)
   * Requirements: 1.5 - Admin can delete/deactivate user accounts
   */
  @Delete(':id')
  @Roles('admin')
  async deleteUser(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    this.logger.log(`Deleting user with ID: ${id}`);

    await this.userService.deleteUser(id);
    this.logger.log(`Successfully deleted user with ID: ${id}`);
    return { message: `User with ID ${id} has been deactivated` };
  }
}
