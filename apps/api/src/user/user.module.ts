import { Module } from '@nestjs/common';
import { UserModule as UserLibModule } from '@app/user';
import { AuthModule } from '@app/auth';
import { UserController } from './user.controller';

/**
 * User API module that provides HTTP endpoints for user management
 * Requirements: 8.1, 8.5 - Modular architecture with proper dependency injection
 */
@Module({
  imports: [
    UserLibModule, // Import the user library module for services and repositories
    AuthModule, // Import auth module for guards and decorators
  ],
  controllers: [UserController],
  exports: [], // No exports needed as this is an API module
})
export class UserModule {}
