import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule as AuthLibModule } from '@app/auth';
import { UserModule as UserLibModule } from '@app/user';
import { AuthController } from './auth.controller';

/**
 * Auth API module that provides HTTP endpoints for authentication
 * Requirements: 5.1, 8.1, 8.5 - Authentication endpoints with proper module dependencies
 *
 * Session middleware integration is handled at the application level in main.ts
 * through SessionService.setup(app) which configures Redis-based session storage
 */
@Module({
  imports: [
    AuthLibModule, // Import the auth library module for services and guards
    UserLibModule, // Import user library module for user-related operations
  ],
  controllers: [AuthController],
  exports: [], // No exports needed as this is an API module
})
export class AuthModule {}
