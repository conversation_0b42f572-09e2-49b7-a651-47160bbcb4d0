import { <PERSON>, Post, Body, Req, Res, UseGuards, Logger, UnauthorizedException } from '@nestjs/common';
import { Request, Response } from 'express';
import {
  AuthService,
  AuthGuard,
  Public,
  LoginRequest,
  PasswordResetRequest,
  PasswordReset,
  ChangePasswordRequest,
  LoginResponse,
  LogoutResponse,
  PasswordResetRequestResponse,
  PasswordResetResponse,
  ChangePasswordResponse,
  SessionContext,
} from '@app/auth';

/**
 * Auth controller providing authentication endpoints
 * Requirements: 3.1, 3.2, 3.3, 4.1, 4.2, 4.3, 7.4 - Authentication and password management
 */
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  /**
   * User login endpoint
   * Requirements: 3.1 - User authentication with credentials and session creation
   */
  @Post('login')
  @Public()
  async login(
    @Body() loginDto: LoginRequest,
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ): Promise<LoginResponse> {
    this.logger.log(`Login attempt for email: ${loginDto.email}`);

    // Extract session context from request
    const sessionContext: SessionContext = {
      ipAddress: request.ip || request.socket?.remoteAddress,
      userAgent: request.get('User-Agent'),
      rememberMe: loginDto.rememberMe,
    };

    try {
      const loginResult = await this.authService.login(loginDto, sessionContext);

      // Set session cookie (this would typically be handled by session middleware)
      // For now, we'll set a basic session cookie
      response.cookie('sessionId', loginResult.sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: loginDto.rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000, // 30 days or 1 day
      });

      this.logger.log(`Successful login for user ID: ${loginResult.user.id}`);
      return loginResult;
    } catch (error) {
      this.logger.warn(
        `Login failed for email: ${loginDto.email} - ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * User logout endpoint
   * Requirements: 3.3 - Logout with proper session cleanup
   */
  @Post('logout')
  @UseGuards(AuthGuard)
  async logout(@Req() request: Request, @Res({ passthrough: true }) response: Response): Promise<LogoutResponse> {
    const sessionId = request.cookies?.sessionId;

    this.logger.log(`Logout attempt for session: ${sessionId}`);

    try {
      const logoutResult = await this.authService.logout(sessionId);

      // Clear session cookie
      response.clearCookie('sessionId', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      });

      this.logger.log(`Successful logout for session: ${sessionId}`);
      return logoutResult;
    } catch (error) {
      this.logger.warn(
        `Logout failed for session: ${sessionId} - ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Request password reset endpoint
   * Requirements: 4.1 - Password reset initiation with token generation
   */
  @Post('password-reset/request')
  @Public()
  async requestPasswordReset(
    @Body() resetRequestDto: PasswordResetRequest,
    @Req() request: Request,
  ): Promise<PasswordResetRequestResponse> {
    const clientIp = request.ip || request.socket?.remoteAddress;

    this.logger.log(`Password reset requested for email: ${resetRequestDto.email} from IP: ${clientIp}`);

    try {
      const result = await this.authService.requestPasswordReset(resetRequestDto.email);

      this.logger.log(`Password reset request processed for email: ${resetRequestDto.email}`);
      return result;
    } catch (error) {
      this.logger.warn(
        `Password reset request failed for email: ${resetRequestDto.email} - ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Confirm password reset endpoint
   * Requirements: 4.2, 4.3 - Password reset with token validation and password update
   */
  @Post('password-reset/confirm')
  @Public()
  async resetPassword(@Body() resetDto: PasswordReset, @Req() request: Request): Promise<PasswordResetResponse> {
    const clientIp = request.ip || request.socket?.remoteAddress;

    this.logger.log(`Password reset confirmation attempt from IP: ${clientIp}`);

    try {
      const result = await this.authService.resetPassword(resetDto);

      this.logger.log(`Password reset completed successfully`);
      return result;
    } catch (error) {
      this.logger.warn(`Password reset failed - ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Change password endpoint for authenticated users
   * Requirements: 4.4 - Password change for authenticated users
   */
  @Post('password/change')
  @UseGuards(AuthGuard)
  async changePassword(
    @Body() changePasswordDto: ChangePasswordRequest,
    @Req() request: Request,
  ): Promise<ChangePasswordResponse> {
    // Extract user ID from request (set by AuthGuard)
    const userId = (request as Request & { user?: { id: number } }).user?.id;

    if (!userId) {
      this.logger.error('User ID not found in authenticated request');
      throw new UnauthorizedException('User not authenticated');
    }

    this.logger.log(`Password change attempt for user ID: ${userId}`);

    try {
      const result = await this.authService.changePassword(userId, changePasswordDto);

      this.logger.log(`Password changed successfully for user ID: ${userId}`);
      return result;
    } catch (error) {
      this.logger.warn(
        `Password change failed for user ID: ${userId} - ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }
}
