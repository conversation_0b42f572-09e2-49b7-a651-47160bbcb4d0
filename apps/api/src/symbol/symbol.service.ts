import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { SymbolGrpcService } from '@app/symbol';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { ClientProxy } from '@nestjs/microservices';
import { SymbolQueryDto, SymbolDownloadDto } from './symbol.dto';

/**
 * Symbol Service for API Gateway
 *
 * Provides symbol operations for the API Gateway by communicating with
 * the datastore microservice. Acts as a proxy between the REST API
 * and the symbol microservice.
 *
 * Features:
 * - Symbol data retrieval and searching
 * - Download operation management
 * - Service health monitoring
 * - Error handling and retry logic
 * - Request validation and transformation
 */
@Injectable()
export class SymbolService implements OnModuleInit {
  private readonly logger = new Logger(SymbolService.name);

  constructor(
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Initialize the service and set up gRPC client
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('Initializing Symbol Service for API Gateway');

      // Wait for the datastore service to become available

      this.logger.log('Symbol Service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Symbol Service', {
        error: this.errorUtils.getErrorMessage(error),
      });
      // Don't throw here to allow the service to start even if datastore is not available
    }
  }

  // ==================== SYMBOL DATA OPERATIONS ====================

  /**
   * Get symbols with filters and pagination
   */
  async getSymbols(query: SymbolQueryDto): Promise<{
    data: any[];
    meta: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  }> {
    try {
      this.logger.log('Getting symbols from datastore service', query);

      // Transform API query to internal format
      const filters = {
        exchange: query.exchange,
        segment: query.segment,
        instrumentType: query.instrumentType,
        tradingSymbol: query.tradingSymbol,
        isActive: query.isActive,
        limit: query.limit || 100,
        offset: query.offset || 0,
      };

      const result = await this.symbolGrpcService.getSymbols(filters);

      this.logger.log('Symbols retrieved successfully', {
        count: result.data.length,
        total: result.meta.total,
      });

      return {
        data: result.data,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error('Failed to get symbols', {
        query,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get symbol by instrument token
   */
  async getSymbolByToken(instrumentToken: string): Promise<{
    data: any | null;
  }> {
    try {
      this.logger.log('Getting symbol by token from datastore service', { instrumentToken });

      const result = await this.symbolGrpcService.getSymbolByToken(instrumentToken);

      this.logger.log('Symbol retrieved successfully', {
        instrumentToken,
        found: !!result.data,
      });

      return {
        data: result.data,
      };
    } catch (error) {
      this.logger.error('Failed to get symbol by token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Search symbols by trading symbol pattern
   */
  async searchSymbols(
    searchTerm: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<{
    data: any[];
  }> {
    try {
      this.logger.log('Searching symbols in datastore service', { searchTerm, limit, offset });

      const result = await this.symbolGrpcService.searchSymbols(searchTerm, limit, offset);

      this.logger.log('Symbol search completed', {
        searchTerm,
        resultCount: result.data.length,
      });

      return {
        data: result.data,
      };
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== DOWNLOAD OPERATIONS ====================

  /**
   * Trigger symbol download
   */
  async triggerDownload(downloadDto: SymbolDownloadDto): Promise<{
    data: {
      jobId: string;
      requestId: string;
      scheduledAt: Date;
    };
    estimatedCompletion?: Date;
  }> {
    try {
      this.logger.log('Triggering symbol download in datastore service', downloadDto);

      // Transform API DTO to internal format
      const request = {
        exchange: downloadDto.exchange,
        segment: downloadDto.segment,
        forceRefresh: downloadDto.forceRefresh || false,
        batchSize: downloadDto.batchSize || 500,
        requestId: downloadDto.requestId || `api-${Date.now()}`,
        priority: downloadDto.priority || 5,
      };

      const result = await this.symbolGrpcService.triggerDownload(request);

      // Estimate completion time (simple estimation)
      const estimatedCompletion = this.estimateCompletionTime(downloadDto);

      this.logger.log('Symbol download triggered successfully', {
        jobId: result.data.jobId,
        requestId: result.data.requestId,
      });

      return {
        data: result.data,
        estimatedCompletion,
      };
    } catch (error) {
      this.logger.error('Failed to trigger symbol download', {
        downloadDto,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get download status
   */
  async getDownloadStatus(jobId: string): Promise<{
    data: any | null;
  }> {
    try {
      this.logger.log('Getting download status from datastore service', { jobId });

      const result = await this.symbolGrpcService.getDownloadStatus(jobId);

      this.logger.log('Download status retrieved successfully', {
        jobId,
        status: result.data?.status,
      });

      return {
        data: result.data,
      };
    } catch (error) {
      this.logger.error('Failed to get download status', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Trigger immediate download for all exchanges
   */
  async triggerImmediateDownload(): Promise<{
    jobId: string;
    requestId: string;
    scheduledAt: Date;
  }> {
    try {
      this.logger.log('Triggering immediate download for all exchanges');

      const request = {
        exchange: 'ALL',
        segment: 'ALL',
        forceRefresh: true,
        batchSize: 500,
        requestId: `immediate-${Date.now()}`,
        priority: 1, // Highest priority
      };

      const result = await this.symbolGrpcService.triggerDownload(request);

      this.logger.log('Immediate download triggered successfully', {
        jobId: result.data.jobId,
        requestId: result.data.requestId,
      });

      return {
        jobId: result.data.jobId,
        requestId: result.data.requestId,
        scheduledAt: result.data.scheduledAt,
      };
    } catch (error) {
      this.logger.error('Failed to trigger immediate download', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== HEALTH AND STATISTICS ====================

  /**
   * Get service health status
   */
  async getHealth(): Promise<{
    data: any;
  }> {
    try {
      this.logger.log('Getting health status from datastore service');

      const result = await this.symbolGrpcService.getHealth();

      return {
        data: result.data,
      };
    } catch (error) {
      this.logger.error('Failed to get health status', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get service statistics
   */
  async getStats(): Promise<{
    data: any;
  }> {
    try {
      this.logger.log('Getting statistics from datastore service');

      const result = await this.symbolGrpcService.getStats();

      return {
        data: result.data,
      };
    } catch (error) {
      this.logger.error('Failed to get statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Check if the datastore service is available
   */
  async isServiceAvailable(): Promise<boolean> {
    try {
      return await this.symbolGrpcService.isServiceAvailable();
    } catch (error) {
      this.logger.warn('Failed to check service availability', {
        error: this.errorUtils.getErrorMessage(error),
      });
      return false;
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Estimate completion time for download operations
   */
  private estimateCompletionTime(downloadDto: SymbolDownloadDto): Date | undefined {
    // Simple estimation based on exchange and segment
    const baseTime = 30000; // 30 seconds base time
    let multiplier = 1;

    if (downloadDto.exchange === 'ALL') {
      multiplier = 8; // All exchanges take longer
    }

    if (downloadDto.segment === 'ALL') {
      multiplier *= 2; // All segments take longer
    }

    const estimatedMs = baseTime * multiplier;
    const completion = new Date();
    completion.setTime(completion.getTime() + estimatedMs);

    return completion;
  }
}
