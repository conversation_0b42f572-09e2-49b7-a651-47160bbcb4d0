import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Logger,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { SymbolService } from './symbol.service';
import {
  SymbolQueryDto,
  SymbolDownloadDto,
  SymbolSearchDto,
  SymbolResponseDto,
  SymbolDownloadResponseDto,
  SymbolStatusResponseDto,
} from './symbol.dto';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { AuthGuard } from '@app/auth';

/**
 * Symbol Controller for API Gateway
 *
 * Provides REST API endpoints for symbol master data operations.
 * Acts as the gateway between external clients and the datastore microservice.
 *
 * Features:
 * - Symbol data querying and searching
 * - Symbol download triggering and monitoring
 * - Job status tracking
 * - Authentication and authorization
 * - Comprehensive error handling
 * - API documentation with Swagger
 */
@Controller('symbols')
@UseGuards(AuthGuard)
export class SymbolController {
  private readonly logger = new Logger(SymbolController.name);

  constructor(
    private readonly symbolService: SymbolService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== SYMBOL DATA ENDPOINTS ====================

  /**
   * Get symbols with filters and pagination
   */
  @Get()
  async getSymbols(@Query() query: SymbolQueryDto): Promise<SymbolResponseDto> {
    try {
      this.logger.log('Getting symbols with filters', query);

      const result = await this.symbolService.getSymbols(query);

      return {
        success: true,
        data: result.data,
        meta: result.meta,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbols', {
        query,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get symbol by instrument token
   */
  @Get(':instrumentToken')
  async getSymbolByToken(@Param('instrumentToken') instrumentToken: string) {
    try {
      this.logger.log('Getting symbol by token', { instrumentToken });

      const symbol = await this.symbolService.getSymbolByToken(instrumentToken);

      if (!symbol.data) {
        throw new HttpException(
          {
            success: false,
            error: 'Symbol not found',
            timestamp: this.dateTimeUtils.getUtcNow(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: symbol.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('Failed to get symbol by token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Search symbols by trading symbol pattern
   */
  @Get('search/:searchTerm')
  async searchSymbols(@Param('searchTerm') searchTerm: string, @Query() query: SymbolSearchDto) {
    try {
      this.logger.log('Searching symbols', { searchTerm, ...query });

      const result = await this.symbolService.searchSymbols(searchTerm, query.limit || 50, query.offset || 0);

      return {
        success: true,
        data: result.data,
        meta: {
          searchTerm,
          limit: query.limit || 50,
          offset: query.offset || 0,
          count: result.data.length,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        query,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== DOWNLOAD OPERATIONS ====================

  /**
   * Trigger symbol download
   */
  @Post('download')
  async triggerDownload(@Body() downloadDto: SymbolDownloadDto): Promise<SymbolDownloadResponseDto> {
    try {
      this.logger.log('Triggering symbol download', downloadDto);

      const result = await this.symbolService.triggerDownload(downloadDto);

      return {
        success: true,
        data: {
          jobId: result.data.jobId,
          requestId: result.data.requestId,
          scheduledAt: result.data.scheduledAt,
          estimatedCompletion: result.estimatedCompletion,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger symbol download', {
        downloadDto,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get download job status
   */
  @Get('download/status/:jobId')
  async getDownloadStatus(@Param('jobId') jobId: string): Promise<SymbolStatusResponseDto> {
    try {
      this.logger.log('Getting download status', { jobId });

      const status = await this.symbolService.getDownloadStatus(jobId);

      if (!status.data) {
        throw new HttpException(
          {
            success: false,
            error: 'Job not found',
            timestamp: this.dateTimeUtils.getUtcNow(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: status.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('Failed to get download status', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Trigger immediate download for all exchanges
   */
  @Post('download/immediate')
  async triggerImmediateDownload() {
    try {
      this.logger.log('Triggering immediate download for all exchanges');

      const result = await this.symbolService.triggerImmediateDownload();

      return {
        success: true,
        data: {
          jobId: result.jobId,
          requestId: result.requestId,
          scheduledAt: result.scheduledAt,
          priority: 'HIGH',
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger immediate download', {
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== STATISTICS AND HEALTH ====================

  /**
   * Get symbol service statistics
   */
  @Get('stats')
  async getStats() {
    try {
      this.logger.log('Getting symbol service statistics');

      const stats = await this.symbolService.getStats();

      return {
        success: true,
        data: stats.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbol statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get symbol service health
   */
  @Get('health')
  async getHealth() {
    try {
      this.logger.log('Getting symbol service health');

      const health = await this.symbolService.getHealth();

      return {
        success: true,
        data: health.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbol service health', {
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
