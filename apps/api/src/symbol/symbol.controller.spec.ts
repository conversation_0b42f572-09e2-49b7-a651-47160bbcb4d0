import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { SymbolController } from './symbol.controller';
import { SymbolService } from './symbol.service';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { SymbolQueryDto, SymbolDownloadDto, SymbolSearchDto } from './symbol.dto';

describe('SymbolController', () => {
  let controller: SymbolController;
  let mockSymbolService: jest.Mocked<SymbolService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockErrorUtils: jest.Mocked<ErrorUtilsService>;

  const mockSymbol = {
    instrumentToken: '738561',
    exchangeToken: '2885',
    tradingSymbol: 'RELIANCE',
    name: 'Reliance Industries Limited',
    lastPrice: 2450.50,
    expiry: undefined,
    strike: undefined,
    tickSize: 0.05,
    lotSize: 1,
    instrumentType: 'EQ',
    segment: 'NSE',
    exchange: 'NSE',
    isActive: true,
    downloadedAt: new Date('2024-01-15T08:00:00Z'),
    updatedAt: new Date('2024-01-15T08:00:00Z'),
  };

  const mockSymbolsResponse = {
    data: [mockSymbol],
    meta: {
      total: 1,
      limit: 100,
      offset: 0,
      hasMore: false,
    },
  };

  beforeEach(async () => {
    mockSymbolService = {
      getSymbols: jest.fn(),
      getSymbolByToken: jest.fn(),
      searchSymbols: jest.fn(),
      triggerDownload: jest.fn(),
      getDownloadStatus: jest.fn(),
      triggerImmediateDownload: jest.fn(),
      getStats: jest.fn(),
      getHealth: jest.fn(),
      isServiceAvailable: jest.fn(),
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(() => new Date('2024-01-15T10:00:00Z')),
      getTime: jest.fn(() => Date.now()),
    } as any;

    mockErrorUtils = {
      getErrorMessage: jest.fn((error) => error instanceof Error ? error.message : String(error)),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SymbolController],
      providers: [
        { provide: SymbolService, useValue: mockSymbolService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
      ],
    }).compile();

    controller = module.get<SymbolController>(SymbolController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getSymbols', () => {
    it('should return symbols with filters', async () => {
      const query: SymbolQueryDto = {
        exchange: 'NSE',
        segment: 'NSE',
        limit: 10,
        offset: 0,
      };

      mockSymbolService.getSymbols.mockResolvedValue(mockSymbolsResponse);

      const result = await controller.getSymbols(query);

      expect(result).toMatchObject({
        success: true,
        data: [mockSymbol],
        meta: mockSymbolsResponse.meta,
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.getSymbols).toHaveBeenCalledWith(query);
    });

    it('should handle empty results', async () => {
      const query: SymbolQueryDto = { limit: 10, offset: 0 };
      const emptyResponse = {
        data: [],
        meta: { total: 0, limit: 10, offset: 0, hasMore: false },
      };

      mockSymbolService.getSymbols.mockResolvedValue(emptyResponse);

      const result = await controller.getSymbols(query);

      expect(result).toMatchObject({
        success: true,
        data: [],
        meta: emptyResponse.meta,
      });
    });

    it('should handle service errors', async () => {
      const query: SymbolQueryDto = { limit: 10, offset: 0 };
      mockSymbolService.getSymbols.mockRejectedValue(new Error('Service error'));

      await expect(controller.getSymbols(query)).rejects.toThrow(HttpException);

      try {
        await controller.getSymbols(query);
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
        expect(error.getResponse()).toMatchObject({
          success: false,
          error: 'Service error',
          timestamp: expect.any(Date),
        });
      }
    });
  });

  describe('getSymbolByToken', () => {
    it('should return symbol by instrument token', async () => {
      mockSymbolService.getSymbolByToken.mockResolvedValue({ data: mockSymbol });

      const result = await controller.getSymbolByToken('738561');

      expect(result).toMatchObject({
        success: true,
        data: mockSymbol,
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.getSymbolByToken).toHaveBeenCalledWith('738561');
    });

    it('should return 404 when symbol not found', async () => {
      mockSymbolService.getSymbolByToken.mockResolvedValue({ data: null });

      await expect(controller.getSymbolByToken('999999')).rejects.toThrow(HttpException);

      try {
        await controller.getSymbolByToken('999999');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.NOT_FOUND);
        expect(error.getResponse()).toMatchObject({
          success: false,
          error: 'Symbol not found',
        });
      }
    });

    it('should handle service errors', async () => {
      mockSymbolService.getSymbolByToken.mockRejectedValue(new Error('Service error'));

      await expect(controller.getSymbolByToken('738561')).rejects.toThrow(HttpException);
    });
  });

  describe('searchSymbols', () => {
    it('should search symbols by trading symbol', async () => {
      const query: SymbolSearchDto = { limit: 10, offset: 0 };
      mockSymbolService.searchSymbols.mockResolvedValue({ data: [mockSymbol] });

      const result = await controller.searchSymbols('RELIANCE', query);

      expect(result).toMatchObject({
        success: true,
        data: [mockSymbol],
        meta: {
          searchTerm: 'RELIANCE',
          limit: 10,
          offset: 0,
          count: 1,
        },
      });

      expect(mockSymbolService.searchSymbols).toHaveBeenCalledWith('RELIANCE', 10, 0);
    });

    it('should use default values when query params not provided', async () => {
      const query: SymbolSearchDto = {};
      mockSymbolService.searchSymbols.mockResolvedValue({ data: [] });

      await controller.searchSymbols('TEST', query);

      expect(mockSymbolService.searchSymbols).toHaveBeenCalledWith('TEST', 50, 0);
    });

    it('should handle search errors', async () => {
      mockSymbolService.searchSymbols.mockRejectedValue(new Error('Search error'));

      await expect(controller.searchSymbols('RELIANCE', {})).rejects.toThrow(HttpException);
    });
  });

  describe('triggerDownload', () => {
    it('should trigger symbol download', async () => {
      const downloadDto: SymbolDownloadDto = {
        exchange: 'NSE',
        segment: 'NSE',
        forceRefresh: false,
        batchSize: 500,
        requestId: 'test-request',
        priority: 5,
      };

      const mockDownloadResponse = {
        data: {
          jobId: 'job-123',
          requestId: 'test-request',
          scheduledAt: new Date('2024-01-15T10:00:00Z'),
        },
        estimatedCompletion: new Date('2024-01-15T10:05:00Z'),
      };

      mockSymbolService.triggerDownload.mockResolvedValue(mockDownloadResponse);

      const result = await controller.triggerDownload(downloadDto);

      expect(result).toMatchObject({
        success: true,
        data: {
          jobId: 'job-123',
          requestId: 'test-request',
          scheduledAt: expect.any(Date),
          estimatedCompletion: expect.any(Date),
        },
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.triggerDownload).toHaveBeenCalledWith(downloadDto);
    });

    it('should handle download trigger errors', async () => {
      const downloadDto: SymbolDownloadDto = {
        exchange: 'NSE',
        segment: 'NSE',
      };

      mockSymbolService.triggerDownload.mockRejectedValue(new Error('Download error'));

      await expect(controller.triggerDownload(downloadDto)).rejects.toThrow(HttpException);
    });
  });

  describe('getDownloadStatus', () => {
    it('should return download status', async () => {
      const mockStatus = {
        id: 'job-123',
        state: 'completed',
        progress: 100,
        attempts: 1,
        maxAttempts: 3,
        createdAt: new Date('2024-01-15T10:00:00Z'),
        finishedAt: new Date('2024-01-15T10:05:00Z'),
      };

      mockSymbolService.getDownloadStatus.mockResolvedValue({ data: mockStatus });

      const result = await controller.getDownloadStatus('job-123');

      expect(result).toMatchObject({
        success: true,
        data: mockStatus,
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.getDownloadStatus).toHaveBeenCalledWith('job-123');
    });

    it('should return 404 when job not found', async () => {
      mockSymbolService.getDownloadStatus.mockResolvedValue({ data: null });

      await expect(controller.getDownloadStatus('non-existent')).rejects.toThrow(HttpException);

      try {
        await controller.getDownloadStatus('non-existent');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.NOT_FOUND);
        expect(error.getResponse()).toMatchObject({
          success: false,
          error: 'Job not found',
        });
      }
    });

    it('should handle status check errors', async () => {
      mockSymbolService.getDownloadStatus.mockRejectedValue(new Error('Status error'));

      await expect(controller.getDownloadStatus('job-123')).rejects.toThrow(HttpException);
    });
  });

  describe('triggerImmediateDownload', () => {
    it('should trigger immediate download', async () => {
      const mockResponse = {
        jobId: 'job-456',
        requestId: 'immediate-123',
        scheduledAt: new Date('2024-01-15T10:00:00Z'),
      };

      mockSymbolService.triggerImmediateDownload.mockResolvedValue(mockResponse);

      const result = await controller.triggerImmediateDownload();

      expect(result).toMatchObject({
        success: true,
        data: {
          jobId: 'job-456',
          requestId: 'immediate-123',
          scheduledAt: expect.any(Date),
          priority: 'HIGH',
        },
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.triggerImmediateDownload).toHaveBeenCalled();
    });

    it('should handle immediate download errors', async () => {
      mockSymbolService.triggerImmediateDownload.mockRejectedValue(new Error('Immediate download error'));

      await expect(controller.triggerImmediateDownload()).rejects.toThrow(HttpException);
    });
  });

  describe('getStats', () => {
    it('should return symbol service statistics', async () => {
      const mockStats = {
        data: {
          totalSymbols: 1000,
          activeSymbols: 950,
          lastUpdate: new Date('2024-01-15T08:00:00Z'),
        },
      };

      mockSymbolService.getStats.mockResolvedValue(mockStats);

      const result = await controller.getStats();

      expect(result).toMatchObject({
        success: true,
        data: mockStats.data,
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.getStats).toHaveBeenCalled();
    });

    it('should handle stats errors', async () => {
      mockSymbolService.getStats.mockRejectedValue(new Error('Stats error'));

      await expect(controller.getStats()).rejects.toThrow(HttpException);
    });
  });

  describe('getHealth', () => {
    it('should return symbol service health', async () => {
      const mockHealth = {
        data: {
          isHealthy: true,
          services: {
            repository: { isHealthy: true },
            queue: { isHealthy: true },
            audit: { isHealthy: true },
          },
        },
      };

      mockSymbolService.getHealth.mockResolvedValue(mockHealth);

      const result = await controller.getHealth();

      expect(result).toMatchObject({
        success: true,
        data: mockHealth.data,
        timestamp: expect.any(Date),
      });

      expect(mockSymbolService.getHealth).toHaveBeenCalled();
    });

    it('should handle health check errors', async () => {
      mockSymbolService.getHealth.mockRejectedValue(new Error('Health check error'));

      await expect(controller.getHealth()).rejects.toThrow(HttpException);
    });
  });

  describe('error handling', () => {
    it('should format errors consistently', async () => {
      const error = new Error('Test error');
      mockSymbolService.getSymbols.mockRejectedValue(error);

      try {
        await controller.getSymbols({});
      } catch (httpError) {
        expect(httpError).toBeInstanceOf(HttpException);
        expect(httpError.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
        expect(httpError.getResponse()).toMatchObject({
          success: false,
          error: 'Test error',
          timestamp: expect.any(Date),
        });
      }

      expect(mockErrorUtils.getErrorMessage).toHaveBeenCalledWith(error);
    });

    it('should preserve HTTP exceptions', async () => {
      const httpError = new HttpException('Custom error', HttpStatus.BAD_REQUEST);
      mockSymbolService.getSymbolByToken.mockRejectedValue(httpError);

      try {
        await controller.getSymbolByToken('738561');
      } catch (error) {
        expect(error).toBe(httpError);
        expect(error.getStatus()).toBe(HttpStatus.BAD_REQUEST);
      }
    });

    it('should log errors appropriately', async () => {
      const logSpy = jest.spyOn(controller['logger'], 'error');
      mockSymbolService.getSymbols.mockRejectedValue(new Error('Service error'));

      try {
        await controller.getSymbols({ exchange: 'NSE' });
      } catch (error) {
        // Expected to throw
      }

      expect(logSpy).toHaveBeenCalledWith(
        'Failed to get symbols',
        expect.objectContaining({
          query: { exchange: 'NSE' },
          error: 'Service error',
        })
      );
    });
  });

  describe('input validation', () => {
    it('should handle various query parameter types', async () => {
      const queries = [
        { exchange: 'NSE', isActive: true },
        { limit: 50, offset: 10 },
        { tradingSymbol: 'RELIANCE' },
        {}, // Empty query
      ];

      mockSymbolService.getSymbols.mockResolvedValue(mockSymbolsResponse);

      for (const query of queries) {
        const result = await controller.getSymbols(query);
        expect(result.success).toBe(true);
      }
    });

    it('should handle download DTO variations', async () => {
      const downloadDtos = [
        { exchange: 'NSE', segment: 'NSE' },
        { exchange: 'ALL', segment: 'ALL', forceRefresh: true },
        { exchange: 'BSE', segment: 'BSE', batchSize: 1000, priority: 1 },
      ];

      const mockResponse = {
        data: {
          jobId: 'job-123',
          requestId: 'test',
          scheduledAt: new Date(),
        },
      };

      mockSymbolService.triggerDownload.mockResolvedValue(mockResponse);

      for (const dto of downloadDtos) {
        const result = await controller.triggerDownload(dto);
        expect(result.success).toBe(true);
      }
    });
  });
});
