import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsNumber, IsEnum, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Symbol Query DTO for filtering and pagination
 */
export class SymbolQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by exchange',
    enum: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
    example: 'NSE',
  })
  @IsOptional()
  @IsEnum(['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'])
  exchange?: string;

  @ApiPropertyOptional({
    description: 'Filter by segment',
    enum: ['NSE', 'BSE', 'NFO-FUT', 'NFO-OPT', 'BFO-FUT', 'BFO-OPT', 'CDS', 'MCX'],
    example: 'NSE',
  })
  @IsOptional()
  @IsEnum(['NSE', 'BSE', 'NFO-FUT', 'NFO-OPT', 'BFO-FUT', 'BFO-OPT', 'CDS', 'MCX'])
  segment?: string;

  @ApiPropertyOptional({
    description: 'Filter by instrument type',
    enum: ['EQ', 'FUT', 'CE', 'PE', 'INDEX'],
    example: 'EQ',
  })
  @IsOptional()
  @IsEnum(['EQ', 'FUT', 'CE', 'PE', 'INDEX'])
  instrumentType?: string;

  @ApiPropertyOptional({
    description: 'Filter by trading symbol pattern',
    example: 'RELIANCE',
  })
  @IsOptional()
  @IsString()
  tradingSymbol?: string;

  @ApiPropertyOptional({
    description: 'Filter by active status',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Maximum results to return',
    minimum: 1,
    maximum: 1000,
    default: 100,
    example: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Number of results to skip',
    minimum: 0,
    default: 0,
    example: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number;
}

/**
 * Symbol Search DTO for search operations
 */
export class SymbolSearchDto {
  @ApiPropertyOptional({
    description: 'Maximum results to return',
    minimum: 1,
    maximum: 100,
    default: 50,
    example: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Number of results to skip',
    minimum: 0,
    default: 0,
    example: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number;
}

/**
 * Symbol Download DTO for triggering downloads
 */
export class SymbolDownloadDto {
  @ApiProperty({
    description: 'Exchange to download symbols from',
    enum: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX', 'ALL'],
    default: 'ALL',
    example: 'NSE',
  })
  @IsEnum(['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX', 'ALL'])
  exchange: string = 'ALL';

  @ApiProperty({
    description: 'Segment to download symbols from',
    enum: ['NSE', 'BSE', 'NFO-FUT', 'NFO-OPT', 'BFO-FUT', 'BFO-OPT', 'CDS', 'MCX', 'ALL'],
    default: 'ALL',
    example: 'NSE',
  })
  @IsEnum(['NSE', 'BSE', 'NFO-FUT', 'NFO-OPT', 'BFO-FUT', 'BFO-OPT', 'CDS', 'MCX', 'ALL'])
  segment: string = 'ALL';

  @ApiPropertyOptional({
    description: 'Force refresh even if recently downloaded',
    default: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  forceRefresh?: boolean = false;

  @ApiPropertyOptional({
    description: 'Batch size for processing',
    minimum: 1,
    maximum: 1000,
    default: 500,
    example: 500,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(1000)
  batchSize?: number = 500;

  @ApiPropertyOptional({
    description: 'Optional request identifier for tracking',
    example: 'manual-download-123',
  })
  @IsOptional()
  @IsString()
  requestId?: string;

  @ApiPropertyOptional({
    description: 'Job priority (1=highest, 10=lowest)',
    minimum: 1,
    maximum: 10,
    default: 5,
    example: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number = 5;
}

/**
 * Symbol Master Data DTO for responses
 */
export class SymbolMasterDto {
  @ApiProperty({ description: 'Kite instrument token', example: '738561' })
  instrumentToken: string;

  @ApiProperty({ description: 'Exchange token', example: '2885' })
  exchangeToken: string;

  @ApiProperty({ description: 'Trading symbol', example: 'RELIANCE' })
  tradingSymbol: string;

  @ApiProperty({ description: 'Instrument name', example: 'Reliance Industries Limited' })
  name: string;

  @ApiProperty({ description: 'Last traded price', example: 2450.5 })
  lastPrice: number;

  @ApiPropertyOptional({ description: 'Expiry date for derivatives', example: '2024-03-28' })
  expiry?: Date;

  @ApiPropertyOptional({ description: 'Strike price for options', example: 2500 })
  strike?: number;

  @ApiProperty({ description: 'Minimum price movement', example: 0.05 })
  tickSize: number;

  @ApiProperty({ description: 'Lot size', example: 1 })
  lotSize: number;

  @ApiProperty({ description: 'Instrument type', enum: ['EQ', 'FUT', 'CE', 'PE', 'INDEX'], example: 'EQ' })
  instrumentType: string;

  @ApiProperty({ description: 'Market segment', example: 'NSE' })
  segment: string;

  @ApiProperty({ description: 'Exchange', example: 'NSE' })
  exchange: string;

  @ApiProperty({ description: 'Whether instrument is active', example: true })
  isActive: boolean;

  @ApiProperty({ description: 'When this data was downloaded', example: '2024-01-15T08:00:00Z' })
  downloadedAt: Date;

  @ApiProperty({ description: 'Last update timestamp', example: '2024-01-15T08:00:00Z' })
  updatedAt: Date;
}

/**
 * Symbol Response DTO for paginated symbol data
 */
export class SymbolResponseDto {
  @ApiProperty({ description: 'Request success status', example: true })
  success: boolean;

  @ApiProperty({ description: 'Array of symbol data', type: [SymbolMasterDto] })
  data: SymbolMasterDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    example: {
      total: 1000,
      limit: 100,
      offset: 0,
      hasMore: true,
    },
  })
  meta: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };

  @ApiProperty({ description: 'Response timestamp', example: '2024-01-15T10:30:00Z' })
  timestamp: Date;
}

/**
 * Symbol Download Response DTO
 */
export class SymbolDownloadResponseDto {
  @ApiProperty({ description: 'Request success status', example: true })
  success: boolean;

  @ApiProperty({
    description: 'Download job information',
    example: {
      jobId: 'job_123456',
      requestId: 'req_789012',
      scheduledAt: '2024-01-15T10:30:00Z',
      estimatedCompletion: '2024-01-15T10:35:00Z',
    },
  })
  data: {
    jobId: string;
    requestId: string;
    scheduledAt: Date;
    estimatedCompletion?: Date;
  };

  @ApiProperty({ description: 'Response timestamp', example: '2024-01-15T10:30:00Z' })
  timestamp: Date;
}

/**
 * Symbol Status Response DTO
 */
export class SymbolStatusResponseDto {
  @ApiProperty({ description: 'Request success status', example: true })
  success: boolean;

  @ApiProperty({
    description: 'Job status information',
    example: {
      id: 'job_123456',
      state: 'completed',
      progress: 100,
      attempts: 1,
      createdAt: '2024-01-15T10:30:00Z',
      processedAt: '2024-01-15T10:31:00Z',
      finishedAt: '2024-01-15T10:35:00Z',
    },
  })
  data: {
    id: string;
    name?: string;
    state: string;
    progress: number;
    attempts: number;
    maxAttempts: number;
    createdAt?: Date;
    processedAt?: Date;
    finishedAt?: Date;
    failedReason?: string;
    result?: any;
  };

  @ApiProperty({ description: 'Response timestamp', example: '2024-01-15T10:30:00Z' })
  timestamp: Date;
}

/**
 * Error Response DTO
 */
export class ErrorResponseDto {
  @ApiProperty({ description: 'Request success status', example: false })
  success: boolean;

  @ApiProperty({ description: 'Error message', example: 'Symbol not found' })
  error: string;

  @ApiProperty({ description: 'Response timestamp', example: '2024-01-15T10:30:00Z' })
  timestamp: Date;
}
